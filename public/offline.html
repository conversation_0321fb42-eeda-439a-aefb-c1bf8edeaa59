<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - SettingWala</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #ec4899, #f472b6, #fbbf24);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
        }
        
        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 3rem;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📱
        </div>
        
        <h1>You're Offline</h1>
        
        <p>
            It looks like you've lost your internet connection. Don't worry, 
            SettingWala will work again once you're back online!
        </p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💝</div>
                <span>Your profile data is safely stored</span>
            </div>
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <span>Changes will sync when you're back online</span>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <span>Fast loading when connection returns</span>
            </div>
        </div>
    </div>

    <script>
        // Auto-retry when connection is restored
        window.addEventListener('online', function() {
            window.location.reload();
        });
        
        // Check connection status
        function checkConnection() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        // Check every 5 seconds
        setInterval(checkConnection, 5000);
    </script>
</body>
</html>
