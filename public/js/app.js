/**
 * SettingWala Dating App - Minimal JavaScript
 * No animations, no design effects - only essential functionality
 */

/**
 * Connection Check
 */
function checkConnection() {
    // Simple connection check to ensure server is reachable
    if (navigator.onLine) {
        fetch(window.location.origin + '/manifest.json', {
            method: 'HEAD',
            cache: 'no-cache'
        }).catch(() => {
            // Connection failed - handle silently
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check connection first
    checkConnection();

    // Initialize performance optimizations
    initializePerformanceOptimizations();

    // Initialize notification badge
    initializeNotificationBadge();
});

/**
 * Performance Optimizations
 */
function initializePerformanceOptimizations() {
    // Lazy load images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // Debounce scroll events
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(() => {
            // Handle scroll events here
            updateScrollProgress();
        }, 16); // ~60fps
    });
}

/**
 * Scroll Progress Indicator
 */
function updateScrollProgress() {
    const scrollProgress = document.querySelector('.scroll-progress');
    if (scrollProgress) {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';
    }
}

/**
 * Form Utilities
 */
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            isValid = false;
            input.classList.add('error');
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

/**
 * Utility Functions
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

/**
 * Notification Badge Functionality
 */
function initializeNotificationBadge() {
    const notificationBadge = document.getElementById('notification-badge');
    const mobileNotificationBadge = document.getElementById('mobile-notification-badge');

    if (!notificationBadge && !mobileNotificationBadge) {
        return;
    }

    // Fetch initial notification count immediately
    updateNotificationBadge();

    // Update notification count every 30 seconds
    setInterval(updateNotificationBadge, 30000);

    // Also update when user clicks on notification link
    const notificationLinks = document.querySelectorAll('a[href*="notifications"]');
    notificationLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Clear badges when user clicks notification link
            setTimeout(() => {
                if (notificationBadge) {
                    notificationBadge.classList.remove('show', 'has-notifications');
                    notificationBadge.style.display = 'none';
                }
                if (mobileNotificationBadge) {
                    mobileNotificationBadge.classList.remove('show', 'has-notifications');
                    mobileNotificationBadge.style.display = 'none';
                }
            }, 1000);
        });
    });
}

function updateNotificationBadge() {
    const notificationBadge = document.getElementById('notification-badge');
    const mobileNotificationBadge = document.getElementById('mobile-notification-badge');

    if (!notificationBadge && !mobileNotificationBadge) {
        return;
    }

    fetch('/notifications/unread-count', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Only show badge if there are unread notifications (count > 0)
            const showBadge = data.has_unread && data.count > 0;
            const displayCount = data.formatted_count || data.count.toString();

            // Update desktop notification badge with force
            if (notificationBadge) {
                if (showBadge) {
                    notificationBadge.textContent = displayCount;
                    notificationBadge.classList.add('show', 'has-notifications');
                    // Force display with multiple approaches
                    notificationBadge.style.setProperty('display', 'flex', 'important');
                    notificationBadge.style.cssText += 'display: flex !important;';

                } else {
                    notificationBadge.classList.remove('show', 'has-notifications');
                    notificationBadge.style.setProperty('display', 'none', 'important');

                }
            }

            // Update mobile notification badge with force
            if (mobileNotificationBadge) {
                if (showBadge) {
                    mobileNotificationBadge.textContent = displayCount;
                    mobileNotificationBadge.classList.add('show', 'has-notifications');
                    // Force display with multiple approaches
                    mobileNotificationBadge.style.setProperty('display', 'flex', 'important');
                    mobileNotificationBadge.style.cssText += 'display: flex !important;';

                } else {
                    mobileNotificationBadge.classList.remove('show', 'has-notifications');
                    mobileNotificationBadge.style.setProperty('display', 'none', 'important');

                }
            }
        } else {

        }
    })
    .catch(error => {
        // Silently handle errors
    });
}

/**
 * Test function to manually show notification badge
 */
function testNotificationBadge(count = 5) {
    const notificationBadge = document.getElementById('notification-badge');
    const mobileNotificationBadge = document.getElementById('mobile-notification-badge');

    // Only show badge if count > 0 (realistic behavior)
    if (count > 0) {
        const displayCount = count > 9 ? '9+' : count.toString();

        if (notificationBadge) {
            notificationBadge.textContent = displayCount;
            notificationBadge.classList.add('show', 'has-notifications');
            // Force display with multiple approaches
            notificationBadge.style.setProperty('display', 'flex', 'important');
            notificationBadge.style.cssText += 'display: flex !important;';
        }

        if (mobileNotificationBadge) {
            mobileNotificationBadge.textContent = displayCount;
            mobileNotificationBadge.classList.add('show', 'has-notifications');
            // Force display with multiple approaches
            mobileNotificationBadge.style.setProperty('display', 'flex', 'important');
            mobileNotificationBadge.style.cssText += 'display: flex !important;';
        }
    } else {
        // Hide badge if count is 0
        hideNotificationBadges();
    }
}

/**
 * Function to hide notification badges
 */
function hideNotificationBadges() {
    const notificationBadge = document.getElementById('notification-badge');
    const mobileNotificationBadge = document.getElementById('mobile-notification-badge');

    if (notificationBadge) {
        notificationBadge.classList.remove('show', 'has-notifications');
        notificationBadge.style.setProperty('display', 'none', 'important');
    }

    if (mobileNotificationBadge) {
        mobileNotificationBadge.classList.remove('show', 'has-notifications');
        mobileNotificationBadge.style.setProperty('display', 'none', 'important');
    }


}

// Export functions for global use
window.validateForm = validateForm;
window.debounce = debounce;
window.throttle = throttle;
window.updateNotificationBadge = updateNotificationBadge;
window.testNotificationBadge = testNotificationBadge;
window.hideNotificationBadges = hideNotificationBadges;
