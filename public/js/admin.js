// Minimal Admin Panel JavaScript
// This file contains only essential functionality to prevent white screen issues

// Simple admin panel class
class AdminPanel {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.apiUrl = '/admin/api';
        console.log('AdminPanel initialized with API URL:', this.apiUrl);
        this.setupUserActions();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('/users')) return 'users';
        if (path.includes('/management-users')) return 'management-users';
        if (path.includes('/meeting-events')) return 'meeting-events';
        if (path.includes('/notifications')) return 'notifications';
        if (path.includes('/withdrawals')) return 'withdrawals';
        if (path.includes('/settings')) return 'settings';
        return 'dashboard';
    }

    // Setup user action buttons
    setupUserActions() {
        document.addEventListener('click', async (e) => {
            if (e.target.closest('.btn-suspend-user')) {
                e.preventDefault();
                const button = e.target.closest('.btn-suspend-user');
                const userId = button.dataset.userId;
                await this.suspendUser(userId);
            }

            if (e.target.closest('.btn-activate-user')) {
                e.preventDefault();
                const button = e.target.closest('.btn-activate-user');
                const userId = button.dataset.userId;
                await this.activateUser(userId);
            }

            if (e.target.closest('.btn-view-user')) {
                e.preventDefault();
                const button = e.target.closest('.btn-view-user');
                const userId = button.dataset.userId;
                await this.showUserDetails(userId);
            }

            if (e.target.closest('.btn-toggle-event-status')) {
                e.preventDefault();
                const button = e.target.closest('.btn-toggle-event-status');
                const eventId = button.dataset.eventId;
                await this.toggleEventStatus(eventId);
            }

            if (e.target.closest('.btn-delete-event')) {
                e.preventDefault();
                const button = e.target.closest('.btn-delete-event');
                const eventId = button.dataset.eventId;
                await this.deleteEvent(eventId);
            }
        });
    }

    // Suspend user
    async suspendUser(userId) {
        const button = document.querySelector(`.btn-suspend-user[data-user-id="${userId}"]`);
        const originalText = button.innerHTML;

        try {
            // Show loading state on button
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            console.log('Suspending user:', userId, 'API URL:', `${this.apiUrl}/users/${userId}/suspend`);
            const response = await fetch(`${this.apiUrl}/users/${userId}/suspend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('User suspended successfully');
                this.refreshPage();
            } else {
                this.showError(data.message || 'Failed to suspend user');
            }
        } catch (error) {
            this.showError('Failed to suspend user');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    // Activate user
    async activateUser(userId) {
        const button = document.querySelector(`.btn-activate-user[data-user-id="${userId}"]`);
        const originalText = button.innerHTML;

        try {
            // Show loading state on button
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            const response = await fetch(`${this.apiUrl}/users/${userId}/activate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('User activated successfully');
                this.refreshPage();
            } else {
                this.showError(data.message || 'Failed to activate user');
            }
        } catch (error) {
            this.showError('Failed to activate user');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    // Show user details
    async showUserDetails(userId) {
        try {
            // Show modal with loading state
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            modal.show();

            // Reset modal content to loading state
            document.getElementById('userDetailsContent').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading user details...</p>
                </div>
            `;

            const response = await fetch(`${this.apiUrl}/users/${userId}/details`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.renderUserDetails(data.user);
            } else {
                this.showError(data.message || 'Failed to load user details');
                modal.hide();
            }
        } catch (error) {
            this.showError('Failed to load user details');
            const modal = bootstrap.Modal.getInstance(document.getElementById('userDetailsModal'));
            if (modal) modal.hide();
        }
    }

    // Render user details in modal
    renderUserDetails(user) {
        const content = `
            <div class="row">
                <div class="col-md-4 text-center">
                    ${user.profile_picture ?
                        `<img src="/storage/${user.profile_picture}" alt="${user.name}" class="rounded-circle mb-3" style="width: 120px; height: 120px; object-fit: cover;">` :
                        `<div class="rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <span class="text-white fw-bold" style="font-size: 2rem;">${user.name.charAt(0)}</span>
                        </div>`
                    }
                    <h5 class="fw-bold">${user.name}</h5>
                    <p class="text-muted">${user.email}</p>
                    <span class="badge ${user.is_suspended ? 'bg-danger' : 'bg-success'} mb-2">
                        ${user.is_suspended ? 'Suspended' : 'Active'}
                    </span>
                </div>
                <div class="col-md-8">
                    <div class="row g-3">
                        <div class="col-sm-6">
                            <label class="form-label fw-medium">Contact Number</label>
                            <p class="form-control-plaintext">${user.contact_number || 'Not provided'}</p>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium">Gender</label>
                            <p class="form-control-plaintext">
                                ${user.gender === 'male' ?
                                    '<span class="badge bg-info-subtle text-info"><i class="bi bi-person me-1"></i>Male</span>' :
                                    user.gender === 'female' ?
                                    '<span class="badge bg-warning-subtle text-warning"><i class="bi bi-person-dress me-1"></i>Female</span>' :
                                    'Not specified'
                                }
                            </p>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium">Date of Birth</label>
                            <p class="form-control-plaintext">${user.date_of_birth || 'Not provided'}</p>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium">Email Verified</label>
                            <p class="form-control-plaintext">
                                <span class="badge ${user.email_verified_at ? 'bg-success' : 'bg-danger'}">
                                    ${user.email_verified_at ? 'Verified' : 'Unverified'}
                                </span>
                            </p>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-medium">Interests</label>
                            <p class="form-control-plaintext">${user.interests || 'Not provided'}</p>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-medium">Expectations</label>
                            <p class="form-control-plaintext">${user.expectation || 'Not provided'}</p>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium">Joined</label>
                            <p class="form-control-plaintext">${new Date(user.created_at).toLocaleDateString()}</p>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium">Last Updated</label>
                            <p class="form-control-plaintext">${new Date(user.updated_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('userDetailsContent').innerHTML = content;
    }

    // Toggle event status
    async toggleEventStatus(eventId) {
        const button = document.querySelector(`.btn-toggle-event-status[data-event-id="${eventId}"]`);
        const originalText = button.innerHTML;
        const isCurrentlyEnabled = button.classList.contains('btn-success');

        try {
            // Show loading state on button
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            const response = await fetch(`${this.apiUrl}/meeting-events/${eventId}/toggle-status-ajax`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(data.message);

                // Update button appearance immediately with animation
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    if (data.status) {
                        // Event is now enabled
                        button.className = 'btn btn-sm btn-success rounded-pill btn-toggle-event-status';
                        button.innerHTML = '<i class="bi bi-check-circle me-1"></i>Enabled';
                    } else {
                        // Event is now disabled
                        button.className = 'btn btn-sm btn-danger rounded-pill btn-toggle-event-status';
                        button.innerHTML = '<i class="bi bi-x-circle me-1"></i>Disabled';
                    }
                    button.style.transform = 'scale(1)';
                }, 100);

                // Refresh page after a short delay
                setTimeout(() => {
                    this.refreshPage();
                }, 1500);
            } else {
                this.showError(data.message || 'Failed to toggle event status');
                // Restore original button state
                button.innerHTML = originalText;
            }
        } catch (error) {
            this.showError('Failed to toggle event status');
            // Restore original button state
            button.innerHTML = originalText;
        } finally {
            button.disabled = false;
        }
    }

    // Delete event
    async deleteEvent(eventId) {
        if (!confirm('Are you sure you want to delete this meeting event? This action cannot be undone.')) {
            return;
        }

        const button = document.querySelector(`.btn-delete-event[data-event-id="${eventId}"]`);
        const originalText = button.innerHTML;

        try {
            // Show loading state on button
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            const response = await fetch(`${this.apiUrl}/meeting-events/${eventId}/ajax`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Meeting event deleted successfully');
                this.refreshPage();
            } else {
                this.showError(data.message || 'Failed to delete event');
            }
        } catch (error) {
            this.showError('Failed to delete event');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    // Refresh the current page
    refreshPage() {
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    // Simple utility functions
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showLoading() {
        // Loading functionality
    }

    hideLoading() {
        // Loading complete
    }

    // Simple toast notification
    showToast(message, type = 'success') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi ${type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-circle-fill'} me-2"></i>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on an admin page
    if (window.location.pathname.includes('/admin') && !window.location.pathname.includes('/admin/login')) {
        try {
            window.adminPanel = new AdminPanel();
        } catch (error) {
            // Silent error handling for production
        }
    }
});
