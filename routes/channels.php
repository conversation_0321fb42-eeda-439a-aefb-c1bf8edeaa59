<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Chat channel authorization
Broadcast::channel('chat.{bookingId}', function ($user, $bookingId) {
    $booking = \App\Models\TimeSpendingBooking::find($bookingId);
    
    if (!$booking) {
        return false;
    }
    
    // Check if user is part of this booking (client or provider)
    return $booking->client_id === $user->id || $booking->provider_id === $user->id;
});
