<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Booking Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the booking system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Payment Timeout
    |--------------------------------------------------------------------------
    |
    | The number of minutes a user has to complete payment after creating
    | a booking. After this time, the booking will be automatically cancelled
    | and the time slot will be released for other users.
    |
    */
    'payment_timeout' => env('BOOKING_PAYMENT_TIMEOUT', 30),

    /*
    |--------------------------------------------------------------------------
    | Cleanup Schedule
    |--------------------------------------------------------------------------
    |
    | How often (in minutes) to run the cleanup process for expired bookings.
    | This should be run via a scheduled task or cron job.
    |
    */
    'cleanup_interval' => env('BOOKING_CLEANUP_INTERVAL', 5),

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for booking-related notifications.
    |
    */
    'notifications' => [
        'payment_reminder_minutes' => env('BOOKING_PAYMENT_REMINDER', 10),
        'send_confirmation_email' => env('BOOKING_SEND_CONFIRMATION', true),
        'send_cancellation_email' => env('BOOKING_SEND_CANCELLATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto-Cancellation Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic booking cancellation when providers
    | don't respond before the scheduled meeting time.
    |
    */
    'auto_cancellation' => [
        'enabled' => env('BOOKING_AUTO_CANCELLATION_ENABLED', true),
        'check_interval_minutes' => env('BOOKING_AUTO_CANCELLATION_INTERVAL', 5),
        'send_provider_notification' => env('BOOKING_SEND_PROVIDER_MISSED_NOTIFICATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Definitions
    |--------------------------------------------------------------------------
    |
    | Valid status values for bookings and their meanings.
    |
    */
    'statuses' => [
        'pending' => 'Awaiting payment',
        'confirmed' => 'Payment completed, booking confirmed',
        'cancelled' => 'Booking cancelled',
        'auto_cancelled' => 'Automatically cancelled due to no provider response',
        'completed' => 'Service completed',
        'no_show' => 'Client did not show up',
    ],

    'payment_statuses' => [
        'pending' => 'Payment not yet completed',
        'paid' => 'Payment successful',
        'failed' => 'Payment failed or cancelled',
        'refunded' => 'Payment refunded',
    ],
];
