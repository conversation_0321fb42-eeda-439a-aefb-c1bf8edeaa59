<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_change_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('field_name'); // The field that was changed (e.g., 'name', 'email', 'interests')
            $table->text('old_value')->nullable(); // Previous value
            $table->text('new_value')->nullable(); // New value
            $table->string('action')->default('update'); // Action type: 'update', 'create', 'delete'
            $table->string('ip_address')->nullable(); // User's IP address
            $table->text('user_agent')->nullable(); // User's browser/device info
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['user_id', 'created_at']);
            $table->index('field_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_change_logs');
    }
};
