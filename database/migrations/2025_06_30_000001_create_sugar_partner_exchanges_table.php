<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sugar_partner_exchanges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user1_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('user2_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('initiated_by_admin_id')->constrained('users')->onDelete('cascade');
            $table->decimal('exchange_price', 10, 2);
            $table->string('currency', 3)->default('INR');
            $table->enum('status', ['pending_payment', 'payment_completed', 'profiles_viewed', 'responses_completed', 'cancelled'])->default('pending_payment');
            $table->timestamp('payment_completed_at')->nullable();
            $table->timestamp('profiles_viewed_at')->nullable();
            $table->timestamp('responses_completed_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();

            // Ensure unique exchanges between two users
            $table->unique(['user1_id', 'user2_id']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sugar_partner_exchanges');
    }
};
