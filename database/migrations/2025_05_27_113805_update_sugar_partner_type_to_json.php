<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the old enum column
            $table->dropColumn('sugar_partner_type');

            // Add new JSON column for multiple selections
            $table->json('sugar_partner_types')->nullable()->after('interested_in_sugar_partner');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the JSON column
            $table->dropColumn('sugar_partner_types');

            // Restore the old enum column
            $table->enum('sugar_partner_type', ['sugar_daddy', 'sugar_mommy', 'sugar_companion_female', 'sugar_companion_male'])->nullable()->after('interested_in_sugar_partner');
        });
    }
};
