<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('couple_activity_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('requester_id')->constrained('users')->onDelete('cascade'); // User who sent the request
            $table->foreignId('partner_id')->constrained('users')->onDelete('cascade'); // User who received the request
            $table->enum('type', ['own_partner', 'couple_swap'])->default('own_partner'); // Type of request
            $table->enum('status', ['pending', 'approved', 'rejected', 'blocked'])->default('pending');
            $table->text('message')->nullable(); // Optional message from requester
            $table->text('rejection_reason')->nullable(); // Reason for rejection
            $table->timestamp('responded_at')->nullable(); // When the request was approved/rejected
            $table->timestamps();

            // Ensure a user can't send multiple requests to the same partner for the same type
            $table->unique(['requester_id', 'partner_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('couple_activity_requests');
    }
};
