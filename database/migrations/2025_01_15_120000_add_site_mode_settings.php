<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only proceed if settings table exists
        if (!Schema::hasTable('settings')) {
            return;
        }

        // Add site mode settings to the settings table
        $settings = [
            [
                'key' => 'maintenance_mode_enabled',
                'value' => '0',
                'group' => 'site_mode',
                'type' => 'boolean',
                'label' => 'Enable Maintenance Mode',
                'description' => 'When enabled, all users will see a maintenance page except admin users.'
            ],
            [
                'key' => 'maintenance_mode_end_time',
                'value' => null,
                'group' => 'site_mode',
                'type' => 'datetime',
                'label' => 'Maintenance End Time',
                'description' => 'Approximate time when maintenance will be completed. Maintenance mode will automatically disable at this time.'
            ],
            [
                'key' => 'maintenance_mode_message',
                'value' => 'We are currently under maintenance. The website will be back online approximately at {end_time}.',
                'group' => 'site_mode',
                'type' => 'textarea',
                'label' => 'Maintenance Message',
                'description' => 'Message to display on the maintenance page. Use {end_time} placeholder for the scheduled end time.'
            ],
            [
                'key' => 'coming_soon_mode_enabled',
                'value' => '0',
                'group' => 'site_mode',
                'type' => 'boolean',
                'label' => 'Enable Coming Soon Mode',
                'description' => 'When enabled, all users will see a coming soon page with countdown timer except admin users.'
            ],
            [
                'key' => 'coming_soon_launch_time',
                'value' => null,
                'group' => 'site_mode',
                'type' => 'datetime',
                'label' => 'Launch Date & Time',
                'description' => 'Exact date and time when the website will go live. Coming soon mode will automatically disable at this time.'
            ],
            [
                'key' => 'coming_soon_title',
                'value' => 'Something Amazing is Coming Soon!',
                'group' => 'site_mode',
                'type' => 'text',
                'label' => 'Coming Soon Title',
                'description' => 'Main title to display on the coming soon page.'
            ],
            [
                'key' => 'coming_soon_message',
                'value' => 'We are working hard to bring you an amazing dating experience. Stay tuned for the launch!',
                'group' => 'site_mode',
                'type' => 'textarea',
                'label' => 'Coming Soon Message',
                'description' => 'Message to display on the coming soon page below the countdown timer.'
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only proceed if settings table exists
        if (!Schema::hasTable('settings')) {
            return;
        }

        // Remove site mode settings
        $keys = [
            'maintenance_mode_enabled',
            'maintenance_mode_end_time',
            'maintenance_mode_message',
            'coming_soon_mode_enabled',
            'coming_soon_launch_time',
            'coming_soon_title',
            'coming_soon_message'
        ];

        Setting::whereIn('key', $keys)->delete();
    }
};
