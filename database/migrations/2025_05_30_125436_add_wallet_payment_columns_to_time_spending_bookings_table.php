<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->decimal('wallet_amount_used', 10, 2)->default(0)->after('payment_method');
            $table->decimal('razorpay_amount_paid', 10, 2)->default(0)->after('wallet_amount_used');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropColumn(['wallet_amount_used', 'razorpay_amount_paid']);
        });
    }
};
