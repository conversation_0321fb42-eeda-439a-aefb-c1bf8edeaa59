<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_plan_id')->constrained()->onDelete('cascade');
            $table->decimal('amount_paid', 10, 2); // Amount paid for this subscription
            $table->decimal('wallet_amount_used', 10, 2)->default(0); // Amount paid from wallet
            $table->decimal('razorpay_amount_paid', 10, 2)->default(0); // Amount paid via Razorpay
            $table->string('payment_method')->nullable(); // 'wallet', 'razorpay', 'mixed'
            $table->string('razorpay_payment_id')->nullable();
            $table->string('razorpay_order_id')->nullable();
            $table->string('razorpay_signature')->nullable();
            $table->string('status')->default('active'); // active, expired, cancelled
            $table->datetime('starts_at'); // Subscription start date
            $table->datetime('expires_at'); // Subscription expiry date
            $table->datetime('paid_at')->nullable(); // When payment was completed
            $table->json('metadata')->nullable(); // Additional subscription data
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['user_id', 'expires_at']);
            $table->index(['expires_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_subscriptions');
    }
};
