<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sugar_partner_rejections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exchange_id')->constrained('sugar_partner_exchanges')->onDelete('cascade');
            $table->foreignId('rejector_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('rejected_user_id')->constrained('users')->onDelete('cascade');
            $table->enum('rejection_type', ['accept', 'soft_reject', 'hard_reject']);
            $table->text('rejection_reason')->nullable();
            $table->text('admin_note')->nullable(); // Private note for admin
            $table->boolean('notification_sent')->default(false);
            $table->timestamp('notification_sent_at')->nullable();
            $table->timestamps();

            $table->index(['rejector_id', 'rejected_user_id']);
            $table->index(['rejection_type', 'created_at']);
            $table->index(['exchange_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sugar_partner_rejections');
    }
};
