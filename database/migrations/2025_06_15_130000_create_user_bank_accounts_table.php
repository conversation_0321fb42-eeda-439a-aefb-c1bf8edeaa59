<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_bank_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('account_holder_name');
            $table->string('account_number', 20);
            $table->string('ifsc_code', 11);
            $table->string('bank_name');
            $table->enum('account_type', ['savings', 'current'])->default('savings');
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_primary')->default(false);
            $table->string('razorpay_fund_account_id')->nullable();
            $table->json('verification_data')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'is_primary']);
            $table->index('is_verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_bank_accounts');
    }
};
