<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            // Change columns to have better constraints
            $table->string('name')->nullable(false)->change();
            $table->unsignedInteger('duration_months')->change();
            $table->decimal('amount', 10, 2)->unsigned()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            // Revert the column changes
            $table->string('name')->nullable()->change();
            $table->integer('duration_months')->change();
            $table->decimal('amount', 10, 2)->change();
        });
    }
};
