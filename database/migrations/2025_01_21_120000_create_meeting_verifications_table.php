<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_verifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained('time_spending_bookings')->onDelete('cascade');
            $table->string('client_start_photo')->nullable(); // Photo taken by client at meeting start
            $table->string('provider_start_photo')->nullable(); // Photo taken by provider at meeting start
            $table->string('client_end_photo')->nullable(); // Photo taken by client at meeting end
            $table->string('provider_end_photo')->nullable(); // Photo taken by provider at meeting end
            $table->timestamp('client_start_time')->nullable(); // When client uploaded start photo
            $table->timestamp('provider_start_time')->nullable(); // When provider uploaded start photo
            $table->timestamp('client_end_time')->nullable(); // When client uploaded end photo
            $table->timestamp('provider_end_time')->nullable(); // When provider uploaded end photo
            $table->timestamp('meeting_start_time')->nullable(); // Calculated meeting start (when both start photos uploaded)
            $table->timestamp('meeting_end_time')->nullable(); // Calculated meeting end (when both end photos uploaded)
            $table->integer('calculated_duration_minutes')->nullable(); // Duration in minutes
            $table->boolean('is_verified')->default(false); // True when all 4 photos are uploaded
            $table->text('notes')->nullable(); // Any additional notes

            // Location data for start photos
            $table->decimal('client_start_latitude', 10, 8)->nullable();
            $table->decimal('client_start_longitude', 11, 8)->nullable();
            $table->string('client_start_address')->nullable();
            $table->decimal('provider_start_latitude', 10, 8)->nullable();
            $table->decimal('provider_start_longitude', 11, 8)->nullable();
            $table->string('provider_start_address')->nullable();

            // Location data for end photos
            $table->decimal('client_end_latitude', 10, 8)->nullable();
            $table->decimal('client_end_longitude', 11, 8)->nullable();
            $table->string('client_end_address')->nullable();
            $table->decimal('provider_end_latitude', 10, 8)->nullable();
            $table->decimal('provider_end_longitude', 11, 8)->nullable();
            $table->string('provider_end_address')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['booking_id']);
            $table->index(['is_verified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_verifications');
    }
};
