<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if column doesn't already exist
        if (!Schema::hasColumn('meeting_addresses', 'payment_amount_couple')) {
            Schema::table('meeting_addresses', function (Blueprint $table) {
                $table->decimal('payment_amount_couple', 10, 2)->default(0)->after('payment_amount_girls');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meeting_addresses', function (Blueprint $table) {
            $table->dropColumn('payment_amount_couple');
        });
    }
};
