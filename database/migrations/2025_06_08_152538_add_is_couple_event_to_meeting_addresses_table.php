<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if column doesn't already exist
        if (!Schema::hasColumn('meeting_addresses', 'is_couple_event')) {
            Schema::table('meeting_addresses', function (Blueprint $table) {
                $table->boolean('is_couple_event')->default(false)->after('description');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meeting_addresses', function (Blueprint $table) {
            $table->dropColumn('is_couple_event');
        });
    }
};
