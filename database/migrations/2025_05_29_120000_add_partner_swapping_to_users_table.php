<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Couple activity settings
            $table->boolean('is_couple_activity_enabled')->default(false)->after('is_time_spending_enabled');
            $table->string('couple_activity_status')->default('available')->after('is_couple_activity_enabled'); // available, paired, inactive
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_couple_activity_enabled',
                'couple_activity_status'
            ]);
        });
    }
};
