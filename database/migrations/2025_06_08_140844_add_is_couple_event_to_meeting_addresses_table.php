<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('meeting_addresses', function (Blueprint $table) {
            $table->boolean('is_couple_event')->default(false)->after('description');
            $table->decimal('payment_amount_couple', 10, 2)->default(0)->after('is_couple_event');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meeting_addresses', function (Blueprint $table) {
            $table->dropColumn(['is_couple_event', 'payment_amount_couple']);
        });
    }
};
