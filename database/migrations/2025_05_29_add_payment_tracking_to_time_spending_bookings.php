<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            // Add payment tracking columns if they don't exist
            if (!Schema::hasColumn('time_spending_bookings', 'cancelled_at')) {
                $table->timestamp('cancelled_at')->nullable()->after('paid_at');
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'cancellation_reason')) {
                $table->string('cancellation_reason')->nullable()->after('cancelled_at');
            }
            
            // Add index for performance on payment cleanup queries
            $table->index(['status', 'payment_status', 'created_at'], 'booking_payment_status_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropIndex('booking_payment_status_idx');
            $table->dropColumn(['cancelled_at', 'cancellation_reason']);
        });
    }
};
