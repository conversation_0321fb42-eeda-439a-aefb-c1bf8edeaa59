<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_spending_bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('users')->onDelete('cascade'); // User who is booking
            $table->foreignId('provider_id')->constrained('users')->onDelete('cascade'); // User being booked
            $table->datetime('booking_date'); // Date and time of the booking
            $table->integer('duration_hours')->default(1); // Duration in hours
            $table->decimal('hourly_rate', 8, 2); // Rate at the time of booking
            $table->decimal('total_amount', 10, 2); // Total calculated amount
            $table->string('status')->default('pending'); // pending, confirmed, completed, cancelled
            $table->string('payment_status')->default('pending'); // pending, paid, failed, refunded
            $table->string('escrow_status')->default('pending'); // pending, held, released, disputed
            $table->timestamp('escrow_held_at')->nullable(); // When payment was held in escrow
            $table->timestamp('escrow_released_at')->nullable(); // When payment was released to provider
            $table->timestamp('auto_release_at')->nullable(); // When payment will auto-release (24 hours after meeting end)
            $table->string('payment_method')->nullable(); // razorpay, etc.
            $table->string('razorpay_payment_id')->nullable();
            $table->string('razorpay_order_id')->nullable();
            $table->string('razorpay_signature')->nullable();
            $table->text('notes')->nullable(); // Optional notes from client
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['provider_id', 'booking_date']);
            $table->index(['client_id', 'booking_date']);
            $table->index(['booking_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_spending_bookings');
    }
};
