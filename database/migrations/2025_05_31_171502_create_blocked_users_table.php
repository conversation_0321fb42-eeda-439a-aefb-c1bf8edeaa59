<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blocked_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('blocker_id'); // User who is blocking
            $table->unsignedBigInteger('blocked_id'); // User who is being blocked
            $table->string('reason')->nullable(); // Reason for blocking
            $table->unsignedBigInteger('booking_id')->nullable(); // Related booking if any
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('blocker_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('blocked_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('booking_id')->references('id')->on('time_spending_bookings')->onDelete('set null');

            // Unique constraint to prevent duplicate blocks
            $table->unique(['blocker_id', 'blocked_id']);

            // Indexes for performance
            $table->index(['blocker_id', 'blocked_id']);
            $table->index('blocked_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blocked_users');
    }
};
