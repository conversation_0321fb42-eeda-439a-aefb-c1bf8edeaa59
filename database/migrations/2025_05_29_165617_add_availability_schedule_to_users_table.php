<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add availability schedule fields
            $table->json('availability_schedule')->nullable()->after('is_time_spending_enabled');
            $table->json('holiday_dates')->nullable()->after('availability_schedule');
            $table->string('service_location')->nullable()->after('holiday_dates');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['availability_schedule', 'holiday_dates', 'service_location']);
        });
    }
};
