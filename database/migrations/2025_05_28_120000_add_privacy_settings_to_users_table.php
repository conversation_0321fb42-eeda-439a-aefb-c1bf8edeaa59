<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Privacy settings for Personal Information
            $table->boolean('is_public_profile')->default(false);
            $table->boolean('show_contact_number')->default(false);
            $table->boolean('show_date_of_birth')->default(true);
            $table->boolean('hide_dob_year')->default(false);
            $table->boolean('show_interests_hobbies')->default(true);
            $table->boolean('show_expectations')->default(true);

            // Privacy settings for Gallery
            $table->boolean('show_gallery_images')->default(true);

            // Time Spending settings
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->string('currency', 3)->default('INR');
            $table->boolean('is_time_spending_enabled')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_public_profile',
                'show_contact_number',
                'show_date_of_birth',
                'hide_dob_year',
                'show_interests_hobbies',
                'show_expectations',
                'show_gallery_images',
                'hourly_rate',
                'currency',
                'is_time_spending_enabled'
            ]);
        });
    }
};
