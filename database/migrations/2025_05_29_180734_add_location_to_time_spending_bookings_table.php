<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->string('meeting_location')->nullable()->after('notes');
            $table->string('client_location')->nullable()->after('meeting_location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropColumn(['meeting_location', 'client_location']);
        });
    }
};
