<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add withdrawal-related settings to settings table
        $withdrawalSettings = [
            [
                'key' => 'minimum_withdrawal_amount',
                'value' => '100',
                'group' => 'withdrawal',
                'type' => 'number',
                'label' => 'Minimum Withdrawal Amount',
                'description' => 'Minimum amount required in wallet to request withdrawal',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'maximum_withdrawal_amount',
                'value' => '50000',
                'group' => 'withdrawal',
                'type' => 'number',
                'label' => 'Maximum Withdrawal Amount',
                'description' => 'Maximum amount that can be withdrawn per request',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'daily_withdrawal_limit',
                'value' => '100000',
                'group' => 'withdrawal',
                'type' => 'number',
                'label' => 'Daily Withdrawal Limit',
                'description' => 'Maximum amount that can be withdrawn per day',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'withdrawal_processing_fee',
                'value' => '5',
                'group' => 'withdrawal',
                'type' => 'number',
                'label' => 'Withdrawal Processing Fee',
                'description' => 'Processing fee for withdrawal requests (in rupees)',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'withdrawal_processing_time',
                'value' => '1-3 business days',
                'group' => 'withdrawal',
                'type' => 'text',
                'label' => 'Withdrawal Processing Time',
                'description' => 'Expected processing time for withdrawals',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'auto_withdrawal_enabled',
                'value' => 'false',
                'group' => 'withdrawal',
                'type' => 'boolean',
                'label' => 'Auto Withdrawal Enabled',
                'description' => 'Enable automatic withdrawal processing via payment gateway',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($withdrawalSettings as $setting) {
            DB::table('settings')->updateOrInsert(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $keys = [
            'minimum_withdrawal_amount',
            'maximum_withdrawal_amount',
            'daily_withdrawal_limit',
            'withdrawal_processing_fee',
            'withdrawal_processing_time',
            'auto_withdrawal_enabled',
        ];

        DB::table('settings')->whereIn('key', $keys)->delete();
    }
};
