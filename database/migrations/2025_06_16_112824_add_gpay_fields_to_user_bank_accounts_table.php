<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_bank_accounts', function (Blueprint $table) {
            $table->string('payment_method_type')->default('bank')->after('account_type'); // 'bank' or 'gpay'
            $table->string('gpay_number')->nullable()->after('payment_method_type');
            $table->string('gpay_upi')->nullable()->after('gpay_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_bank_accounts', function (Blueprint $table) {
            $table->dropColumn(['payment_method_type', 'gpay_number', 'gpay_upi']);
        });
    }
};
