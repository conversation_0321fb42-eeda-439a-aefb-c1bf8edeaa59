<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sugar_partner_hard_rejects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user1_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('user2_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('rejector_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('original_exchange_id')->constrained('sugar_partner_exchanges')->onDelete('cascade');
            $table->text('rejection_reason')->nullable();
            $table->text('admin_note')->nullable();
            $table->timestamps();

            // Ensure we can quickly check if two users have hard rejected each other
            $table->unique(['user1_id', 'user2_id']);
            $table->index(['rejector_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sugar_partner_hard_rejects');
    }
};
