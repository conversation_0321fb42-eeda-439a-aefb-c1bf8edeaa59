<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_revenues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained('time_spending_bookings')->onDelete('cascade');
            $table->decimal('platform_fee', 10, 2)->default(0.00);
            $table->decimal('commission_amount', 10, 2)->default(0.00);
            $table->decimal('total_admin_earnings', 10, 2)->default(0.00);
            $table->string('revenue_type')->default('booking_payment');
            $table->text('description')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index('booking_id');
            $table->index('revenue_type');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_revenues');
    }
};
