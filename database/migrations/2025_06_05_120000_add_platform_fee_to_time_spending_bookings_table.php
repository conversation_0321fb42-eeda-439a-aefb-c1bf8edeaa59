<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            // Add platform fee tracking fields
            $table->decimal('platform_fee', 8, 2)->default(0)->after('hourly_rate');
            $table->decimal('base_amount', 8, 2)->nullable()->after('platform_fee');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropColumn(['platform_fee', 'base_amount']);
        });
    }
};
