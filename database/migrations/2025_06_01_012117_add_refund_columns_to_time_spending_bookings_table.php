<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->enum('refund_status', ['pending', 'processed', 'failed'])->nullable()->after('payment_status');
            $table->decimal('refund_amount', 10, 2)->nullable()->after('refund_status');
            $table->timestamp('refunded_at')->nullable()->after('refund_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropColumn(['refund_status', 'refund_amount', 'refunded_at']);
        });
    }
};
