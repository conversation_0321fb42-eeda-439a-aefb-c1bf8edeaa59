<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table to update the CHECK constraint
        if (DB::getDriverName() === 'sqlite') {
            // Create a temporary table with the correct constraint
            Schema::create('couple_activity_requests_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('requester_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('partner_id')->constrained('users')->onDelete('cascade');
                $table->enum('type', ['own_partner', 'couple_swap'])->default('own_partner');
                $table->enum('status', ['pending', 'approved', 'rejected', 'blocked'])->default('pending');
                $table->text('message')->nullable();
                $table->text('rejection_reason')->nullable();
                $table->timestamp('responded_at')->nullable();
                $table->timestamps();
                
                $table->unique(['requester_id', 'partner_id', 'type']);
            });
            
            // Copy data from old table to new table
            DB::statement('INSERT INTO couple_activity_requests_temp (id, requester_id, partner_id, type, status, message, rejection_reason, responded_at, created_at, updated_at) SELECT id, requester_id, partner_id, type, status, message, rejection_reason, responded_at, created_at, updated_at FROM couple_activity_requests');
            
            // Drop old table
            Schema::dropIfExists('couple_activity_requests');
            
            // Rename temp table to original name
            DB::statement('ALTER TABLE couple_activity_requests_temp RENAME TO couple_activity_requests');
        } else {
            // For MySQL, the previous migration should have worked
            DB::statement("ALTER TABLE couple_activity_requests MODIFY COLUMN status ENUM('pending', 'approved', 'rejected', 'blocked') DEFAULT 'pending'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (DB::getDriverName() === 'sqlite') {
            // Create a temporary table with the old constraint
            Schema::create('couple_activity_requests_temp', function (Blueprint $table) {
                $table->id();
                $table->foreignId('requester_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('partner_id')->constrained('users')->onDelete('cascade');
                $table->enum('type', ['own_partner', 'couple_swap'])->default('own_partner');
                $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
                $table->text('message')->nullable();
                $table->text('rejection_reason')->nullable();
                $table->timestamp('responded_at')->nullable();
                $table->timestamps();
                
                $table->unique(['requester_id', 'partner_id', 'type']);
            });
            
            // Copy data (excluding blocked status)
            DB::statement('INSERT INTO couple_activity_requests_temp (id, requester_id, partner_id, type, status, message, rejection_reason, responded_at, created_at, updated_at) SELECT id, requester_id, partner_id, type, CASE WHEN status = "blocked" THEN "rejected" ELSE status END, message, rejection_reason, responded_at, created_at, updated_at FROM couple_activity_requests');
            
            // Drop old table
            Schema::dropIfExists('couple_activity_requests');
            
            // Rename temp table to original name
            DB::statement('ALTER TABLE couple_activity_requests_temp RENAME TO couple_activity_requests');
        } else {
            DB::statement("ALTER TABLE couple_activity_requests MODIFY COLUMN status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'");
        }
    }
};
