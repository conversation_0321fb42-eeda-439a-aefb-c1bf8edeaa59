<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            // Add escrow-related fields if they don't exist
            if (!Schema::hasColumn('time_spending_bookings', 'escrow_status')) {
                $table->string('escrow_status')->default('pending')->after('payment_status'); // pending, held, released, disputed
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'escrow_held_at')) {
                $table->timestamp('escrow_held_at')->nullable()->after('escrow_status'); // When payment was held in escrow
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'escrow_released_at')) {
                $table->timestamp('escrow_released_at')->nullable()->after('escrow_held_at'); // When payment was released to provider
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'auto_release_at')) {
                $table->timestamp('auto_release_at')->nullable()->after('escrow_released_at'); // When payment will auto-release (24 hours after meeting end)
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'dispute_reason')) {
                $table->text('dispute_reason')->nullable()->after('auto_release_at'); // Reason for dispute if any
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'disputed_at')) {
                $table->timestamp('disputed_at')->nullable()->after('dispute_reason'); // When dispute was raised
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'disputed_by')) {
                $table->foreignId('disputed_by')->nullable()->constrained('users')->onDelete('set null')->after('disputed_at'); // Who raised the dispute
            }
            
            // Add index for escrow queries
            $table->index(['escrow_status', 'auto_release_at'], 'escrow_auto_release_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropIndex('escrow_auto_release_idx');
            $table->dropColumn([
                'escrow_status',
                'escrow_held_at', 
                'escrow_released_at',
                'auto_release_at',
                'dispute_reason',
                'disputed_at',
                'disputed_by'
            ]);
        });
    }
};
