<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Plan name (e.g., "1 Month Premium", "3 Month Premium")
            $table->integer('duration_months'); // Duration in months
            $table->decimal('amount', 10, 2); // Plan price (kept for backward compatibility)
            $table->decimal('original_price', 10, 2); // Original price before discount
            $table->decimal('discount_price', 10, 2)->nullable(); // Discounted price (optional)
            $table->text('description')->nullable(); // Rich text description
            $table->boolean('is_active')->default(true); // Admin can enable/disable plans
            $table->integer('sort_order')->default(0); // For ordering plans in UI
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
