<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_addresses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->longText('description'); // Changed to longText for rich content
            $table->longText('rules_and_regulations')->nullable(); // Rich text for rules
            $table->decimal('payment_amount_boys', 10, 2)->default(0); // Payment for boys
            $table->decimal('payment_amount_girls', 10, 2)->default(0); // Payment for girls
            $table->boolean('is_event_enabled')->default(true); // Enable/disable event
            $table->datetime('event_date')->nullable(); // Optional event date
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_addresses');
    }
};
