<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->enum('provider_status', ['pending', 'accepted', 'rejected'])->default('pending')->after('status');
            $table->text('rejection_reason')->nullable()->after('provider_status');
            $table->timestamp('provider_responded_at')->nullable()->after('rejection_reason');
            $table->decimal('commission_percentage', 5, 2)->default(0.00)->after('total_amount');
            $table->decimal('commission_amount', 10, 2)->default(0.00)->after('commission_percentage');
            $table->decimal('provider_amount', 10, 2)->default(0.00)->after('commission_amount');
            $table->boolean('chat_enabled')->default(false)->after('provider_responded_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropColumn([
                'provider_status', 
                'rejection_reason', 
                'provider_responded_at',
                'commission_percentage',
                'commission_amount',
                'provider_amount',
                'chat_enabled'
            ]);
        });
    }
};
