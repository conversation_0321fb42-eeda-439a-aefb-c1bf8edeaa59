<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            // Change duration_hours from integer to decimal to support half-hour bookings
            $table->decimal('duration_hours', 4, 2)->default(1.00)->change();

            // Add actual_duration_hours to store the real booking duration (separate from billing duration)
            if (!Schema::hasColumn('time_spending_bookings', 'actual_duration_hours')) {
                $table->decimal('actual_duration_hours', 4, 2)->default(1.00)->after('duration_hours');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            // Remove actual_duration_hours column
            if (Schema::hasColumn('time_spending_bookings', 'actual_duration_hours')) {
                $table->dropColumn('actual_duration_hours');
            }

            // Revert back to integer (this will truncate decimal values)
            $table->integer('duration_hours')->default(1)->change();
        });
    }
};
