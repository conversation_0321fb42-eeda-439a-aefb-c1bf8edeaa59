<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sugar_partner_exchange_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exchange_id')->constrained('sugar_partner_exchanges')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('INR');
            $table->enum('payment_method', ['razorpay', 'wallet', 'stripe', 'paypal'])->default('razorpay');
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
            $table->string('payment_id')->nullable(); // External payment gateway ID
            $table->string('order_id')->nullable(); // External order ID
            $table->string('signature')->nullable(); // Payment signature for verification
            $table->json('payment_details')->nullable(); // Store additional payment gateway response
            $table->timestamp('paid_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->timestamps();

            $table->index(['exchange_id', 'user_id']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sugar_partner_exchange_payments');
    }
};
