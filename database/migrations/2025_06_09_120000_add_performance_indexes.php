<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for better query performance
        
        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'is_time_spending_enabled') && Schema::hasColumn('users', 'is_public_profile')) {
                $table->index(['is_time_spending_enabled', 'is_public_profile'], 'users_time_spending_public_idx');
            }
            if (Schema::hasColumn('users', 'gender') && Schema::hasColumn('users', 'is_time_spending_enabled')) {
                $table->index(['gender', 'is_time_spending_enabled'], 'users_gender_time_spending_idx');
            }
            $table->index(['created_at'], 'users_created_at_idx');
            if (Schema::hasColumn('users', 'email_verified_at')) {
                $table->index(['email_verified_at'], 'users_email_verified_idx');
            }
            if (Schema::hasColumn('users', 'is_suspended')) {
                $table->index(['is_suspended'], 'users_suspended_idx');
            }
        });

        // Time spending bookings indexes
        if (Schema::hasTable('time_spending_bookings')) {
            Schema::table('time_spending_bookings', function (Blueprint $table) {
                $table->index(['client_id', 'status'], 'bookings_client_status_idx');
                $table->index(['provider_id', 'status'], 'bookings_provider_status_idx');
                $table->index(['booking_date', 'status'], 'bookings_date_status_idx');
                $table->index(['payment_status'], 'bookings_payment_status_idx');
                $table->index(['created_at'], 'bookings_created_at_idx');
            });
        }

        // Gallery images indexes
        if (Schema::hasTable('gallery_images')) {
            Schema::table('gallery_images', function (Blueprint $table) {
                $table->index(['user_id', 'is_active'], 'gallery_user_active_idx');
                $table->index(['is_active', 'order'], 'gallery_active_order_idx');
            });
        }

        // Wallet transactions indexes
        if (Schema::hasTable('wallet_transactions')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                $table->index(['user_id', 'created_at'], 'wallet_user_created_idx');
                $table->index(['type', 'created_at'], 'wallet_type_created_idx');
            });
        }

        // Event payments indexes
        if (Schema::hasTable('event_payments')) {
            Schema::table('event_payments', function (Blueprint $table) {
                $table->index(['user_id', 'status'], 'event_payments_user_status_idx');
                $table->index(['meeting_address_id', 'status'], 'event_payments_meeting_status_idx');
                $table->index(['created_at'], 'event_payments_created_at_idx');
            });
        }

        // Chat messages indexes
        if (Schema::hasTable('chat_messages')) {
            Schema::table('chat_messages', function (Blueprint $table) {
                $table->index(['sender_id', 'receiver_id'], 'chat_sender_receiver_idx');
                $table->index(['receiver_id', 'is_read'], 'chat_receiver_read_idx');
                $table->index(['created_at'], 'chat_created_at_idx');
            });
        }

        // Settings indexes
        if (Schema::hasTable('settings')) {
            Schema::table('settings', function (Blueprint $table) {
                $table->index(['group'], 'settings_group_idx');
                $table->index(['type'], 'settings_type_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes
        Schema::table('users', function (Blueprint $table) {
            try {
                $table->dropIndex('users_time_spending_public_idx');
            } catch (Exception $e) {}
            try {
                $table->dropIndex('users_gender_time_spending_idx');
            } catch (Exception $e) {}
            try {
                $table->dropIndex('users_created_at_idx');
            } catch (Exception $e) {}
            try {
                $table->dropIndex('users_email_verified_idx');
            } catch (Exception $e) {}
            try {
                $table->dropIndex('users_suspended_idx');
            } catch (Exception $e) {}
        });

        if (Schema::hasTable('time_spending_bookings')) {
            Schema::table('time_spending_bookings', function (Blueprint $table) {
                $table->dropIndex('bookings_client_status_idx');
                $table->dropIndex('bookings_provider_status_idx');
                $table->dropIndex('bookings_date_status_idx');
                $table->dropIndex('bookings_payment_status_idx');
                $table->dropIndex('bookings_created_at_idx');
            });
        }

        if (Schema::hasTable('gallery_images')) {
            Schema::table('gallery_images', function (Blueprint $table) {
                $table->dropIndex('gallery_user_active_idx');
                $table->dropIndex('gallery_active_order_idx');
            });
        }

        if (Schema::hasTable('wallet_transactions')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                $table->dropIndex('wallet_user_created_idx');
                $table->dropIndex('wallet_type_created_idx');
            });
        }

        if (Schema::hasTable('event_payments')) {
            Schema::table('event_payments', function (Blueprint $table) {
                $table->dropIndex('event_payments_user_status_idx');
                $table->dropIndex('event_payments_meeting_status_idx');
                $table->dropIndex('event_payments_created_at_idx');
            });
        }

        if (Schema::hasTable('chat_messages')) {
            Schema::table('chat_messages', function (Blueprint $table) {
                $table->dropIndex('chat_sender_receiver_idx');
                $table->dropIndex('chat_receiver_read_idx');
                $table->dropIndex('chat_created_at_idx');
            });
        }

        if (Schema::hasTable('settings')) {
            Schema::table('settings', function (Blueprint $table) {
                $table->dropIndex('settings_group_idx');
                $table->dropIndex('settings_type_idx');
            });
        }
    }
};
