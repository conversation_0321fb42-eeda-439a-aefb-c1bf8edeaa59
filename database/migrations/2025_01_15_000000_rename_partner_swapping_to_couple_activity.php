<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename the table only if it exists
        if (Schema::hasTable('partner_swapping_requests')) {
            Schema::rename('partner_swapping_requests', 'couple_activity_requests');
        }

        // Rename columns in users table only if they exist
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (Schema::hasColumn('users', 'is_partner_swapping_enabled')) {
                    $table->renameColumn('is_partner_swapping_enabled', 'is_couple_activity_enabled');
                }
                if (Schema::hasColumn('users', 'partner_swapping_status')) {
                    $table->renameColumn('partner_swapping_status', 'couple_activity_status');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rename the table back only if it exists
        if (Schema::hasTable('couple_activity_requests')) {
            Schema::rename('couple_activity_requests', 'partner_swapping_requests');
        }

        // Rename columns back in users table only if they exist
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (Schema::hasColumn('users', 'is_couple_activity_enabled')) {
                    $table->renameColumn('is_couple_activity_enabled', 'is_partner_swapping_enabled');
                }
                if (Schema::hasColumn('users', 'couple_activity_status')) {
                    $table->renameColumn('couple_activity_status', 'partner_swapping_status');
                }
            });
        }
    }
};
