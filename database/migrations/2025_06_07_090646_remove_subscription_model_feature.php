<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Feature;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove subscription-related features from database
        Feature::where('name', 'subscription_model')->delete();
        Feature::where('name', 'subscription')->delete();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore subscription features if needed
        Feature::firstOrCreate(['name' => 'subscription_model'], [
            'label' => 'Subscription Model',
            'description' => 'Enable subscription model for Time Spending Service. If disabled, users can access the service without a subscription.',
            'is_enabled' => false,
            'is_hidden' => true,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'subscription'], [
            'label' => 'Subscription Model',
            'description' => 'Enable subscription model for Time Spending Service. If disabled, users can access the service without a subscription.',
            'is_enabled' => false,
            'is_hidden' => false,
            'options' => null
        ]);
    }
};
