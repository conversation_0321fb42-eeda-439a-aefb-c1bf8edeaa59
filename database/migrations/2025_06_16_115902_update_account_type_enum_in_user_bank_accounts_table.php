<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if we're using SQLite (for testing)
        if (DB::getDriverName() === 'sqlite') {
            // SQLite doesn't support ENUM or MODIFY COLUMN, so we'll skip this migration in tests
            return;
        }

        DB::statement("ALTER TABLE user_bank_accounts MODIFY COLUMN account_type ENUM('savings', 'current', 'gpay') DEFAULT 'savings'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if we're using SQLite (for testing)
        if (DB::getDriverName() === 'sqlite') {
            // SQLite doesn't support ENUM or MODIFY COLUMN, so we'll skip this migration in tests
            return;
        }

        DB::statement("ALTER TABLE user_bank_accounts MODIFY COLUMN account_type ENUM('savings', 'current') DEFAULT 'savings'");
    }
};
