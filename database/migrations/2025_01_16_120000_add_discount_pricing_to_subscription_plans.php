<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only add columns if they don't exist
        if (!Schema::hasColumn('subscription_plans', 'original_price')) {
            Schema::table('subscription_plans', function (Blueprint $table) {
                $table->decimal('original_price', 10, 2)->nullable()->after('amount');
            });
        }

        if (!Schema::hasColumn('subscription_plans', 'discount_price')) {
            Schema::table('subscription_plans', function (Blueprint $table) {
                $table->decimal('discount_price', 10, 2)->nullable()->after('original_price');
            });
        }

        // Migrate existing data: set original_price to current amount value if it's null
        \DB::table('subscription_plans')
            ->whereNull('original_price')
            ->update(['original_price' => \DB::raw('amount')]);

        // Now make original_price NOT NULL since all records have values
        if (Schema::hasColumn('subscription_plans', 'original_price')) {
            Schema::table('subscription_plans', function (Blueprint $table) {
                $table->decimal('original_price', 10, 2)->nullable(false)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->dropColumn(['original_price', 'discount_price']);
        });
    }
};
