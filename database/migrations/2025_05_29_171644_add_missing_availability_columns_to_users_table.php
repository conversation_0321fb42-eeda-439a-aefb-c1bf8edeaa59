<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add the missing availability schedule columns
            if (!Schema::hasColumn('users', 'availability_schedule')) {
                $table->json('availability_schedule')->nullable()->after('is_time_spending_enabled');
            }
            if (!Schema::hasColumn('users', 'holiday_dates')) {
                $table->json('holiday_dates')->nullable()->after('availability_schedule');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'availability_schedule')) {
                $table->dropColumn('availability_schedule');
            }
            if (Schema::hasColumn('users', 'holiday_dates')) {
                $table->dropColumn('holiday_dates');
            }
        });
    }
};
