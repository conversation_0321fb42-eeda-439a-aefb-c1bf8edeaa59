<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('google_id')->nullable()->unique()->after('email'); // Or after id
            $table->string('contact_number')->nullable()->after('password');
            $table->string('gender')->nullable()->after('contact_number'); // Options: 'male', 'female'
            $table->text('interests')->nullable()->after('gender');
            $table->timestamp('paid_at')->nullable()->after('interests'); // For payment simulation later
            // Make password nullable if users can register without it (e.g. socialite only)
            // <PERSON><PERSON>'s default migration makes password non-nullable.
            // Our GoogleLoginController generates a password, so this should be fine.
            // If we want to allow users to *only* use Google and never set a password:
            // $table->string('password')->nullable()->change(); // This would require DBAL
            // For now, let's stick to what <PERSON><PERSON> provides and ensure GoogleLoginController sets a password.
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['google_id', 'contact_number', 'gender', 'interests', 'paid_at']);
            // If password nullability was changed, revert it here.
            // $table->string('password')->nullable(false)->change();
        });
    }
};
