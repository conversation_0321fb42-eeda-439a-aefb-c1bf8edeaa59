<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained('time_spending_bookings')->onDelete('cascade');
            $table->foreignId('reviewer_id')->constrained('users')->onDelete('cascade'); // User giving the review
            $table->foreignId('reviewee_id')->constrained('users')->onDelete('cascade'); // User receiving the review
            $table->integer('rating')->unsigned()->comment('Rating from 1 to 5 stars');
            $table->text('review_text')->nullable()->comment('Optional text review');
            $table->boolean('is_anonymous')->default(false)->comment('Whether review is anonymous');
            $table->boolean('is_approved')->default(true)->comment('Admin approval status');
            $table->boolean('is_flagged')->default(false)->comment('Flagged for inappropriate content');
            $table->text('admin_notes')->nullable()->comment('Admin notes for moderation');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['reviewee_id', 'is_approved']);
            $table->index(['booking_id']);
            $table->index(['rating']);
            $table->index(['created_at']);
            
            // Ensure one review per user per booking
            $table->unique(['booking_id', 'reviewer_id'], 'unique_booking_reviewer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings_reviews');
    }
};
