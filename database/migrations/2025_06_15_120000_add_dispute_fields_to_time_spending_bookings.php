<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            // Add dispute type and status fields
            if (!Schema::hasColumn('time_spending_bookings', 'dispute_type')) {
                $table->enum('dispute_type', ['no_show', 'service_issue', 'payment_issue', 'other'])->nullable()->after('disputed_by');
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'dispute_status')) {
                $table->enum('dispute_status', ['pending', 'investigating', 'resolved', 'rejected'])->nullable()->after('dispute_type');
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'dispute_evidence')) {
                $table->json('dispute_evidence')->nullable()->after('dispute_status')->comment('Photos, messages, or other evidence');
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'admin_notes')) {
                $table->text('admin_notes')->nullable()->after('dispute_evidence')->comment('Admin notes for dispute resolution');
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'resolved_at')) {
                $table->timestamp('resolved_at')->nullable()->after('admin_notes');
            }
            
            if (!Schema::hasColumn('time_spending_bookings', 'resolved_by')) {
                $table->foreignId('resolved_by')->nullable()->constrained('users')->onDelete('set null')->after('resolved_at');
            }
            
            // Add index for dispute queries
            $table->index(['dispute_status', 'disputed_at'], 'dispute_status_idx');
            $table->index(['dispute_type', 'dispute_status'], 'dispute_type_status_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('time_spending_bookings', function (Blueprint $table) {
            $table->dropIndex('dispute_status_idx');
            $table->dropIndex('dispute_type_status_idx');
            $table->dropColumn([
                'dispute_type',
                'dispute_status',
                'dispute_evidence',
                'admin_notes',
                'resolved_at',
                'resolved_by'
            ]);
        });
    }
};
