<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Only seed essential configuration data
        $this->call([
            SettingsSeeder::class,
            FeatureSeeder::class,
            EssentialDataSeeder::class,
            SubscriptionPlanSeeder::class, // Keep subscription plans
        ]);

        // Essential data seeded silently
    }
}
