<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Google OAuth Settings
        Setting::firstOrCreate(['key' => 'google_client_id'], [
            'value' => env('GOOGLE_CLIENT_ID'),
            'group' => 'auth',
            'type' => 'text',
            'label' => 'Google Client ID',
            'description' => 'Client ID for Google OAuth authentication'
        ]);

        Setting::firstOrCreate(['key' => 'google_client_secret'], [
            'value' => env('GOOGLE_CLIENT_SECRET'),
            'group' => 'auth',
            'type' => 'text',
            'label' => 'Google Client Secret',
            'description' => 'Client Secret for Google OAuth authentication'
        ]);

        Setting::firstOrCreate(['key' => 'google_redirect_uri'], [
            'value' => env('GOOGLE_REDIRECT_URI'),
            'group' => 'auth',
            'type' => 'text',
            'label' => 'Google Redirect URI',
            'description' => 'Redirect URI for Google OAuth authentication'
        ]);

        // Razorpay Settings
        Setting::firstOrCreate(['key' => 'razorpay_key_id'], [
            'value' => env('RAZORPAY_KEY_ID'),
            'group' => 'payment',
            'type' => 'text',
            'label' => 'Razorpay Key ID',
            'description' => 'Key ID for Razorpay payment gateway'
        ]);

        Setting::firstOrCreate(['key' => 'razorpay_key_secret'], [
            'value' => env('RAZORPAY_KEY_SECRET'),
            'group' => 'payment',
            'type' => 'text',
            'label' => 'Razorpay Key Secret',
            'description' => 'Key Secret for Razorpay payment gateway'
        ]);

        // Firebase Cloud Messaging Settings
        Setting::firstOrCreate(['key' => 'firebase_project_id'], [
            'value' => env('FIREBASE_PROJECT_ID'),
            'group' => 'notification',
            'type' => 'text',
            'label' => 'Firebase Project ID',
            'description' => 'Project ID for Firebase Cloud Messaging'
        ]);

        Setting::firstOrCreate(['key' => 'firebase_client_email'], [
            'value' => env('FIREBASE_CLIENT_EMAIL'),
            'group' => 'notification',
            'type' => 'text',
            'label' => 'Firebase Client Email',
            'description' => 'Client Email for Firebase Cloud Messaging'
        ]);

        Setting::firstOrCreate(['key' => 'firebase_private_key'], [
            'value' => env('FIREBASE_PRIVATE_KEY'),
            'group' => 'notification',
            'type' => 'textarea',
            'label' => 'Firebase Private Key',
            'description' => 'Private Key for Firebase Cloud Messaging'
        ]);

        // Logo Settings (these will be managed through file uploads)
        Setting::firstOrCreate(['key' => 'header_logo'], [
            'value' => null,
            'group' => 'branding',
            'type' => 'file',
            'label' => 'Header Logo',
            'description' => 'Logo displayed in the header navigation'
        ]);

        Setting::firstOrCreate(['key' => 'footer_logo'], [
            'value' => null,
            'group' => 'branding',
            'type' => 'file',
            'label' => 'Footer Logo',
            'description' => 'Logo displayed in the footer'
        ]);

        Setting::firstOrCreate(['key' => 'mobile_icon'], [
            'value' => null,
            'group' => 'branding',
            'type' => 'file',
            'label' => 'Mobile App Icon',
            'description' => 'Icon used for PWA app and mobile installations (recommended: 512x512px PNG)'
        ]);

        Setting::firstOrCreate(['key' => 'favicon'], [
            'value' => null,
            'group' => 'branding',
            'type' => 'file',
            'label' => 'Favicon',
            'description' => 'Website favicon (32x32px recommended)'
        ]);

        Setting::firstOrCreate(['key' => 'copyright_text'], [
            'value' => '© ' . date('Y') . ' ' . config('app.name', 'SettingWala') . '. All rights reserved. Made with ❤️ for finding love.',
            'group' => 'branding',
            'type' => 'textarea',
            'label' => 'Copyright Text',
            'description' => 'Footer copyright text. You can use {year} for current year and {app_name} for app name.'
        ]);

        // PWA Theme Settings
        Setting::firstOrCreate(['key' => 'theme_color'], [
            'value' => '#C9B6E4',
            'group' => 'branding',
            'type' => 'color',
            'label' => 'PWA Theme Color',
            'description' => 'Primary theme color for PWA and browser address bar. This color will be used when the app is installed on mobile devices.'
        ]);

        Setting::firstOrCreate(['key' => 'background_color'], [
            'value' => '#ffffff',
            'group' => 'branding',
            'type' => 'color',
            'label' => 'PWA Background Color',
            'description' => 'Background color for PWA splash screen and app background.'
        ]);

        // Payment & Commission Settings
        Setting::firstOrCreate(['key' => 'platform_fee'], [
            'value' => '0',
            'group' => 'payment',
            'type' => 'number',
            'label' => 'Platform Fee (₹)',
            'description' => 'Fixed platform fee in INR applied to all time spending bookings. This fee is added to the base amount.'
        ]);

        Setting::firstOrCreate(['key' => 'commission_percentage'], [
            'value' => '10',
            'group' => 'payment',
            'type' => 'number',
            'label' => 'Commission Percentage (%)',
            'description' => 'Commission percentage deducted from provider earnings for time spending services. Enter value between 0-100.'
        ]);

        // Security Settings
        Setting::firstOrCreate(['key' => 'custom_admin_url'], [
            'value' => '',
            'group' => 'security',
            'type' => 'text',
            'label' => 'Custom Admin URL Path',
            'description' => 'Set a custom URL path for admin panel access (e.g., "my-secret-admin"). Leave empty to use default /admin/ path. Only alphanumeric characters, hyphens, and underscores allowed.'
        ]);
    }
}
