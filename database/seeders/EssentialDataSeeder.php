<?php

namespace Database\Seeders;

use App\Models\Feature;
use App\Models\MeetingAddress;
use Illuminate\Database\Seeder;

class EssentialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Enable essential features that should be active by default
        $this->enableEssentialFeatures();

        // Create sample meeting addresses if none exist
        $this->createSampleMeetingAddresses();

        // Essential data seeded silently
    }

    /**
     * Enable essential features for the app to function properly
     */
    private function enableEssentialFeatures(): void
    {
        $essentialFeatures = [
            'notifications' => true,
            'meeting_events' => true,
            'gallery' => true, // Enable gallery by default
        ];

        // Advanced features that should be disabled by default
        $advancedFeatures = [
            'time_spending' => false,
            'partner_swapping' => false, // Couple Activity
            'sugar_partner' => false,
        ];

        // Merge all features
        $allFeatures = array_merge($essentialFeatures, $advancedFeatures);

        foreach ($allFeatures as $featureName => $enabled) {
            $feature = Feature::where('name', $featureName)->first();
            if ($feature) {
                $feature->update(['is_enabled' => $enabled]);
                $this->command->line("✅ {$feature->label} feature " . ($enabled ? 'enabled' : 'disabled'));
            }
        }

        // Note about chat system
        $this->command->line("ℹ️  Chat system is managed by Time Spending feature");
    }

    /**
     * Create sample meeting addresses for the events system
     */
    private function createSampleMeetingAddresses(): void
    {
        // Only create if no meeting addresses exist
        if (MeetingAddress::count() > 0) {
            $this->command->line('Meeting addresses already exist, skipping creation.');
            return;
        }

        $meetingAddresses = [
            [
                'title' => 'Coffee Meet & Greet',
                'description' => 'Join us for a casual coffee meetup where singles can connect in a relaxed atmosphere. Perfect for making new friends and potential romantic connections.',
                'rules_and_regulations' => '<ul><li>Be respectful to all participants</li><li>Arrive on time</li><li>No inappropriate behavior</li><li>Exchange contacts only with mutual consent</li></ul>',
                'payment_amount_boys' => 299.00,
                'payment_amount_girls' => 199.00,
                'is_event_enabled' => true,
                'event_date' => now()->addDays(7)->setTime(18, 0, 0),
                'location' => 'Cafe Coffee Day, Connaught Place, New Delhi',
                'latitude' => 28.6315,
                'longitude' => 77.2167,
            ],
            [
                'title' => 'Speed Dating Night',
                'description' => 'Experience the excitement of speed dating! Meet multiple potential matches in one evening through structured 5-minute conversations.',
                'rules_and_regulations' => '<ul><li>Dress code: Smart casual</li><li>No phones during dating rounds</li><li>Be punctual for all rounds</li><li>Respect the time limits</li><li>Have fun and be yourself!</li></ul>',
                'payment_amount_boys' => 599.00,
                'payment_amount_girls' => 399.00,
                'is_event_enabled' => true,
                'event_date' => now()->addDays(14)->setTime(19, 30, 0),
                'location' => 'The Imperial Hotel, Janpath, New Delhi',
                'latitude' => 28.6139,
                'longitude' => 77.2090,
            ],
            [
                'title' => 'Outdoor Adventure Meetup',
                'description' => 'Join fellow adventure enthusiasts for a day of hiking and outdoor activities. Great way to meet like-minded people who love nature and adventure.',
                'rules_and_regulations' => '<ul><li>Wear comfortable hiking shoes</li><li>Bring water and snacks</li><li>Follow safety guidelines</li><li>Stay with the group</li><li>Respect the environment</li></ul>',
                'payment_amount_boys' => 399.00,
                'payment_amount_girls' => 299.00,
                'is_event_enabled' => true,
                'event_date' => now()->addDays(21)->setTime(9, 0, 0),
                'location' => 'Lodhi Gardens, New Delhi',
                'latitude' => 28.5918,
                'longitude' => 77.2273,
            ],
            [
                'title' => 'Cultural Evening & Dinner',
                'description' => 'An elegant evening featuring cultural performances, fine dining, and opportunities to connect with educated professionals in a sophisticated setting.',
                'rules_and_regulations' => '<ul><li>Formal dress code required</li><li>RSVP mandatory</li><li>No outside food or drinks</li><li>Maintain decorum throughout the event</li><li>Photography allowed in designated areas only</li></ul>',
                'payment_amount_boys' => 899.00,
                'payment_amount_girls' => 699.00,
                'is_event_enabled' => true,
                'event_date' => now()->addDays(28)->setTime(19, 0, 0),
                'location' => 'India Habitat Centre, Lodhi Road, New Delhi',
                'latitude' => 28.5706,
                'longitude' => 77.2294,
            ],
            [
                'title' => 'Game Night Social',
                'description' => 'Fun-filled evening with board games, card games, and interactive activities. Perfect for breaking the ice and getting to know each other in a playful environment.',
                'rules_and_regulations' => '<ul><li>Participate actively in games</li><li>Be a good sport - win or lose gracefully</li><li>No cheating or unfair play</li><li>Help with setup and cleanup</li><li>Respect game rules and other players</li></ul>',
                'payment_amount_boys' => 249.00,
                'payment_amount_girls' => 199.00,
                'is_event_enabled' => true,
                'event_date' => now()->addDays(10)->setTime(18, 30, 0),
                'location' => 'Select City Walk Mall, Saket, New Delhi',
                'latitude' => 28.5245,
                'longitude' => 77.2066,
            ],
        ];

        foreach ($meetingAddresses as $addressData) {
            MeetingAddress::create($addressData);
            $this->command->line("✅ Created meeting address: {$addressData['title']}");
        }
    }
}
