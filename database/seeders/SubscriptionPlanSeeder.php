<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => '1 Month Premium',
                'duration_months' => 1,
                'amount' => 99.00,
                'original_price' => 99.00,
                'discount_price' => null,
                'description' => 'Perfect for trying out our Time Spending Service. Get full access to all features for one month.',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => '3 Month Premium',
                'duration_months' => 3,
                'amount' => 249.00, // Save 16% compared to monthly
                'original_price' => 297.00, // 3 × ₹99
                'discount_price' => 249.00,
                'description' => 'Save 16% with our 3-month subscription. Great value for regular users.',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => '6 Month Premium',
                'duration_months' => 6,
                'amount' => 399.00, // Save 33% compared to monthly
                'original_price' => 594.00, // 6 × ₹99
                'discount_price' => 399.00,
                'description' => 'Save 33% with our 6-month subscription. Perfect for serious users who want long-term visibility.',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => '1 Year Premium',
                'duration_months' => 12,
                'amount' => 499.00, // Save 58% compared to monthly - Maximum ₹499 per year
                'original_price' => 1188.00, // 12 × ₹99
                'discount_price' => 499.00,
                'description' => 'Ultimate value plan! Get a full year of premium access with maximum 58% savings. Only ₹499 for the entire year!',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($plans as $planData) {
            SubscriptionPlan::firstOrCreate(
                [
                    'name' => $planData['name'],
                    'duration_months' => $planData['duration_months']
                ],
                $planData
            );
        }
    }
}
