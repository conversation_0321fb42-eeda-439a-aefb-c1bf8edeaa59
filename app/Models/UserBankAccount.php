<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserBankAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'account_holder_name',
        'account_number',
        'ifsc_code',
        'bank_name',
        'account_type',
        'payment_method_type', // 'bank' or 'gpay'
        'gpay_number',
        'gpay_upi',
        'is_verified',
        'is_primary',
        'razorpay_fund_account_id',
        'verification_data',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'is_primary' => 'boolean',
        'verification_data' => 'array',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'masked_account_number',
        'display_name',
    ];

    /**
     * Get the user that owns the bank account.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get withdrawal requests for this bank account.
     */
    public function withdrawalRequests(): HasMany
    {
        return $this->hasMany(WithdrawalRequest::class, 'bank_account_id');
    }

    /**
     * Get masked account number for display.
     */
    public function getMaskedAccountNumberAttribute(): string
    {
        if ($this->payment_method_type === 'gpay') {
            $number = $this->gpay_number;
            $length = strlen($number);

            if ($length <= 4) {
                return str_repeat('*', $length);
            }

            return str_repeat('*', $length - 4) . substr($number, -4);
        }

        $accountNumber = $this->account_number;
        $length = strlen($accountNumber);

        if ($length <= 4) {
            return str_repeat('*', $length);
        }

        return str_repeat('*', $length - 4) . substr($accountNumber, -4);
    }

    /**
     * Get full bank details for display.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->payment_method_type === 'gpay') {
            return "G-Pay - {$this->masked_account_number}";
        }

        return "{$this->bank_name} - {$this->masked_account_number}";
    }

    /**
     * Set primary bank account for user.
     */
    public function setPrimary(): void
    {
        // Remove primary status from other accounts
        static::where('user_id', $this->user_id)
              ->where('id', '!=', $this->id)
              ->update(['is_primary' => false]);
        
        // Set this account as primary
        $this->update(['is_primary' => true]);
    }

    /**
     * Validate IFSC code format.
     */
    public static function validateIfscCode(string $ifsc): bool
    {
        return preg_match('/^[A-Z]{4}0[A-Z0-9]{6}$/', $ifsc);
    }

    /**
     * Get bank name from IFSC code.
     */
    public static function getBankNameFromIfsc(string $ifsc): ?string
    {
        $bankCodes = [
            'SBIN' => 'State Bank of India',
            'HDFC' => 'HDFC Bank',
            'ICIC' => 'ICICI Bank',
            'AXIS' => 'Axis Bank',
            'PUNB' => 'Punjab National Bank',
            'UBIN' => 'Union Bank of India',
            'CNRB' => 'Canara Bank',
            'IOBA' => 'Indian Overseas Bank',
            'BKID' => 'Bank of India',
            'MAHB' => 'Bank of Maharashtra',
            'ALLA' => 'Allahabad Bank',
            'IDIB' => 'Indian Bank',
            'CORP' => 'Corporation Bank',
            'VIJB' => 'Vijaya Bank',
            'ANDB' => 'Andhra Bank',
            'UTIB' => 'Axis Bank',
            'KKBK' => 'Kotak Mahindra Bank',
            'YESB' => 'Yes Bank',
            'INDB' => 'IndusInd Bank',
            'FDRL' => 'Federal Bank',
            'KARB' => 'Karnataka Bank',
            'NKGS' => 'NKGSB Co-operative Bank',
            'JSBL' => 'Jammu & Kashmir Bank',
            'TMBL' => 'Tamilnad Mercantile Bank',
            'SIBL' => 'South Indian Bank',
            'KVBL' => 'Karur Vysya Bank',
            'LAVB' => 'Lakshmi Vilas Bank',
            'DCBL' => 'Development Credit Bank',
            'RATN' => 'RBL Bank',
            'IBKL' => 'IDBI Bank',
        ];

        $bankCode = substr($ifsc, 0, 4);
        return $bankCodes[$bankCode] ?? null;
    }

    /**
     * Scope for verified accounts only.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for primary account.
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Check if account can be used for withdrawal.
     */
    public function canWithdraw(): bool
    {
        return $this->is_verified && $this->user->is_active;
    }

    /**
     * Get total withdrawn amount from this account.
     */
    public function getTotalWithdrawnAttribute(): float
    {
        return $this->withdrawalRequests()
                    ->where('status', 'completed')
                    ->sum('amount');
    }
}
