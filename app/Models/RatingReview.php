<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class RatingReview extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'ratings_reviews';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'booking_id',
        'reviewer_id',
        'reviewee_id',
        'rating',
        'review_text',
        'is_anonymous',
        'is_approved',
        'is_flagged',
        'admin_notes',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_anonymous' => 'boolean',
        'is_approved' => 'boolean',
        'is_flagged' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the booking that this review belongs to.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(TimeSpendingBooking::class, 'booking_id');
    }

    /**
     * Get the user who wrote the review.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    /**
     * Get the user who received the review.
     */
    public function reviewee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewee_id');
    }

    /**
     * Get formatted time ago string.
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get star rating as HTML.
     */
    public function getStarRatingHtmlAttribute(): string
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '<svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
            } else {
                $stars .= '<svg class="w-4 h-4 text-gray-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
            }
        }
        return $stars;
    }

    /**
     * Scope to get approved reviews only.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get reviews for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('reviewee_id', $userId);
    }

    /**
     * Scope to get reviews by a specific user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('reviewer_id', $userId);
    }

    /**
     * Get average rating for a user.
     */
    public static function getAverageRatingForUser($userId): float
    {
        return self::approved()
            ->forUser($userId)
            ->avg('rating') ?: 0;
    }

    /**
     * Get total reviews count for a user.
     */
    public static function getTotalReviewsForUser($userId): int
    {
        return self::approved()
            ->forUser($userId)
            ->count();
    }

    /**
     * Get rating distribution for a user.
     */
    public static function getRatingDistributionForUser($userId): array
    {
        $distribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $distribution[$i] = self::approved()
                ->forUser($userId)
                ->where('rating', $i)
                ->count();
        }
        return $distribution;
    }

    /**
     * Check if a user can review a booking.
     */
    public static function canReviewBooking($bookingId, $reviewerId): bool
    {
        // Check if review already exists
        $existingReview = self::where('booking_id', $bookingId)
            ->where('reviewer_id', $reviewerId)
            ->first();

        if ($existingReview) {
            return false;
        }

        // Check if booking is completed and user is part of it
        $booking = TimeSpendingBooking::find($bookingId);
        if (!$booking) {
            return false;
        }

        // Check if user is part of the booking
        if ($booking->client_id !== $reviewerId && $booking->provider_id !== $reviewerId) {
            return false;
        }

        // Check if booking is completed (past end time)
        $durationHours = is_numeric($booking->duration_hours) ? (float) $booking->duration_hours : 1.0;
        $bookingEndTime = $booking->booking_date->copy()->addHours($durationHours);
        if (now()->lt($bookingEndTime)) {
            return false;
        }

        // Check if booking was accepted and paid
        if ($booking->provider_status !== 'accepted' || $booking->payment_status !== 'paid') {
            return false;
        }

        return true;
    }

    /**
     * Create a review for a booking.
     */
    public static function createReview($bookingId, $reviewerId, $rating, $reviewText = null, $isAnonymous = false): ?self
    {
        if (!self::canReviewBooking($bookingId, $reviewerId)) {
            return null;
        }

        $booking = TimeSpendingBooking::find($bookingId);
        $revieweeId = $booking->client_id === $reviewerId ? $booking->provider_id : $booking->client_id;

        return self::create([
            'booking_id' => $bookingId,
            'reviewer_id' => $reviewerId,
            'reviewee_id' => $revieweeId,
            'rating' => $rating,
            'review_text' => $reviewText,
            'is_anonymous' => $isAnonymous,
        ]);
    }
}
