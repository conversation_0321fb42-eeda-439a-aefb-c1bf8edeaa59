<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdminRevenue extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_id',
        'subscription_id',
        'platform_fee',
        'commission_amount',
        'total_admin_earnings',
        'revenue_type',
        'description',
        'metadata',
    ];

    protected $casts = [
        'platform_fee' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'total_admin_earnings' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * Get the booking associated with this revenue.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(TimeSpendingBooking::class, 'booking_id');
    }

    /**
     * Get the subscription associated with this revenue.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class, 'subscription_id');
    }

    /**
     * Create admin revenue record for a booking.
     */
    public static function createFromBooking(TimeSpendingBooking $booking): self
    {
        // Check if AdminRevenue record already exists for this booking
        $existingRevenue = self::where('booking_id', $booking->id)->first();
        if ($existingRevenue) {
            return $existingRevenue;
        }

        $platformFee = $booking->platform_fee ?? 0;
        $commissionAmount = $booking->commission_amount ?? 0;
        $totalAdminEarnings = $platformFee + $commissionAmount;

        return self::create([
            'booking_id' => $booking->id,
            'platform_fee' => $platformFee,
            'commission_amount' => $commissionAmount,
            'total_admin_earnings' => $totalAdminEarnings,
            'revenue_type' => 'booking_payment',
            'description' => "Admin revenue from booking #{$booking->id} - Platform fee: ₹{$platformFee}, Commission: ₹{$commissionAmount}",
            'metadata' => [
                'client_id' => $booking->client_id,
                'provider_id' => $booking->provider_id,
                'base_amount' => $booking->base_amount,
                'total_amount' => $booking->total_amount,
                'commission_percentage' => $booking->commission_percentage,
                'booking_date' => $booking->booking_date->toISOString(),
            ],
        ]);
    }

    /**
     * Create admin revenue record for a subscription.
     */
    public static function createFromSubscription(UserSubscription $subscription): self
    {
        // For subscriptions, the entire amount is admin revenue (no commission split)
        $totalAdminEarnings = $subscription->amount_paid;

        return self::create([
            'subscription_id' => $subscription->id,
            'platform_fee' => 0, // No platform fee for subscriptions
            'commission_amount' => 0, // No commission for subscriptions
            'total_admin_earnings' => $totalAdminEarnings,
            'revenue_type' => 'subscription_payment',
            'description' => "Subscription revenue from {$subscription->subscriptionPlan->name} - User: {$subscription->user->name} - Amount: ₹{$totalAdminEarnings}",
            'metadata' => [
                'user_id' => $subscription->user_id,
                'subscription_plan_id' => $subscription->subscription_plan_id,
                'plan_name' => $subscription->subscriptionPlan->name,
                'plan_duration' => $subscription->subscriptionPlan->duration_months,
                'payment_method' => $subscription->payment_method,
                'wallet_amount_used' => $subscription->wallet_amount_used,
                'razorpay_amount_paid' => $subscription->razorpay_amount_paid,
                'subscription_status' => $subscription->status,
                'starts_at' => $subscription->starts_at->toISOString(),
                'expires_at' => $subscription->expires_at->toISOString(),
            ],
        ]);
    }

    /**
     * Get total platform revenue.
     */
    public static function getTotalPlatformRevenue(): float
    {
        return self::sum('total_admin_earnings');
    }

    /**
     * Get total platform fees collected.
     */
    public static function getTotalPlatformFees(): float
    {
        return self::sum('platform_fee');
    }

    /**
     * Get total commission earned.
     */
    public static function getTotalCommission(): float
    {
        return self::sum('commission_amount');
    }

    /**
     * Get revenue statistics for a date range.
     */
    public static function getRevenueStats($startDate = null, $endDate = null): array
    {
        $query = self::query();

        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $stats = $query->selectRaw('
            SUM(platform_fee) as total_platform_fees,
            SUM(commission_amount) as total_commission,
            SUM(total_admin_earnings) as total_revenue,
            COUNT(*) as total_transactions
        ')->first();

        return [
            'total_platform_fees' => (float) ($stats->total_platform_fees ?? 0),
            'total_commission' => (float) ($stats->total_commission ?? 0),
            'total_revenue' => (float) ($stats->total_revenue ?? 0),
            'total_transactions' => (int) ($stats->total_transactions ?? 0),
        ];
    }
}
