<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SugarPartnerRejection extends Model
{
    use HasFactory;

    protected $fillable = [
        'exchange_id',
        'rejector_id',
        'rejected_user_id',
        'rejection_type',
        'rejection_reason',
        'admin_note',
        'notification_sent',
        'notification_sent_at',
    ];

    protected $casts = [
        'notification_sent' => 'boolean',
        'notification_sent_at' => 'datetime',
    ];

    /**
     * Get the exchange this rejection belongs to.
     */
    public function exchange(): BelongsTo
    {
        return $this->belongsTo(SugarPartnerExchange::class, 'exchange_id');
    }

    /**
     * Get the user who made the rejection.
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejector_id');
    }

    /**
     * Get the user who was rejected.
     */
    public function rejectedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_user_id');
    }

    /**
     * Create a rejection record.
     */
    public static function createRejection(
        SugarPartnerExchange $exchange,
        int $rejectorId,
        string $rejectionType,
        ?string $rejectionReason = null,
        ?string $adminNote = null
    ): self {
        $rejectedUserId = $exchange->getOtherUser($rejectorId)->id;

        $rejection = self::create([
            'exchange_id' => $exchange->id,
            'rejector_id' => $rejectorId,
            'rejected_user_id' => $rejectedUserId,
            'rejection_type' => $rejectionType,
            'rejection_reason' => $rejectionReason,
            'admin_note' => $adminNote,
        ]);

        // Handle hard reject - create permanent block
        if ($rejectionType === 'hard_reject') {
            $rejection->createHardRejectRecord();
        }

        // Send notification
        $rejection->sendRejectionNotification();

        // Check if both users have responded
        $exchange->markResponsesCompleted();

        return $rejection;
    }

    /**
     * Create a hard reject record for permanent blocking.
     */
    protected function createHardRejectRecord(): void
    {
        // Ensure user1_id is always the smaller ID for consistency
        $user1Id = min($this->rejector_id, $this->rejected_user_id);
        $user2Id = max($this->rejector_id, $this->rejected_user_id);

        SugarPartnerHardReject::firstOrCreate([
            'user1_id' => $user1Id,
            'user2_id' => $user2Id,
        ], [
            'rejector_id' => $this->rejector_id,
            'original_exchange_id' => $this->exchange_id,
            'rejection_reason' => $this->rejection_reason,
            'admin_note' => $this->admin_note,
        ]);
    }

    /**
     * Send rejection notification to the rejected user.
     */
    public function sendRejectionNotification(): void
    {
        if ($this->notification_sent) {
            return;
        }

        $rejectorName = $this->rejector->name;
        $rejectionTypeDisplay = $this->getRejectionTypeDisplayName();

        $title = "Sugar Partner Response: {$rejectionTypeDisplay}";
        $message = 'Sugar Partner Exchange';
        $body = $this->buildNotificationMessage($rejectorName, $rejectionTypeDisplay);

        Notification::create([
            'user_id' => $this->rejected_user_id,
            'type' => 'sugar_partner_response_received',
            'title' => $title,
            'message' => $message,
            'body' => $body,
            'data' => [
                'exchange_id' => $this->exchange_id,
                'rejector_id' => $this->rejector_id,
                'rejector_name' => $rejectorName,
                'rejection_type' => $this->rejection_type,
                'rejection_reason' => $this->rejection_reason,
                'action_url' => route('profile.edit', ['tab' => 'sugar-partner']),
                'action_text' => 'View Exchange Details'
            ],
        ]);

        $this->update([
            'notification_sent' => true,
            'notification_sent_at' => now(),
        ]);
    }

    /**
     * Build notification message based on rejection type.
     */
    protected function buildNotificationMessage(string $rejectorName, string $rejectionTypeDisplay): string
    {
        $baseMessage = "{$rejectorName} has responded to your Sugar Partner profile exchange with: {$rejectionTypeDisplay}";

        if ($this->rejection_reason) {
            $baseMessage .= "\n\nReason: {$this->rejection_reason}";
        }

        if ($this->rejection_type === 'hard_reject') {
            $baseMessage .= "\n\nNote: This is a permanent rejection. Future profile exchanges with this user are not permitted.";
        }

        return $baseMessage;
    }

    /**
     * Get rejection type display name.
     */
    public function getRejectionTypeDisplayName(): string
    {
        return match($this->rejection_type) {
            'accept' => 'Accepted',
            'soft_reject' => 'Soft Reject',
            'hard_reject' => 'Hard Reject',
            default => ucfirst(str_replace('_', ' ', $this->rejection_type))
        };
    }

    /**
     * Get rejection type badge class for UI.
     */
    public function getRejectionTypeBadgeClass(): string
    {
        return match($this->rejection_type) {
            'accept' => 'bg-success',
            'soft_reject' => 'bg-warning',
            'hard_reject' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    /**
     * Get soft rejection count for a user pair.
     */
    public static function getSoftRejectionCount(int $user1Id, int $user2Id): int
    {
        return self::where('rejection_type', 'soft_reject')
            ->where(function ($query) use ($user1Id, $user2Id) {
                $query->where(function ($q) use ($user1Id, $user2Id) {
                    $q->where('rejector_id', $user1Id)->where('rejected_user_id', $user2Id);
                })->orWhere(function ($q) use ($user1Id, $user2Id) {
                    $q->where('rejector_id', $user2Id)->where('rejected_user_id', $user1Id);
                });
            })
            ->count();
    }

    /**
     * Get rejection history between two users.
     */
    public static function getRejectionHistory(int $user1Id, int $user2Id): array
    {
        $rejections = self::where(function ($query) use ($user1Id, $user2Id) {
            $query->where(function ($q) use ($user1Id, $user2Id) {
                $q->where('rejector_id', $user1Id)->where('rejected_user_id', $user2Id);
            })->orWhere(function ($q) use ($user1Id, $user2Id) {
                $q->where('rejector_id', $user2Id)->where('rejected_user_id', $user1Id);
            });
        })
        ->with(['rejector', 'rejectedUser'])
        ->orderBy('created_at', 'desc')
        ->get();

        $softRejectCount = $rejections->where('rejection_type', 'soft_reject')->count();
        $hasHardReject = $rejections->where('rejection_type', 'hard_reject')->isNotEmpty();

        return [
            'rejections' => $rejections,
            'soft_reject_count' => $softRejectCount,
            'has_hard_reject' => $hasHardReject,
            'total_rejections' => $rejections->count(),
        ];
    }
}
