<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'amount_paid',
        'wallet_amount_used',
        'razorpay_amount_paid',
        'payment_method',
        'razorpay_payment_id',
        'razorpay_order_id',
        'razorpay_signature',
        'status',
        'starts_at',
        'expires_at',
        'paid_at',
        'metadata',
    ];

    protected $casts = [
        'amount_paid' => 'decimal:2',
        'wallet_amount_used' => 'decimal:2',
        'razorpay_amount_paid' => 'decimal:2',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'paid_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->expires_at > now();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Get days remaining in subscription.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }
        
        return max(0, now()->diffInDays($this->expires_at, false));
    }

    /**
     * Get formatted expiry date.
     */
    public function getFormattedExpiryDateAttribute(): string
    {
        return $this->expires_at->format('M d, Y');
    }

    /**
     * Get user's active time spending subscription.
     */
    public static function getActiveSubscription(int $userId): ?self
    {
        return self::where('user_id', $userId)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderBy('expires_at', 'desc')
            ->first();
    }

    /**
     * Get user's queued time spending subscription.
     */
    public static function getQueuedSubscription(int $userId): ?self
    {
        return self::where('user_id', $userId)
            ->where('status', 'queued')
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Check if subscription is queued.
     */
    public function isQueued(): bool
    {
        return $this->status === 'queued';
    }

    /**
     * Create a new subscription for user.
     */
    public static function createSubscription(
        int $userId,
        int $planId,
        float $amountPaid,
        float $walletUsed = 0,
        float $razorpayPaid = 0,
        ?string $paymentMethod = null,
        ?array $razorpayData = null,
        bool $isQueued = false
    ): self {
        $plan = SubscriptionPlan::findOrFail($planId);

        if ($isQueued) {
            // For queued subscriptions, start date is when current subscription expires
            $activeSubscription = self::getActiveSubscription($userId);
            $startsAt = $activeSubscription ? $activeSubscription->expires_at : now();
            $status = 'queued';
        } else {
            // For immediate subscriptions, start now
            $startsAt = now();
            $status = 'active';
        }

        $expiresAt = $startsAt->copy()->addMonths($plan->duration_months);

        return self::create([
            'user_id' => $userId,
            'subscription_plan_id' => $planId,
            'amount_paid' => $amountPaid,
            'wallet_amount_used' => $walletUsed,
            'razorpay_amount_paid' => $razorpayPaid,
            'payment_method' => $paymentMethod,
            'razorpay_payment_id' => $razorpayData['payment_id'] ?? null,
            'razorpay_order_id' => $razorpayData['order_id'] ?? null,
            'razorpay_signature' => $razorpayData['signature'] ?? null,
            'status' => $status,
            'starts_at' => $startsAt,
            'expires_at' => $expiresAt,
            'paid_at' => now(),
            'metadata' => [
                'plan_name' => $plan->name,
                'plan_duration' => $plan->duration_months,
                'created_via' => 'time_spending_activation',
                'is_queued' => $isQueued
            ]
        ]);
    }

    /**
     * Mark subscription as expired.
     */
    public function markAsExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Extend subscription by adding more months.
     */
    public function extend(int $additionalMonths): void
    {
        $this->update([
            'expires_at' => $this->expires_at->addMonths($additionalMonths)
        ]);
    }
}
