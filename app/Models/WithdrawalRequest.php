<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class WithdrawalRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bank_account_id',
        'amount',
        'processing_fee',
        'net_amount',
        'status',
        'razorpay_payout_id',
        'transaction_reference',
        'notes',
        'failure_reason',
        'admin_notes',
        'requested_at',
        'processed_at',
        'processed_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'processing_fee' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'requested_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'status_display',
        'status_color',
    ];

    /**
     * Get the user that owns the withdrawal request.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bank account for this withdrawal.
     */
    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(UserBankAccount::class, 'bank_account_id');
    }

    /**
     * Get the admin who processed this withdrawal.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Pending Review',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'processing' => 'info',
            'completed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Check if withdrawal can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending']) && 
               $this->requested_at->diffInHours(now()) < 24;
    }

    /**
     * Cancel the withdrawal request.
     */
    public function cancel(?string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        // No need to refund money since it was never deducted in the new flow
        // Money is only deducted when admin processes the withdrawal

        // Update withdrawal status
        $this->update([
            'status' => 'cancelled',
            'failure_reason' => $reason ?: 'Cancelled by user',
            'processed_at' => now(),
        ]);

        // Create notification
        Notification::create([
            'user_id' => $this->user_id,
            'type' => 'withdrawal_cancelled',
            'title' => 'Withdrawal Cancelled',
            'message' => "Your withdrawal request has been cancelled.",
            'body' => "Your withdrawal request has been cancelled.",
            'data' => [
                'withdrawal_id' => $this->id,
                'amount' => $this->amount,
                'bank_account' => $this->bankAccount->display_name ?? 'Bank Account',
            ],
        ]);

        return true;
    }

    /**
     * Mark withdrawal as completed.
     */
    public function markCompleted(?string $transactionRef = null, ?int $processedBy = null): void
    {
        // Deduct amount from user wallet when admin processes
        $wallet = UserWallet::getOrCreate($this->user_id);
        $wallet->deductMoney(
            $this->amount,
            "Withdrawal processed - Request #{$this->id}",
            null,
            ['withdrawal_processed' => true, 'transaction_reference' => $transactionRef]
        );

        $this->update([
            'status' => 'completed',
            'transaction_reference' => $transactionRef,
            'processed_at' => now(),
            'processed_by' => $processedBy,
        ]);

        // Create notification for user
        Notification::create([
            'user_id' => $this->user_id,
            'type' => 'withdrawal_completed',
            'title' => 'Withdrawal Completed',
            'message' => "Your withdrawal has been completed successfully.",
            'body' => "Your withdrawal has been completed successfully.",
            'data' => [
                'withdrawal_id' => $this->id,
                'amount' => $this->net_amount,
                'bank_account' => $this->bankAccount->display_name,
                'transaction_reference' => $transactionRef,
            ],
        ]);
    }

    /**
     * Mark withdrawal as failed.
     */
    public function markFailed(string $reason, ?int $processedBy = null): void
    {
        // No need to refund money since it was never deducted in the new flow
        // Money is only deducted when admin processes the withdrawal

        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
            'processed_at' => now(),
            'processed_by' => $processedBy,
        ]);

        // Create notification
        Notification::create([
            'user_id' => $this->user_id,
            'type' => 'withdrawal_rejected',
            'title' => 'Withdrawal Request Rejected',
            'message' => "Your withdrawal request has been rejected.",
            'body' => "Your withdrawal request has been rejected.",
            'data' => [
                'withdrawal_id' => $this->id,
                'amount' => $this->amount,
                'failure_reason' => $reason,
                'bank_account' => $this->bankAccount->display_name ?? 'Bank Account',
            ],
        ]);
    }

    /**
     * Get processing time estimate.
     */
    public function getProcessingTimeEstimate(): string
    {
        return \App\Models\Setting::get('withdrawal_processing_time', '1-3 business days');
    }

    /**
     * Scope for pending withdrawals.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for completed withdrawals.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for today's withdrawals.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('requested_at', today());
    }

    /**
     * Get daily withdrawal total for user.
     */
    public static function getDailyWithdrawalTotal(int $userId): float
    {
        return static::where('user_id', $userId)
                    ->today()
                    ->whereIn('status', ['pending', 'processing', 'completed'])
                    ->sum('amount');
    }
}
