<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Models\Feature;
use App\Models\CoupleActivityRequest;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'contact_number',
        'gender',
        'date_of_birth',
        'interests',
        'expectation',
        'interested_in_sugar_partner',
        'sugar_partner_types',
        'sugar_partner_bio',
        'sugar_partner_expectations',
        'paid_at', // For payment simulation
        'is_admin',
        'role',
        'fcm_token',
        'profile_picture',
        'is_suspended',
        // Privacy settings
        'is_public_profile',
        'show_contact_number',
        'show_date_of_birth',
        'hide_dob_year',
        'show_interests_hobbies',
        'show_expectations',
        'show_gallery_images',
        // Time spending settings
        'hourly_rate',
        'currency',
        'is_time_spending_enabled',
        'has_active_time_spending_subscription',
        'time_spending_subscription_expires_at',
        'availability_schedule',
        'holiday_dates',
        'service_location',
        // Couple activity settings
        'is_couple_activity_enabled',
        'couple_activity_status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'paid_at' => 'datetime',
            'date_of_birth' => 'date',
            'is_admin' => 'boolean',
            'is_suspended' => 'boolean',
            'interested_in_sugar_partner' => 'boolean',
            'sugar_partner_types' => 'array',
            // Privacy settings
            'is_public_profile' => 'boolean',
            'show_contact_number' => 'boolean',
            'show_date_of_birth' => 'boolean',
            'hide_dob_year' => 'boolean',
            'show_interests_hobbies' => 'boolean',
            'show_expectations' => 'boolean',
            'show_gallery_images' => 'boolean',
            // Time spending settings
            'hourly_rate' => 'decimal:2',
            'is_time_spending_enabled' => 'boolean',
            'has_active_time_spending_subscription' => 'boolean',
            'time_spending_subscription_expires_at' => 'datetime',
            'availability_schedule' => 'array',
            'holiday_dates' => 'array',
            // Couple activity settings
            'is_couple_activity_enabled' => 'boolean',
        ];
    }

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'profile_picture_url',
    ];

    /**
     * Check if the user is an admin
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->is_admin || $this->isManagementUser();
    }

    /**
     * Check if the user is a management user (admin, super_admin, editor, accountant)
     *
     * @return bool
     */
    public function isManagementUser(): bool
    {
        return in_array($this->role, ['admin', 'super_admin', 'editor', 'accountant']);
    }

    /**
     * Check if the user is a regular user
     *
     * @return bool
     */
    public function isRegularUser(): bool
    {
        return $this->role === 'user' && !$this->is_admin;
    }

    /**
     * Get the user's role display name
     *
     * @return string
     */
    public function getRoleDisplayName(): string
    {
        return match($this->role) {
            'super_admin' => 'Super Admin',
            'admin' => 'Admin',
            'editor' => 'Editor',
            'accountant' => 'Accountant',
            'user' => 'User',
            default => 'User'
        };
    }

    /**
     * Get the user's role badge color
     *
     * @return string
     */
    public function getRoleBadgeColor(): string
    {
        return match($this->role) {
            'super_admin' => 'bg-danger-subtle text-danger',
            'admin' => 'bg-primary-subtle text-primary',
            'editor' => 'bg-info-subtle text-info',
            'accountant' => 'bg-warning-subtle text-warning',
            'user' => 'bg-secondary-subtle text-secondary',
            default => 'bg-secondary-subtle text-secondary'
        };
    }

    /**
     * Get the notifications for the user.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get the count of unread notifications for the user.
     */
    public function getUnreadNotificationCount(): int
    {
        return $this->notifications()->where('is_read', false)->count();
    }

    /**
     * Get formatted unread notification count for display (1-9, then 9+).
     */
    public function getFormattedNotificationCount(): string
    {
        $count = $this->getUnreadNotificationCount();

        if ($count === 0) {
            return '';
        } elseif ($count <= 9) {
            return (string) $count;
        } else {
            return '9+';
        }
    }

    /**
     * Get the uploads for the user.
     */
    public function uploads(): HasMany
    {
        return $this->hasMany(Upload::class);
    }

    /**
     * Get the gallery images for the user.
     */
    public function galleryImages(): HasMany
    {
        return $this->hasMany(UserGallery::class)->active()->ordered();
    }

    /**
     * Get the event payments for the user.
     */
    public function eventPayments(): HasMany
    {
        return $this->hasMany(EventPayment::class);
    }

    /**
     * Get couple activity requests sent by this user.
     */
    public function sentCoupleActivityRequests(): HasMany
    {
        return $this->hasMany(CoupleActivityRequest::class, 'requester_id');
    }

    /**
     * Get couple activity requests received by this user.
     */
    public function receivedCoupleActivityRequests(): HasMany
    {
        return $this->hasMany(CoupleActivityRequest::class, 'partner_id');
    }

    /**
     * Get the time spending bookings made by this user (as client).
     */
    public function clientBookings(): HasMany
    {
        return $this->hasMany(TimeSpendingBooking::class, 'client_id');
    }

    /**
     * Get the time spending bookings for this user (as provider).
     */
    public function providerBookings(): HasMany
    {
        return $this->hasMany(TimeSpendingBooking::class, 'provider_id');
    }

    /**
     * Alias for clientBookings - for payment history.
     */
    public function timeSpendingBookingsAsClient(): HasMany
    {
        return $this->clientBookings();
    }

    /**
     * Get the user's wallet.
     */
    public function wallet(): HasOne
    {
        return $this->hasOne(UserWallet::class);
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(UserSubscription::class)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderBy('expires_at', 'desc');
    }

    /**
     * Get the user's queued subscription.
     */
    public function queuedSubscription(): HasOne
    {
        return $this->hasOne(UserSubscription::class)
            ->where('status', 'queued')
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get the user's wallet transactions.
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get reviews written by this user.
     */
    public function reviewsGiven(): HasMany
    {
        return $this->hasMany(RatingReview::class, 'reviewer_id');
    }

    /**
     * Get reviews received by this user.
     */
    public function reviewsReceived(): HasMany
    {
        return $this->hasMany(RatingReview::class, 'reviewee_id');
    }

    /**
     * Get approved reviews received by this user.
     */
    public function approvedReviewsReceived(): HasMany
    {
        return $this->reviewsReceived()->approved();
    }

    /**
     * Get average rating for this user.
     */
    public function getAverageRating(): float
    {
        return RatingReview::getAverageRatingForUser($this->id);
    }

    /**
     * Get total reviews count for this user.
     */
    public function getTotalReviewsCount(): int
    {
        return RatingReview::getTotalReviewsForUser($this->id);
    }

    /**
     * Get rating distribution for this user.
     */
    public function getRatingDistribution(): array
    {
        return RatingReview::getRatingDistributionForUser($this->id);
    }

    /**
     * Get formatted average rating (e.g., "4.5" or "No ratings").
     */
    public function getFormattedAverageRating(): string
    {
        $average = $this->getAverageRating();
        return $average > 0 ? number_format($average, 1) : 'No ratings';
    }

    /**
     * Get star rating HTML for this user.
     */
    public function getStarRatingHtml(): string
    {
        $average = $this->getAverageRating();
        $stars = '';

        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $average) {
                $stars .= '<svg class="w-4 h-4 text-yellow-400 fill-current inline" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
            } elseif ($i - 0.5 <= $average) {
                $stars .= '<svg class="w-4 h-4 text-yellow-400 fill-current inline" viewBox="0 0 20 20"><defs><linearGradient id="half-fill"><stop offset="50%" stop-color="currentColor"/><stop offset="50%" stop-color="#d1d5db"/></linearGradient></defs><path fill="url(#half-fill)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
            } else {
                $stars .= '<svg class="w-4 h-4 text-gray-300 fill-current inline" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
            }
        }

        return $stars;
    }

    /**
     * Get the user's chat messages as sender.
     */
    public function sentMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'sender_id');
    }

    /**
     * Get the user's chat messages as receiver.
     */
    public function receivedMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'receiver_id');
    }

    /**
     * Get sugar partner exchanges where this user is user1.
     */
    public function sugarPartnerExchangesAsUser1(): HasMany
    {
        return $this->hasMany(SugarPartnerExchange::class, 'user1_id');
    }

    /**
     * Get sugar partner exchanges where this user is user2.
     */
    public function sugarPartnerExchangesAsUser2(): HasMany
    {
        return $this->hasMany(SugarPartnerExchange::class, 'user2_id');
    }

    /**
     * Get all sugar partner exchanges for this user.
     */
    public function sugarPartnerExchanges()
    {
        return SugarPartnerExchange::where('user1_id', $this->id)
            ->orWhere('user2_id', $this->id);
    }

    /**
     * Get sugar partner exchange payments for this user.
     */
    public function sugarPartnerExchangePayments(): HasMany
    {
        return $this->hasMany(SugarPartnerExchangePayment::class);
    }

    /**
     * Get sugar partner rejections made by this user.
     */
    public function sugarPartnerRejectionsGiven(): HasMany
    {
        return $this->hasMany(SugarPartnerRejection::class, 'rejector_id');
    }

    /**
     * Get sugar partner rejections received by this user.
     */
    public function sugarPartnerRejectionsReceived(): HasMany
    {
        return $this->hasMany(SugarPartnerRejection::class, 'rejected_user_id');
    }

    /**
     * Get or create user's wallet.
     */
    public function getWallet(): UserWallet
    {
        return UserWallet::getOrCreate($this->id);
    }

    /**
     * Check if user has paid for a specific event.
     */
    public function hasPaidForEvent($eventId): bool
    {
        return $this->eventPayments()
            ->where('meeting_address_id', $eventId)
            ->where('status', 'completed')
            ->exists();
    }

    /**
     * Get the profile picture URL.
     */
    public function getProfilePictureUrlAttribute(): string
    {
        if ($this->profile_picture) {
            return asset('storage/' . $this->profile_picture);
        }

        // Return default avatar if no profile picture
        return asset('images/default-avatar.png');
    }

    /**
     * Get interests as an array.
     */
    public function getInterestsArrayAttribute(): array
    {
        if (empty($this->interests)) {
            return [];
        }

        return array_map('trim', explode(',', $this->interests));
    }

    /**
     * Check if user's profile is 100% complete.
     */
    public function isProfileComplete(): bool
    {
        return !empty($this->name) &&
               !empty($this->email) &&
               !empty($this->contact_number) &&
               !empty($this->gender) &&
               !empty($this->date_of_birth) &&
               !empty($this->interests) &&
               !empty($this->expectation) &&
               !empty($this->profile_picture);
    }

    /**
     * Get profile completion percentage.
     */
    public function getProfileCompletionPercentage(): int
    {
        $totalFields = 7; // name, email, contact_number, gender, date_of_birth, interests, expectation, profile_picture
        $completedFields = 0;

        if (!empty($this->name) && !empty($this->email) && !empty($this->contact_number) && !empty($this->gender) && !empty($this->date_of_birth)) {
            $completedFields++; // Basic info counts as 1
        }
        if (!empty($this->profile_picture)) {
            $completedFields++; // Profile picture
        }
        if (!empty($this->interests)) {
            $completedFields++; // Interests
        }
        if (!empty($this->expectation)) {
            $completedFields++; // Expectation
        }

        return round(($completedFields / 4) * 100); // 4 main sections: basic info, profile picture, interests, expectation
    }

    /**
     * Get the user's change logs.
     */
    public function changeLogs()
    {
        return $this->hasMany(UserChangeLog::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the user's recent change logs.
     */
    public function recentChangeLogs($limit = 10)
    {
        return $this->changeLogs()->limit($limit);
    }

    /**
     * Get sugar partner types display names with "I am" vs "I want" labels.
     */
    public function getSugarPartnerTypesDisplayNames(): array
    {
        if (!$this->sugar_partner_types || !is_array($this->sugar_partner_types)) {
            return [];
        }

        $displayNames = [];
        foreach ($this->sugar_partner_types as $type) {
            $displayNames[] = match($type) {
                'sugar_daddy' => 'I am Sugar Daddy',
                'sugar_mommy' => 'I am Sugar Mommy',
                'sugar_companion_female' => 'I want Sugar Babe',
                'sugar_companion_male' => 'I want Sugar Boy',
                default => $type
            };
        }

        return $displayNames;
    }

    /**
     * Get all available sugar partner options for a user based on their gender.
     */
    public function getAvailableSugarPartnerOptions(): array
    {
        $options = [
            'sugar_daddy' => [
                'label' => 'I am Sugar Daddy',
                'description' => 'Provide financial support and mentorship',
                'color' => 'blue'
            ],
            'sugar_mommy' => [
                'label' => 'I am Sugar Mommy',
                'description' => 'Provide financial support and guidance',
                'color' => 'purple'
            ],
            'sugar_companion_female' => [
                'label' => 'I want Sugar Babe',
                'description' => 'Seek companionship and support',
                'color' => 'pink'
            ],
            'sugar_companion_male' => [
                'label' => 'I want Sugar Boy',
                'description' => 'Seek companionship and support',
                'color' => 'indigo'
            ]
        ];

        // Filter options based on user's gender for logical combinations
        if ($this->gender === 'male') {
            // Male users can be Sugar Daddy or want Sugar Mommy/Babe/Boy
            return $options;
        } elseif ($this->gender === 'female') {
            // Female users can be Sugar Mommy or want Sugar Daddy/Babe/Boy
            return $options;
        }

        // If gender is not specified, show all options
        return $options;
    }

    /**
     * Get sugar partner types for admin display (simplified labels).
     */
    public function getSugarPartnerTypesForAdmin(): array
    {
        if (!$this->sugar_partner_types || !is_array($this->sugar_partner_types)) {
            return [];
        }

        $displayNames = [];
        foreach ($this->sugar_partner_types as $type) {
            $displayNames[] = match($type) {
                'sugar_daddy' => 'Sugar Daddy',
                'sugar_mommy' => 'Sugar Mommy',
                'sugar_companion_female' => 'Sugar Babe',
                'sugar_companion_male' => 'Sugar Boy',
                default => $type
            };
        }

        return $displayNames;
    }

    /**
     * Get "What I Am" options for the dropdown (all identity roles).
     */
    public function getWhatIAmOptions(): array
    {
        return [
            'sugar_daddy' => [
                'label' => 'I am Sugar Daddy',
                'description' => 'Provide financial support and mentorship',
                'color' => 'blue'
            ],
            'sugar_mommy' => [
                'label' => 'I am Sugar Mommy',
                'description' => 'Provide financial support and guidance',
                'color' => 'purple'
            ],
            'sugar_boy' => [
                'label' => 'I am Sugar Boy',
                'description' => 'Seek companionship and support',
                'color' => 'indigo'
            ],
            'sugar_babe' => [
                'label' => 'I am Sugar Babe',
                'description' => 'Seek companionship and support',
                'color' => 'pink'
            ]
        ];
    }

    /**
     * Get "What I Want" options for checkboxes (seeking preferences).
     */
    public function getWhatIWantOptions(): array
    {
        return [
            'sugar_daddy' => [
                'label' => 'I want Sugar Daddy',
                'description' => 'Seek financial support and mentorship',
                'color' => 'blue'
            ],
            'sugar_mommy' => [
                'label' => 'I want Sugar Mommy',
                'description' => 'Seek financial support and guidance',
                'color' => 'purple'
            ],
            'sugar_boy' => [
                'label' => 'I want Sugar Boy',
                'description' => 'Seek male companionship and support',
                'color' => 'indigo'
            ],
            'sugar_babe' => [
                'label' => 'I want Sugar Babe',
                'description' => 'Seek female companionship and support',
                'color' => 'pink'
            ]
        ];
    }

    /**
     * Get the user's current "What I Am" selection (single value).
     */
    public function getWhatIAm(): ?string
    {
        if (!$this->sugar_partner_types || !is_array($this->sugar_partner_types)) {
            return null;
        }

        $identityTypes = ['sugar_daddy', 'sugar_mommy', 'sugar_boy', 'sugar_babe'];
        foreach ($this->sugar_partner_types as $type) {
            if (in_array($type, $identityTypes)) {
                return $type;
            }
        }

        return null;
    }

    /**
     * Get the user's current "What I Want" selections (array).
     */
    public function getWhatIWant(): array
    {
        if (!$this->sugar_partner_types || !is_array($this->sugar_partner_types)) {
            return [];
        }

        $seekingTypes = ['sugar_daddy', 'sugar_mommy', 'sugar_boy', 'sugar_babe'];
        $identityTypes = ['sugar_daddy', 'sugar_mommy', 'sugar_boy', 'sugar_babe'];

        // Get what they want (excluding what they are)
        $whatIAm = $this->getWhatIAm();
        $availableSeekingTypes = $whatIAm ? array_diff($seekingTypes, [$whatIAm]) : $seekingTypes;

        return array_intersect($this->sugar_partner_types, $availableSeekingTypes);
    }

    /**
     * Get "What I Am" display name for admin.
     */
    public function getWhatIAmForAdmin(): string
    {
        $whatIAm = $this->getWhatIAm();
        if (!$whatIAm) {
            return 'None';
        }

        return match($whatIAm) {
            'sugar_daddy' => 'Sugar Daddy',
            'sugar_mommy' => 'Sugar Mommy',
            default => 'None'
        };
    }

    /**
     * Get "What I Want" display names for admin.
     */
    public function getWhatIWantForAdmin(): array
    {
        $whatIWant = $this->getWhatIWant();
        if (empty($whatIWant)) {
            return ['None'];
        }

        $displayNames = [];
        foreach ($whatIWant as $type) {
            $displayNames[] = match($type) {
                'sugar_companion_female' => 'Sugar Babe',
                'sugar_companion_male' => 'Sugar Boy',
                default => $type
            };
        }

        return $displayNames;
    }

    /**
     * Get sugar partner types as comma-separated string.
     */
    public function getSugarPartnerTypesString(): string
    {
        $displayNames = $this->getSugarPartnerTypesDisplayNames();
        return empty($displayNames) ? 'Not Selected' : implode(', ', $displayNames);
    }

    /**
     * Get user's age based on date of birth.
     */
    public function getAge(): ?int
    {
        if (!$this->date_of_birth) {
            return null;
        }

        return $this->date_of_birth->age;
    }

    /**
     * Get age-appropriate default "What I Am" selection.
     */
    public function getAgeBasedWhatIAmDefault(): ?string
    {
        $age = $this->getAge();

        if ($age === null) {
            return null; // No default if age is unknown
        }

        if ($age < 30) {
            // Under 30: Default to companion roles based on gender
            if ($this->gender === 'male') {
                return 'sugar_boy';
            } elseif ($this->gender === 'female') {
                return 'sugar_babe';
            }
        } else {
            // 30+: Default to provider roles based on gender
            if ($this->gender === 'male') {
                return 'sugar_daddy';
            } elseif ($this->gender === 'female') {
                return 'sugar_mommy';
            }
        }

        return null;
    }

    /**
     * Get age-appropriate default "What I Want" selections.
     */
    public function getAgeBasedWhatIWantDefaults(): array
    {
        // No automatic defaults for "What I Want" section
        // Users should manually select what they're seeking
        // This maintains the logical separation between identity ("What I Am")
        // and preferences ("What I Want")
        return [];
    }

    /**
     * Get filtered "What I Am" options (excluding what's selected in "What I Want").
     */
    public function getFilteredWhatIAmOptions(array $selectedWhatIWant = []): array
    {
        $allOptions = $this->getWhatIAmOptions();

        // If user selected seeking companion roles, they shouldn't be provider
        if (!empty($selectedWhatIWant)) {
            // If they want companionship, they're likely not providers
            // But we'll still show all options and let them decide
        }

        return $allOptions;
    }

    /**
     * Get filtered "What I Want" options (excluding what's selected in "What I Am").
     */
    public function getFilteredWhatIWantOptions(?string $selectedWhatIAm = null): array
    {
        $allOptions = $this->getWhatIWantOptions();

        // If user is a provider (Sugar Daddy/Mommy), they typically want companions
        // If user is not a provider, they might want other providers
        // We'll show all options but this can be used for smart suggestions

        return $allOptions;
    }

    /**
     * Get smart default selections based on age and current selections.
     */
    public function getSmartDefaults(): array
    {
        // If user already has selections, don't override
        if (!empty($this->sugar_partner_types)) {
            return [
                'what_i_am' => $this->getWhatIAm(),
                'what_i_want' => $this->getWhatIWant()
            ];
        }

        // For new users, provide age-based defaults
        return [
            'what_i_am' => $this->getAgeBasedWhatIAmDefault(),
            'what_i_want' => $this->getAgeBasedWhatIWantDefaults()
        ];
    }

    /**
     * Check if user can access time spending features.
     * Time spending is only available when:
     * 1. Admin has enabled the feature
     * 2. User has both Public Profile and Show Images enabled
     */
    public function canAccessTimeSpending(): bool
    {
        return Feature::isEnabled('time_spending') &&
               $this->is_public_profile &&
               $this->show_gallery_images;
    }

    /**
     * Get formatted hourly rate with currency.
     */
    public function getFormattedHourlyRate(): string
    {
        if (!$this->hourly_rate) {
            return 'Not Set';
        }

        return $this->currency . ' ' . number_format($this->hourly_rate, 2);
    }

    /**
     * Check if user has an active time spending subscription.
     */
    public function hasActiveTimeSpendingSubscription(): bool
    {
        return $this->has_active_time_spending_subscription &&
               $this->time_spending_subscription_expires_at &&
               $this->time_spending_subscription_expires_at > now();
    }

    /**
     * Check if user has an expired time spending subscription.
     */
    public function hasExpiredTimeSpendingSubscription(): bool
    {
        // First check if user currently has an active subscription
        if ($this->hasActiveTimeSpendingSubscription()) {
            return false; // If active, then not expired
        }

        // Check if user has subscription data but it's expired
        if ($this->time_spending_subscription_expires_at && $this->time_spending_subscription_expires_at <= now()) {
            return true;
        }

        // Also check in subscriptions table for any expired subscriptions
        return $this->subscriptions()
            ->where(function($query) {
                $query->where('status', 'expired')
                      ->orWhere(function($subQuery) {
                          $subQuery->where('status', 'active')
                                   ->where('expires_at', '<=', now());
                      });
            })
            ->exists();
    }

    /**
     * Check if user should be visible in Find Person page based on subscription model.
     */
    public function isVisibleInFindPerson(): bool
    {
        // If subscription model is disabled, all users are visible
        if (!Feature::isSubscriptionModelEnabled()) {
            return true;
        }

        // If subscription model is enabled, only users with active subscriptions are visible
        return $this->hasActiveTimeSpendingSubscription();
    }

    /**
     * Check if user can use Time Spending service based on subscription model.
     */
    public function canUseTimeSpending(): bool
    {
        // If subscription model is disabled, all users can use Time Spending
        if (!Feature::isSubscriptionModelEnabled()) {
            return true;
        }

        // If subscription model is enabled, only users with active subscriptions can use it
        return $this->hasActiveTimeSpendingSubscription();
    }

    /**
     * Update user's subscription status.
     */
    public function updateSubscriptionStatus(): void
    {
        $activeSubscription = UserSubscription::getActiveSubscription($this->id);

        if ($activeSubscription) {
            $this->update([
                'has_active_time_spending_subscription' => true,
                'time_spending_subscription_expires_at' => $activeSubscription->expires_at,
            ]);
        } else {
            $this->update([
                'has_active_time_spending_subscription' => false,
                'time_spending_subscription_expires_at' => null,
            ]);
        }
    }

    /**
     * Check if user should be visible on Find Person page.
     */
    public function isVisibleOnFindPerson(): bool
    {
        return $this->is_time_spending_enabled &&
               $this->is_public_profile &&
               $this->show_gallery_images &&
               $this->hourly_rate &&
               $this->hourly_rate > 0 &&
               $this->hasActiveTimeSpendingSubscription();
    }

    /**
     * Check if user is available on a specific day and time.
     */
    public function isAvailableAt(\DateTime $dateTime): bool
    {
        if (!$this->is_time_spending_enabled || !$this->availability_schedule) {
            return false;
        }

        $dayOfWeek = strtolower($dateTime->format('l')); // monday, tuesday, etc.
        $time = $dateTime->format('H:i');

        $daySchedule = $this->availability_schedule[$dayOfWeek] ?? null;

        if (!$daySchedule || ($daySchedule['is_holiday'] ?? false)) {
            return false;
        }

        $startTime = $daySchedule['start_time'] ?? '09:00';
        $endTime = $daySchedule['end_time'] ?? '17:00';

        return $time >= $startTime && $time <= $endTime;
    }

    /**
     * Get availability schedule for a specific day.
     */
    public function getAvailabilityForDay(string $day): ?array
    {
        return $this->availability_schedule[$day] ?? null;
    }

    /**
     * Get formatted availability schedule.
     */
    public function getFormattedAvailabilitySchedule(): array
    {
        $schedule = [];
        $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

        foreach ($days as $day) {
            $daySchedule = $this->availability_schedule[$day] ?? null;

            if (!$daySchedule || ($daySchedule['is_holiday'] ?? false)) {
                $schedule[ucfirst($day)] = 'Holiday';
            } else {
                $startTime = $daySchedule['start_time'] ?? '09:00';
                $endTime = $daySchedule['end_time'] ?? '17:00';

                // Convert to 12-hour format
                $startTime12 = $this->convertTo12HourFormat($startTime);
                $endTime12 = $this->convertTo12HourFormat($endTime);

                $schedule[ucfirst($day)] = $startTime12 . ' - ' . $endTime12;
            }
        }

        return $schedule;
    }

    /**
     * Convert 24-hour time format to 12-hour format with AM/PM.
     */
    private function convertTo12HourFormat(string $time24): string
    {
        try {
            $dateTime = \DateTime::createFromFormat('H:i', $time24);
            if ($dateTime) {
                return $dateTime->format('g:i A');
            }
        } catch (\Exception $e) {
            // Fallback if conversion fails
        }

        return $time24;
    }

    /**
     * Check if user can access couple activity features.
     * Couple activity is only available when:
     * 1. Admin has enabled the feature
     * 2. User has enabled couple activity in settings
     */
    public function canAccessCoupleActivity(): bool
    {
        return Feature::isEnabled('partner_swapping') && $this->is_couple_activity_enabled;
    }

    /**
     * Check if user has a pending couple activity request with another user.
     */
    public function hasPendingRequestWith(User $user): bool
    {
        return $this->sentCoupleActivityRequests()
            ->where('partner_id', $user->id)
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if user has received a pending request from another user.
     */
    public function hasReceivedRequestFrom(User $user): bool
    {
        return $this->receivedCoupleActivityRequests()
            ->where('requester_id', $user->id)
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Get current own partner (boyfriend/girlfriend).
     */
    public function getOwnPartner(): ?User
    {
        $approvedRequest = $this->sentCoupleActivityRequests()
            ->where('status', 'approved')
            ->where('type', 'own_partner')
            ->first();

        if ($approvedRequest) {
            return $approvedRequest->partner;
        }

        $approvedRequest = $this->receivedCoupleActivityRequests()
            ->where('status', 'approved')
            ->where('type', 'own_partner')
            ->first();

        if ($approvedRequest) {
            return $approvedRequest->requester;
        }

        return null;
    }

    /**
     * Get current couple activity partner (if any).
     * @deprecated Use getOwnPartner() instead
     */
    public function getCurrentCoupleActivityPartner(): ?User
    {
        return $this->getOwnPartner();
    }

    /**
     * Check if user has their own partner.
     */
    public function hasOwnPartner(): bool
    {
        return $this->getOwnPartner() !== null;
    }

    /**
     * Get current couple activity partners.
     */
    public function getCoupleActivityPartners(): array
    {
        $partners = [];

        $approvedRequests = $this->sentCoupleActivityRequests()
            ->where('status', 'approved')
            ->where('type', 'couple_swap')
            ->with('partner')
            ->get();

        foreach ($approvedRequests as $request) {
            $partners[] = $request->partner;
        }

        $approvedRequests = $this->receivedCoupleActivityRequests()
            ->where('status', 'approved')
            ->where('type', 'couple_swap')
            ->with('requester')
            ->get();

        foreach ($approvedRequests as $request) {
            $partners[] = $request->requester;
        }

        return $partners;
    }

    /**
     * Get pending own partner requests received by this user.
     */
    public function getPendingOwnPartnerRequests()
    {
        return $this->receivedCoupleActivityRequests()
            ->where('status', 'pending')
            ->where('type', 'own_partner')
            ->with('requester')
            ->latest()
            ->get();
    }

    /**
     * Get pending couple swap requests received by this user.
     */
    public function getPendingCoupleSwapRequests()
    {
        return $this->receivedCoupleActivityRequests()
            ->where('status', 'pending')
            ->where('type', 'couple_swap')
            ->with('requester')
            ->latest()
            ->get();
    }

    /**
     * Get pending couple activity requests received by this user.
     * @deprecated Use getPendingOwnPartnerRequests() or getPendingCoupleSwapRequests()
     */
    public function getPendingCoupleActivityRequests()
    {
        return $this->getPendingOwnPartnerRequests();
    }

    /**
     * Check if user has a pending own partner request with another user.
     */
    public function hasPendingOwnPartnerRequestWith(User $user): bool
    {
        return $this->sentCoupleActivityRequests()
            ->where('partner_id', $user->id)
            ->where('type', 'own_partner')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if user has received a pending own partner request from another user.
     */
    public function hasReceivedOwnPartnerRequestFrom(User $user): bool
    {
        return $this->receivedCoupleActivityRequests()
            ->where('requester_id', $user->id)
            ->where('type', 'own_partner')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if user has blocked another user.
     */
    public function hasBlockedUser(User $user): bool
    {
        return $this->receivedCoupleActivityRequests()
            ->where('requester_id', $user->id)
            ->where('type', 'own_partner')
            ->where('status', 'blocked')
            ->exists();
    }

    /**
     * Check if user has been blocked by another user.
     */
    public function hasBeenBlockedBy(User $user): bool
    {
        return $this->sentCoupleActivityRequests()
            ->where('partner_id', $user->id)
            ->where('type', 'own_partner')
            ->where('status', 'blocked')
            ->exists();
    }

    /**
     * Check if there's any blocking relationship between users.
     */
    public function hasBlockingRelationshipWith(User $user): bool
    {
        return $this->hasBlockedUser($user) || $this->hasBeenBlockedBy($user);
    }

    /**
     * Check if user has any active chat sessions.
     */
    public function hasActiveChatSessions(): bool
    {
        return \App\Models\TimeSpendingBooking::with(['client', 'provider'])
            ->where(function($query) {
                $query->where('client_id', $this->id)
                      ->orWhere('provider_id', $this->id);
            })
            ->where('chat_enabled', true)
            ->where('provider_status', 'accepted')
            ->get()
            ->filter(function($booking) {
                // Calculate end time and check if booking hasn't ended yet (IST timezone)
                $bookingEndTime = \Carbon\Carbon::parse($booking->booking_date)->setTimezone('Asia/Kolkata')->addHours((float) $booking->duration_hours);

                // Check if meeting is completed (both end photos uploaded) or time has passed
                $isMeetingCompleted = $booking->isMeetingCompleted();
                $isTimeExpired = $bookingEndTime->isPast();

                // Only show as active if meeting is not completed AND time hasn't expired
                return !$isMeetingCompleted && !$isTimeExpired;
            })
            ->isNotEmpty();
    }

    /**
     * Get all users that this user has blocked.
     */
    public function getBlockedUsers()
    {
        return $this->receivedCoupleActivityRequests()
            ->where('type', 'own_partner')
            ->where('status', 'blocked')
            ->with('requester')
            ->get()
            ->pluck('requester');
    }

    /**
     * Check if user has a pending couple swap request with another user.
     */
    public function hasPendingCoupleSwapRequestWith(User $user): bool
    {
        return $this->sentCoupleActivityRequests()
            ->where('partner_id', $user->id)
            ->where('type', 'couple_swap')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if user has received a pending couple swap request from another user.
     */
    public function hasReceivedCoupleSwapRequestFrom(User $user): bool
    {
        return $this->receivedCoupleActivityRequests()
            ->where('requester_id', $user->id)
            ->where('type', 'couple_swap')
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Check if user can participate in couple activities.
     * Both user and their partner must have couple activity enabled.
     */
    public function canParticipateInCoupleActivity(): bool
    {
        if (!$this->canAccessCoupleActivity() || !$this->hasOwnPartner()) {
            return false;
        }

        $ownPartner = $this->getOwnPartner();
        return $ownPartner && $ownPartner->canAccessCoupleActivity();
    }

    // Backward compatibility methods for partner swapping
    public function sentPartnerSwappingRequests(): HasMany
    {
        return $this->sentCoupleActivityRequests();
    }

    public function receivedPartnerSwappingRequests(): HasMany
    {
        return $this->receivedCoupleActivityRequests();
    }

    public function canAccessPartnerSwapping(): bool
    {
        return $this->canAccessCoupleActivity();
    }

    public function getCoupleSwappingPartners(): array
    {
        return $this->getCoupleActivityPartners();
    }

    public function getPendingPartnerSwappingRequests()
    {
        return $this->getPendingCoupleActivityRequests();
    }

    public function canParticipateInCoupleSwapping(): bool
    {
        return $this->canParticipateInCoupleActivity();
    }

    public function getCurrentPartnerSwappingPartner(): ?User
    {
        return $this->getCurrentCoupleActivityPartner();
    }
}
