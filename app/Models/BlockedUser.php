<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BlockedUser extends Model
{
    protected $fillable = [
        'blocker_id',
        'blocked_id',
        'reason',
        'booking_id',
    ];

    /**
     * Get the user who is blocking.
     */
    public function blocker(): BelongsTo
    {
        return $this->belongsTo(User::class, 'blocker_id');
    }

    /**
     * Get the user who is being blocked.
     */
    public function blocked(): BelongsTo
    {
        return $this->belongsTo(User::class, 'blocked_id');
    }

    /**
     * Get the related booking if any.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(TimeSpendingBooking::class, 'booking_id');
    }

    /**
     * Check if a user is blocked by another user.
     */
    public static function isBlocked($blockerId, $blockedId): bool
    {
        return self::where('blocker_id', $blockerId)
                   ->where('blocked_id', $blockedId)
                   ->exists();
    }

    /**
     * Block a user.
     */
    public static function blockUser($blockerId, $blockedId, $reason = null, $bookingId = null): self
    {
        return self::updateOrCreate(
            [
                'blocker_id' => $blockerId,
                'blocked_id' => $blockedId,
            ],
            [
                'reason' => $reason,
                'booking_id' => $bookingId,
            ]
        );
    }

    /**
     * Unblock a user.
     */
    public static function unblockUser($blockerId, $blockedId): bool
    {
        return self::where('blocker_id', $blockerId)
                   ->where('blocked_id', $blockedId)
                   ->delete() > 0;
    }

    /**
     * Get all users blocked by a specific user.
     */
    public static function getBlockedUsers($blockerId)
    {
        return self::with('blocked')
                   ->where('blocker_id', $blockerId)
                   ->get();
    }

    /**
     * Auto-reject and remove future bookings when a user is blocked.
     */
    public static function handleUserBlocked($blockerId, $blockedId): int
    {
        // Find all pending bookings from the blocked user to the blocker
        $futureBookings = TimeSpendingBooking::where('provider_id', $blockerId)
            ->where('client_id', $blockedId)
            ->where('provider_status', 'pending')
            ->where('payment_status', 'paid')
            ->where('booking_date', '>', now())
            ->get();

        $rejectedCount = 0;
        foreach ($futureBookings as $booking) {
            $booking->update([
                'provider_status' => 'rejected',
                'rejection_reason' => 'Client blocked by provider',
                'provider_responded_at' => now(),
                'status' => 'cancelled',
                'cancellation_reason' => 'Client blocked by provider',
            ]);

            // Process refund for blocked booking
            $booking->processRefund('Client blocked by provider');

            // Send notification to client about rejection
            Notification::create([
                'user_id' => $booking->client_id,
                'type' => 'booking_rejected',
                'title' => 'Booking Request Declined',
                'message' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was declined.",
                'body' => "Your booking request for {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} was declined.",
                'data' => [
                    'booking_id' => $booking->id,
                    'provider_id' => $booking->provider_id,
                    'blocked' => true,
                    'reason' => 'client_blocked'
                ],
            ]);

            $rejectedCount++;
        }

        return $rejectedCount;
    }
}
