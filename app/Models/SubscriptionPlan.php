<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'duration_months',
        'amount',
        'original_price',
        'discount_price',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'original_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'is_active' => 'boolean',
        'duration_months' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get active subscription plans ordered by sort_order.
     */
    public static function getActivePlans()
    {
        return self::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('duration_months')
            ->get();
    }

    /**
     * Get formatted amount with currency (uses effective price).
     */
    public function getFormattedAmountAttribute(): string
    {
        return '₹' . number_format($this->effective_price, 2);
    }

    /**
     * Get duration text.
     */
    public function getDurationTextAttribute(): string
    {
        if ($this->duration_months == 1) {
            return '1 Month';
        }
        return $this->duration_months . ' Months';
    }

    /**
     * Get monthly rate (uses effective price).
     */
    public function getMonthlyRateAttribute(): float
    {
        if ($this->duration_months <= 0) {
            return 0;
        }
        return $this->effective_price / $this->duration_months;
    }

    /**
     * Get formatted monthly rate.
     */
    public function getFormattedMonthlyRateAttribute(): string
    {
        if ($this->duration_months <= 0) {
            return 'Invalid Duration';
        }
        return '₹' . number_format($this->monthly_rate, 2) . '/month';
    }

    /**
     * Get the effective price (discount price if available, otherwise original price).
     */
    public function getEffectivePriceAttribute(): float
    {
        return $this->discount_price ?? $this->original_price;
    }

    /**
     * Check if plan has an active discount.
     */
    public function hasDiscount(): bool
    {
        return $this->discount_price !== null && $this->discount_price < $this->original_price;
    }

    /**
     * Get discount percentage.
     */
    public function getDiscountPercentageAttribute(): float
    {
        if (!$this->hasDiscount() || $this->original_price <= 0) {
            return 0;
        }
        return round((($this->original_price - $this->discount_price) / $this->original_price) * 100, 1);
    }

    /**
     * Get savings amount.
     */
    public function getSavingsAmountAttribute(): float
    {
        if (!$this->hasDiscount()) {
            return 0;
        }
        return $this->original_price - $this->discount_price;
    }

    /**
     * Get formatted effective price with currency.
     */
    public function getFormattedEffectivePriceAttribute(): string
    {
        return '₹' . number_format($this->effective_price, 2);
    }

    /**
     * Get formatted original price with currency.
     */
    public function getFormattedOriginalPriceAttribute(): string
    {
        return '₹' . number_format($this->original_price, 2);
    }

    /**
     * Get formatted discount price with currency.
     */
    public function getFormattedDiscountPriceAttribute(): string
    {
        return $this->discount_price ? '₹' . number_format($this->discount_price, 2) : null;
    }

    /**
     * Get formatted savings amount with currency.
     */
    public function getFormattedSavingsAmountAttribute(): string
    {
        return '₹' . number_format($this->savings_amount, 2);
    }
}
