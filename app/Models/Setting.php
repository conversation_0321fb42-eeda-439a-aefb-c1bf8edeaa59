<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
        'label',
        'description'
    ];

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $setting = self::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        return $setting->value;
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param mixed $value
     * @return Setting
     */
    public static function set(string $key, $value)
    {
        $setting = self::firstOrNew(['key' => $key]);
        $setting->value = $value;
        $setting->save();

        return $setting;
    }

    /**
     * Get copyright text with dynamic placeholders replaced
     *
     * @return string
     */
    public static function getCopyrightText()
    {
        $copyrightText = self::get('copyright_text', '© {year} {app_name}. All rights reserved. Made with ❤️ for finding love.');

        // Replace dynamic placeholders
        $copyrightText = str_replace('{year}', date('Y'), $copyrightText);
        $copyrightText = str_replace('{app_name}', config('app.name', 'SettingWala'), $copyrightText);

        return $copyrightText;
    }
}
