<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MeetingAddress extends Model
{
    use HasFactory; // Good practice to include HasFactory

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'is_couple_event',
        'payment_amount_couple',
        'rules_and_regulations',
        'payment_amount_boys',
        'payment_amount_girls',
        'is_event_enabled',
        'event_date',
        'location',
        'latitude',
        'longitude'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payment_amount_boys' => 'decimal:2',
        'payment_amount_girls' => 'decimal:2',
        'payment_amount_couple' => 'decimal:2',
        'is_event_enabled' => 'boolean',
        'is_couple_event' => 'boolean',
        'event_date' => 'datetime',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Get the event payments for this meeting address.
     */
    public function eventPayments(): HasMany
    {
        return $this->hasMany(EventPayment::class);
    }

    /**
     * Get truncated description for preview (approximately 2 lines)
     *
     * @return string
     */
    public function getTruncatedDescriptionAttribute(): string
    {
        // Strip HTML tags and get plain text
        $plainText = strip_tags($this->description);

        // Limit to approximately 120 characters (roughly 2 lines)
        if (strlen($plainText) <= 120) {
            return $plainText;
        }

        return substr($plainText, 0, 120) . '...';
    }

    /**
     * Get the payment amount for a specific user based on event type and user gender
     *
     * @param \App\Models\User $user
     * @return float
     */
    public function getAmountForUser($user): float
    {
        if ($this->is_couple_event) {
            return $this->payment_amount_couple;
        }

        return $user->gender === 'female'
            ? $this->payment_amount_girls
            : $this->payment_amount_boys;
    }
}
