<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SugarPartnerExchangePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'exchange_id',
        'user_id',
        'amount',
        'currency',
        'payment_method',
        'status',
        'payment_id',
        'order_id',
        'signature',
        'payment_details',
        'paid_at',
        'failure_reason',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the exchange this payment belongs to.
     */
    public function exchange(): BelongsTo
    {
        return $this->belongsTo(SugarPartnerExchange::class, 'exchange_id');
    }

    /**
     * Get the user who made this payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted(array $paymentDetails = []): void
    {
        $this->update([
            'status' => 'completed',
            'paid_at' => now(),
            'payment_details' => array_merge($this->payment_details ?? [], $paymentDetails),
        ]);

        // Send payment completion notification
        $this->sendPaymentCompletionNotification();

        // Check if both users have paid and update exchange status
        $this->exchange->markPaymentCompleted();

        // If both users have paid, send profile viewable notifications
        if ($this->exchange->bothUsersPaid()) {
            $this->sendProfileViewableNotifications();
        }
    }

    /**
     * Send payment completion notification to the user.
     */
    protected function sendPaymentCompletionNotification(): void
    {
        $exchange = $this->exchange;
        $user = $this->user;
        $otherUser = $exchange->getOtherUser($this->user_id);

        // Skip notification if user has disabled Sugar Partner notifications
        if ($user->hide_sugar_partner_notifications) {
            return;
        }

        Notification::create([
            'user_id' => $this->user_id,
            'type' => 'sugar_partner_payment_completed',
            'title' => 'Sugar Partner Payment Completed',
            'message' => "Your profile shared with {$otherUser->name} as you want sugar partner",
            'body' => "Your profile shared with {$otherUser->name} as you want sugar partner. Your payment of {$this->currency} {$this->amount} has been processed successfully.",
            'data' => [
                'exchange_id' => $exchange->id,
                'other_user_id' => $otherUser->id,
                'other_user_name' => $otherUser->name,
                'amount_paid' => $this->amount,
                'currency' => $this->currency,
                'action_url' => route('sugar-partner.exchange.status', $exchange),
                'action_text' => 'View Exchange Status'
            ],
        ]);
    }

    /**
     * Send profile viewable notifications to both users.
     */
    protected function sendProfileViewableNotifications(): void
    {
        $exchange = $this->exchange;

        foreach ([$exchange->user1, $exchange->user2] as $user) {
            // Skip notification if user has disabled Sugar Partner notifications
            if ($user->hide_sugar_partner_notifications) {
                continue;
            }

            $otherUser = $exchange->getOtherUser($user->id);

            Notification::create([
                'user_id' => $user->id,
                'type' => 'sugar_partner_profiles_viewable',
                'title' => 'Sugar Partner Profile Shared',
                'message' => "Your profile shared with {$otherUser->name} as you want sugar partner",
                'body' => "Your profile shared with {$otherUser->name} as you want sugar partner. Both users have completed payment and can now view each other's profiles.",
                'data' => [
                    'exchange_id' => $exchange->id,
                    'other_user_id' => $otherUser->id,
                    'other_user_name' => $otherUser->name,
                    'action_url' => route('sugar-partner.exchange.profile', [$exchange, $otherUser]),
                    'action_text' => 'View Profile & Respond'
                ],
            ]);
        }
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Create a payment record for an exchange.
     */
    public static function createForExchange(SugarPartnerExchange $exchange, int $userId, array $paymentData = []): self
    {
        // Get the specific price for this user based on their Sugar Partner type
        $userPrice = $exchange->getPriceForUser($userId);

        return self::create([
            'exchange_id' => $exchange->id,
            'user_id' => $userId,
            'amount' => $userPrice,
            'currency' => $exchange->currency,
            'payment_method' => $paymentData['payment_method'] ?? 'razorpay',
            'payment_id' => $paymentData['payment_id'] ?? null,
            'order_id' => $paymentData['order_id'] ?? null,
            'signature' => $paymentData['signature'] ?? null,
            'payment_details' => $paymentData['payment_details'] ?? [],
        ]);
    }

    /**
     * Process refund for this payment.
     */
    public function processRefund(string $reason = 'Exchange cancelled'): bool
    {
        if ($this->status !== 'completed') {
            return false;
        }

        // Here you would integrate with your payment gateway to process the actual refund
        // For now, we'll just mark it as refunded
        $this->update([
            'status' => 'refunded',
            'failure_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Get payment status badge class for UI.
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'completed' => 'bg-success',
            'pending' => 'bg-warning',
            'failed' => 'bg-danger',
            'refunded' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get payment method display name.
     */
    public function getPaymentMethodDisplayName(): string
    {
        return match($this->payment_method) {
            'razorpay' => 'Razorpay',
            'wallet' => 'Wallet',
            'stripe' => 'Stripe',
            'paypal' => 'PayPal',
            default => ucfirst($this->payment_method)
        };
    }
}
