<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Feature extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'label',
        'description',
        'is_enabled',
        'is_hidden',
        'options'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'is_hidden' => 'boolean',
        'options' => 'array'
    ];

    /**
     * Check if a feature is enabled
     */
    public static function isEnabled(string $featureName): bool
    {
        $feature = self::where('name', $featureName)->first();
        return $feature ? $feature->is_enabled : false;
    }

    /**
     * Check if subscription model is enabled.
     */
    public static function isSubscriptionModelEnabled(): bool
    {
        return self::isEnabled('subscription_model');
    }

    /**
     * Get feature options
     */
    public static function getOptions(string $featureName): array
    {
        $feature = self::where('name', $featureName)->first();
        return $feature ? ($feature->options ?? []) : [];
    }
}
