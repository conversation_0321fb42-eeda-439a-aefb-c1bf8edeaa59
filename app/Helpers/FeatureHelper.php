<?php

namespace App\Helpers;

use App\Models\Feature;

class FeatureHelper
{
    /**
     * Check if meeting verification feature is enabled
     */
    public static function isMeetingVerificationEnabled(): bool
    {
        return Feature::isEnabled('meeting_verification');
    }

    /**
     * Check if rating & review system is enabled
     */
    public static function isRatingReviewSystemEnabled(): bool
    {
        return Feature::isEnabled('rating_review_system');
    }

    /**
     * Check if rating & review system should be active
     * (enabled - can work with or without meeting verification)
     */
    public static function isRatingReviewSystemActive(): bool
    {
        return self::isRatingReviewSystemEnabled();
    }

    /**
     * Check if sugar partner feature is enabled
     */
    public static function isSugarPartnerEnabled(): bool
    {
        return Feature::isEnabled('sugar_partner');
    }

    /**
     * Check if couple activity feature is enabled
     */
    public static function isCoupleActivityEnabled(): bool
    {
        return Feature::isEnabled('partner_swapping');
    }

    /**
     * Check if time spending feature is enabled
     */
    public static function isTimeSpendingEnabled(): bool
    {
        return Feature::isEnabled('time_spending');
    }

    /**
     * Check if gallery feature is enabled
     */
    public static function isGalleryEnabled(): bool
    {
        return Feature::isEnabled('gallery');
    }

    /**
     * Check if meeting events feature is enabled
     */
    public static function isMeetingEventsEnabled(): bool
    {
        return Feature::isEnabled('meeting_events');
    }

    /**
     * Check if chat system is enabled
     */
    public static function isChatSystemEnabled(): bool
    {
        return Feature::isEnabled('chat_system');
    }

    /**
     * Check if notifications are enabled
     */
    public static function isNotificationsEnabled(): bool
    {
        return Feature::isEnabled('notifications');
    }

    /**
     * Check if user verification is enabled
     */
    public static function isUserVerificationEnabled(): bool
    {
        return Feature::isEnabled('user_verification');
    }

    /**
     * Check if premium membership is enabled
     */
    public static function isPremiumMembershipEnabled(): bool
    {
        return Feature::isEnabled('premium_membership');
    }

    /**
     * Check if location services are enabled
     */
    public static function isLocationServicesEnabled(): bool
    {
        return Feature::isEnabled('location_services');
    }

    /**
     * Check if privacy controls are enabled
     */
    public static function isPrivacyControlsEnabled(): bool
    {
        return Feature::isEnabled('privacy_controls');
    }

    /**
     * Get all enabled features
     */
    public static function getEnabledFeatures(): array
    {
        return Feature::where('is_enabled', true)->pluck('name')->toArray();
    }

    /**
     * Get feature options
     */
    public static function getFeatureOptions(string $featureName): array
    {
        return Feature::getOptions($featureName);
    }
}
