<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Always disable query logging for performance
        DB::disableQueryLog();

        // Production optimizations
        if (config('app.env') === 'production') {
            // Suppress all debug output and warnings
            error_reporting(E_ERROR | E_PARSE);

            // Disable debug mode completely
            config(['app.debug' => false]);

            // Set production log level to error only
            config(['logging.level' => 'error']);

            // Disable all unnecessary logging
            config(['logging.channels.single.level' => 'error']);
            config(['logging.channels.daily.level' => 'error']);
        }

        // Staging and development optimizations
        if (in_array(config('app.env'), ['local', 'development', 'staging'])) {
            // Still disable query logging for better performance
            error_reporting(E_ERROR | E_PARSE | E_WARNING);

            // Limit logging in non-production environments
            config(['logging.level' => 'warning']);
        }
    }
}
