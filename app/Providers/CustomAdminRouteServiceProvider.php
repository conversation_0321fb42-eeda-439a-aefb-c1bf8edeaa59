<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

class CustomAdminRouteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->registerCustomAdminRoutes();
    }

    /**
     * Register custom admin routes based on settings
     */
    protected function registerCustomAdminRoutes(): void
    {
        try {
            // Only register if database is available
            if (app()->bound('db') && Schema::hasTable('settings')) {
                $customAdminUrl = Setting::get('custom_admin_url', '');
                
                if (!empty($customAdminUrl)) {
                    $customAdminUrl = trim($customAdminUrl, '/');
                    $this->mapCustomAdminRoutes($customAdminUrl);
                }
            }
        } catch (\Exception $e) {
            // Silently fail if database is not available
        }
    }

    /**
     * Map custom admin routes
     */
    protected function mapCustomAdminRoutes(string $customPrefix): void
    {
        // Admin authentication routes (public) with custom prefix
        Route::prefix($customPrefix)->name('admin.')->group(function () {
            Route::get('/login', [\App\Http\Controllers\Admin\AuthController::class, 'showLoginForm'])->name('login');
            Route::post('/login', [\App\Http\Controllers\Admin\AuthController::class, 'login']);
            Route::post('/logout', [\App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('logout');
        });

        // Admin routes (protected) with custom prefix
        Route::middleware('admin')->prefix($customPrefix)->name('admin.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AdminController::class, 'index'])->name('dashboard');
            Route::get('/users', [\App\Http\Controllers\Admin\AdminController::class, 'users'])->name('users');
            Route::get('/management-users', [\App\Http\Controllers\Admin\AdminController::class, 'managementUsers'])->name('management-users');
            Route::post('/users/{user}/toggle-admin', [\App\Http\Controllers\Admin\AdminController::class, 'toggleAdmin'])->name('users.toggle-admin');

            // Admin settings routes
            Route::get('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
            Route::post('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'update'])->name('settings.update');
            Route::post('/settings/ajax', [\App\Http\Controllers\Admin\SettingController::class, 'updateAjax'])->name('settings.update-ajax');
            Route::post('/settings/upload-file', [\App\Http\Controllers\Admin\SettingController::class, 'uploadFile'])->name('settings.upload-file');
            Route::post('/settings/test-razorpay', [\App\Http\Controllers\Admin\SettingController::class, 'testRazorpay'])->name('settings.test-razorpay');

            // Admin notification routes
            Route::get('/notifications', [\App\Http\Controllers\Admin\NotificationController::class, 'index'])->name('notifications.index');
            Route::post('/notifications', [\App\Http\Controllers\Admin\NotificationController::class, 'store'])->name('notifications.store');
            Route::delete('/notifications/{notification}', [\App\Http\Controllers\Admin\NotificationController::class, 'destroy'])->name('notifications.destroy');

            // Meeting Events routes
            Route::get('/meeting-events', [\App\Http\Controllers\Admin\MeetingEventController::class, 'index'])->name('meeting-events.index');
            Route::get('/meeting-events/create', [\App\Http\Controllers\Admin\MeetingEventController::class, 'create'])->name('meeting-events.create');
            Route::post('/meeting-events', [\App\Http\Controllers\Admin\MeetingEventController::class, 'store'])->name('meeting-events.store');
            Route::get('/meeting-events/{meetingEvent}', [\App\Http\Controllers\Admin\MeetingEventController::class, 'show'])->name('meeting-events.show');
            Route::get('/meeting-events/{meetingEvent}/edit', [\App\Http\Controllers\Admin\MeetingEventController::class, 'edit'])->name('meeting-events.edit');
            Route::put('/meeting-events/{meetingEvent}', [\App\Http\Controllers\Admin\MeetingEventController::class, 'update'])->name('meeting-events.update');
            Route::delete('/meeting-events/{meetingEvent}', [\App\Http\Controllers\Admin\MeetingEventController::class, 'destroy'])->name('meeting-events.destroy');

            // Revenue routes
            Route::get('/revenue', [\App\Http\Controllers\Admin\RevenueController::class, 'index'])->name('revenue.index');

            // Contact routes (using ContactController instead of ContactFormController)
            Route::get('/contact-forms', [\App\Http\Controllers\Admin\ContactController::class, 'index'])->name('contact-forms.index');
            Route::get('/contact-forms/{contactForm}', [\App\Http\Controllers\Admin\ContactController::class, 'show'])->name('contact-forms.show');
            Route::patch('/contact-forms/{contactForm}/status', [\App\Http\Controllers\Admin\ContactController::class, 'updateStatus'])->name('contact-forms.update-status');
            Route::delete('/contact-forms/{contactForm}', [\App\Http\Controllers\Admin\ContactController::class, 'destroy'])->name('contact-forms.destroy');

            // Features routes
            Route::get('/features', [\App\Http\Controllers\Admin\FeatureController::class, 'index'])->name('features.index');
            Route::post('/features/{feature}/toggle', [\App\Http\Controllers\Admin\FeatureController::class, 'toggle'])->name('features.toggle');

            // Analytics data routes
            Route::get('/analytics-data', [\App\Http\Controllers\Admin\AdminController::class, 'getAnalyticsData'])->name('analytics.data');
            Route::get('/analytics-export', [\App\Http\Controllers\Admin\AdminController::class, 'exportAnalytics'])->name('analytics.export');

            // API routes
            Route::prefix('api')->name('api.')->group(function () {
                // Users API
                Route::get('/users/data', [\App\Http\Controllers\Admin\AdminController::class, 'getUsersData'])->name('users.data');
                Route::get('/users/{user}/details', [\App\Http\Controllers\Admin\AdminController::class, 'getUserDetails'])->name('users.details');
                Route::post('/users/{user}/suspend', [\App\Http\Controllers\Admin\AdminController::class, 'suspendUser'])->name('users.suspend');
                Route::post('/users/{user}/activate', [\App\Http\Controllers\Admin\AdminController::class, 'activateUser'])->name('users.activate');

                // Management Users API
                Route::get('/management-users/data', [\App\Http\Controllers\Admin\AdminController::class, 'getManagementUsersData'])->name('management-users.data');

                // Meeting Events API
                Route::get('/meeting-events/data', [\App\Http\Controllers\Admin\MeetingEventController::class, 'getData'])->name('meeting-events.data');
                Route::post('/meeting-events/{meetingEvent}/toggle-status', [\App\Http\Controllers\Admin\MeetingEventController::class, 'toggleStatus'])->name('meeting-events.toggle-status');

                // Revenue API
                Route::get('/revenue/data', [\App\Http\Controllers\Admin\RevenueController::class, 'getData'])->name('revenue.data');

                // Contact Forms API
                Route::get('/contact-forms/data', [\App\Http\Controllers\Admin\ContactController::class, 'getData'])->name('contact-forms.data');

                // Settings API
                Route::get('/site-mode-status', [\App\Http\Controllers\Admin\SettingController::class, 'getSiteModeStatus'])->name('site-mode-status');
            });

            // Subscription Plans
            Route::get('/subscription-plans/data', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'getData'])->name('subscription-plans.data');
            Route::post('/subscription-plans/{subscriptionPlan}/toggle-status', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'toggleStatus'])->name('subscription-plans.toggle-status');
            Route::resource('subscription-plans', \App\Http\Controllers\Admin\SubscriptionPlanController::class);
        });
    }
}
