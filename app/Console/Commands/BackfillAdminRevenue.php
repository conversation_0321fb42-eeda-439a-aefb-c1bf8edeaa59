<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TimeSpendingBooking;
use App\Models\AdminRevenue;
use Illuminate\Support\Facades\DB;

class BackfillAdminRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:backfill-revenue {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backfill missing AdminRevenue records for paid bookings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $this->info('Checking for paid bookings missing AdminRevenue records...');

        // Get all paid bookings
        $paidBookings = TimeSpendingBooking::where('payment_status', 'paid')->get();
        $this->info("Found {$paidBookings->count()} paid bookings");

        // Find bookings without AdminRevenue records
        $missingRevenue = $paidBookings->filter(function($booking) {
            return !$booking->adminRevenue;
        });

        $this->info("Found {$missingRevenue->count()} bookings missing AdminRevenue records");

        if ($missingRevenue->count() === 0) {
            $this->info('No missing AdminRevenue records found. All paid bookings have revenue tracking.');
            return 0;
        }

        // Display what will be created
        $this->table(
            ['Booking ID', 'Client', 'Provider', 'Base Amount', 'Commission %', 'Commission Amount', 'Platform Fee'],
            $missingRevenue->map(function($booking) {
                return [
                    $booking->id,
                    $booking->client->name ?? 'N/A',
                    $booking->provider->name ?? 'N/A',
                    '₹' . number_format($booking->base_amount, 2),
                    $booking->commission_percentage . '%',
                    '₹' . number_format($booking->commission_amount, 2),
                    '₹' . number_format($booking->platform_fee, 2),
                ];
            })->toArray()
        );

        if ($isDryRun) {
            $this->info('DRY RUN: Would create ' . $missingRevenue->count() . ' AdminRevenue records');
            return 0;
        }

        if (!$this->confirm('Do you want to create these AdminRevenue records?')) {
            $this->info('Operation cancelled');
            return 0;
        }

        $createdCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($missingRevenue->count());
        $progressBar->start();

        foreach ($missingRevenue as $booking) {
            try {
                DB::transaction(function () use ($booking) {
                    AdminRevenue::createFromBooking($booking);
                });
                $createdCount++;
            } catch (\Exception $e) {
                $this->error("\nError creating AdminRevenue for booking #{$booking->id}: " . $e->getMessage());
                $errorCount++;
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("Successfully created {$createdCount} AdminRevenue records");
        if ($errorCount > 0) {
            $this->error("Failed to create {$errorCount} AdminRevenue records");
        }

        // Display final statistics
        $this->info("\nFinal commission statistics:");
        $totalCommission = AdminRevenue::getTotalCommission();
        $totalPlatformFees = AdminRevenue::getTotalPlatformFees();
        $totalRevenue = AdminRevenue::getTotalPlatformRevenue();

        $this->table(
            ['Metric', 'Amount'],
            [
                ['Total Commission', '₹' . number_format($totalCommission, 2)],
                ['Total Platform Fees', '₹' . number_format($totalPlatformFees, 2)],
                ['Total Admin Revenue', '₹' . number_format($totalRevenue, 2)],
            ]
        );

        return 0;
    }
}
