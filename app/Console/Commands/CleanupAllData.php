<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use App\Models\SubscriptionPlan;
use App\Models\Feature;
use App\Models\Setting;

class CleanupAllData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:cleanup {--force : Force cleanup without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up all user data while preserving subscription plans and features';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will delete ALL user data while keeping subscription plans and features. Are you sure?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $this->info('Starting data cleanup...');

        try {
            // Disable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Backup subscription plans and features
            $this->info('Backing up subscription plans and features...');
            $subscriptionPlans = SubscriptionPlan::all()->toArray();
            $features = Feature::all()->toArray();
            $settings = Setting::all()->toArray();

            // Clean user-related data
            $this->cleanUserData();

            // Clean booking and transaction data
            $this->cleanBookingData();

            // Clean chat and notification data
            $this->cleanCommunicationData();

            // Clean file uploads
            $this->cleanFileUploads();

            // Clean session and cache data
            $this->cleanSessionData();

            // Restore subscription plans and features
            $this->info('Restoring subscription plans and features...');
            $this->restoreEssentialData($subscriptionPlans, $features, $settings);

            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            $this->info('✅ Data cleanup completed successfully!');
            $this->info('📋 Preserved:');
            $this->info('   - Subscription Plans (' . count($subscriptionPlans) . ' plans)');
            $this->info('   - Features (' . count($features) . ' features)');
            $this->info('   - Settings (' . count($settings) . ' settings)');

        } catch (\Exception $e) {
            // Re-enable foreign key checks in case of error
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            
            $this->error('❌ Error during cleanup: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Clean all user-related data
     */
    private function cleanUserData()
    {
        $this->info('🧹 Cleaning user data...');

        $userTables = [
            'users',
            'user_subscriptions',
            'user_wallets',
            'user_wallet_transactions',
            'user_galleries',
            'user_availability_schedules',
            'password_reset_tokens',
        ];

        foreach ($userTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean booking and transaction data
     */
    private function cleanBookingData()
    {
        $this->info('🧹 Cleaning booking and transaction data...');

        $bookingTables = [
            'time_spending_bookings',
            'meeting_verifications',
            'disputes',
            'admin_revenues',
            'couple_activity_requests',
            'couple_activity_meetings',
            'meeting_event_bookings',
            'rating_reviews',
        ];

        foreach ($bookingTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean communication data
     */
    private function cleanCommunicationData()
    {
        $this->info('🧹 Cleaning communication data...');

        $communicationTables = [
            'chats',
            'chat_messages',
            'notifications',
            'contact_submissions',
        ];

        foreach ($communicationTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean file uploads
     */
    private function cleanFileUploads()
    {
        $this->info('🧹 Cleaning file uploads...');

        $directories = [
            'public/profile_pictures',
            'public/gallery_images',
            'public/meeting_verifications',
            'public/dispute_evidence',
            'public/temp',
        ];

        foreach ($directories as $directory) {
            if (Storage::exists($directory)) {
                Storage::deleteDirectory($directory);
                $this->line("   - Cleaned {$directory}");
            }
        }
    }

    /**
     * Clean session and cache data
     */
    private function cleanSessionData()
    {
        $this->info('🧹 Cleaning session and cache data...');

        $sessionTables = [
            'sessions',
            'cache',
            'cache_locks',
            'jobs',
            'job_batches',
            'failed_jobs',
        ];

        foreach ($sessionTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Restore essential data
     */
    private function restoreEssentialData($subscriptionPlans, $features, $settings)
    {
        // Restore subscription plans
        foreach ($subscriptionPlans as $plan) {
            unset($plan['id']); // Let auto-increment handle IDs
            SubscriptionPlan::create($plan);
        }

        // Restore features
        foreach ($features as $feature) {
            unset($feature['id']); // Let auto-increment handle IDs
            Feature::create($feature);
        }

        // Restore settings
        foreach ($settings as $setting) {
            unset($setting['id']); // Let auto-increment handle IDs
            Setting::create($setting);
        }
    }
}
