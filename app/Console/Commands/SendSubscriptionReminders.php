<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSubscription;
use App\Models\Notification;
use Carbon\Carbon;

class SendSubscriptionReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send subscription expiry reminders to users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Sending subscription expiry reminders...');

        // Get subscriptions expiring in 3 days
        $threeDayReminders = UserSubscription::where('status', 'active')
            ->whereBetween('expires_at', [
                now()->addDays(3)->startOfDay(),
                now()->addDays(3)->endOfDay()
            ])
            ->with('user')
            ->get();

        // Get subscriptions expiring in 1 day
        $oneDayReminders = UserSubscription::where('status', 'active')
            ->whereBetween('expires_at', [
                now()->addDay()->startOfDay(),
                now()->addDay()->endOfDay()
            ])
            ->with('user')
            ->get();

        $remindersSent = 0;

        // Send 3-day reminders
        foreach ($threeDayReminders as $subscription) {
            $this->sendReminderNotification(
                $subscription->user,
                'Your Time Spending subscription expires in 3 days',
                "Your Time Spending Service subscription will expire on {$subscription->formatted_expiry_date}. Renew now to continue being visible to other users.",
                'warning'
            );
            $remindersSent++;
        }

        // Send 1-day reminders
        foreach ($oneDayReminders as $subscription) {
            $this->sendReminderNotification(
                $subscription->user,
                'Your Time Spending subscription expires tomorrow',
                "Your Time Spending Service subscription will expire tomorrow ({$subscription->formatted_expiry_date}). Renew now to avoid service interruption.",
                'urgent'
            );
            $remindersSent++;
        }

        $this->info("Sent {$remindersSent} subscription expiry reminders.");

        return 0;
    }

    /**
     * Send a reminder notification to the user.
     */
    private function sendReminderNotification($user, $title, $message, $type)
    {
        // Check if we already sent this type of reminder today
        $existingNotification = Notification::where('user_id', $user->id)
            ->where('title', $title)
            ->whereDate('created_at', today())
            ->first();

        if (!$existingNotification) {
            Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'is_read' => false,
            ]);
        }
    }
}
