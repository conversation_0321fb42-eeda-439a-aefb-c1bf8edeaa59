<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserSubscription;

class UpdateSubscriptionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update user subscription status and expire old subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating subscription statuses...');

        // Mark expired subscriptions as expired
        $expiredCount = UserSubscription::where('status', 'active')
            ->where('expires_at', '<=', now())
            ->update(['status' => 'expired']);

        $this->info("Marked {$expiredCount} subscriptions as expired.");

        // Update all users' subscription status
        $users = User::where('is_time_spending_enabled', true)->get();
        $updatedCount = 0;

        foreach ($users as $user) {
            $user->updateSubscriptionStatus();
            $updatedCount++;
        }

        $this->info("Updated subscription status for {$updatedCount} users.");
        $this->info('Subscription status update completed!');

        return 0;
    }
}
