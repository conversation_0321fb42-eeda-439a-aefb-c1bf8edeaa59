<?php

namespace App\Console\Commands;

use App\Models\TimeSpendingBooking;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendMeetingReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meeting:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send meeting end reminders at 15, 10, and 5 minutes before scheduled end time';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {

        // Get accepted bookings that have started (both start photos uploaded)
        $activeBookings = TimeSpendingBooking::where('provider_status', 'accepted')
            ->where('escrow_status', 'held')
            ->whereHas('meetingVerification', function ($query) {
                $query->whereNotNull('client_start_photo')
                      ->whereNotNull('provider_start_photo')
                      ->whereNull('client_end_photo')
                      ->whereNull('provider_end_photo');
            })
            ->with(['meetingVerification', 'client', 'provider'])
            ->get();

        $remindersSent = 0;

        foreach ($activeBookings as $booking) {
            $verification = $booking->meetingVerification;
            
            if (!$verification || !$verification->meeting_start_time) {
                continue;
            }

            // Calculate expected end time based on booking duration
            $expectedEndTime = Carbon::parse($verification->meeting_start_time)
                ->addHours((float) $booking->duration_hours);
            
            $now = now();
            $minutesUntilEnd = $now->diffInMinutes($expectedEndTime, false);

            // Send reminders at 15, 10, and 5 minutes before end
            $reminderTimes = [15, 10, 5];
            
            foreach ($reminderTimes as $reminderMinutes) {
                if ($minutesUntilEnd <= $reminderMinutes && $minutesUntilEnd > ($reminderMinutes - 1)) {
                    $this->sendMeetingEndReminder($booking, $reminderMinutes);
                    $remindersSent++;
                }
            }
        }

        $this->info("Sent {$remindersSent} meeting end reminders.");
        
        return Command::SUCCESS;
    }

    /**
     * Send meeting end reminder to both client and provider.
     */
    private function sendMeetingEndReminder(TimeSpendingBooking $booking, int $minutesLeft): void
    {
        $message = "Your meeting will end in {$minutesLeft} minutes. Please take a photo together to complete the meeting verification.";
        
        try {
            // Send to client
            Notification::create([
                'user_id' => $booking->client_id,
                'type' => 'meeting_end_reminder',
                'title' => "Meeting Ending Soon - {$minutesLeft} min left",
                'message' => $message,
                'body' => $message,
                'data' => [
                    'booking_id' => $booking->id,
                    'minutes_left' => $minutesLeft,
                ],
            ]);

            // Send to provider
            Notification::create([
                'user_id' => $booking->provider_id,
                'type' => 'meeting_end_reminder',
                'title' => "Meeting Ending Soon - {$minutesLeft} min left",
                'message' => $message,
                'body' => $message,
                'data' => [
                    'booking_id' => $booking->id,
                    'minutes_left' => $minutesLeft,
                ],
            ]);

            Log::info('Meeting end reminder sent', [
                'booking_id' => $booking->id,
                'minutes_left' => $minutesLeft,
            ]);

            $this->info("Sent {$minutesLeft}-minute reminder for booking #{$booking->id}");
            
        } catch (\Exception $e) {
            Log::error('Failed to send meeting end reminder', [
                'booking_id' => $booking->id,
                'minutes_left' => $minutesLeft,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
