<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ClearAllData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:clear-all {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all user data including transactions, notifications, bookings, chats, event locations, and users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will permanently delete ALL user data including transactions, notifications, bookings, chats, event locations, and users. Are you sure?')) {
                return;
            }
        }

        try {
            $driver = DB::getDriverName();

            if ($driver === 'mysql') {
                // MySQL specific commands
                DB::statement('SET FOREIGN_KEY_CHECKS=0;');

                $tables = DB::select('SHOW TABLES');
                $databaseName = DB::getDatabaseName();
                $tableColumn = 'Tables_in_' . $databaseName;

                foreach ($tables as $table) {
                    $tableName = $table->$tableColumn;

                    // Skip migrations table and admin-related tables
                    if (in_array($tableName, ['migrations', 'failed_jobs', 'jobs', 'job_batches'])) {
                        continue;
                    }

                    // Special handling for users table - keep admin users
                    if ($tableName === 'users') {
                        $adminCount = DB::table('users')->where('is_admin', true)->count();
                        $totalCount = DB::table('users')->count();
                        $userCount = $totalCount - $adminCount;

                        DB::table('users')->where('is_admin', false)->delete();
                        continue;
                    }

                    // Truncate table
                    DB::table($tableName)->truncate();
                }

                DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            } elseif ($driver === 'sqlite') {
                // SQLite specific commands
                DB::statement('PRAGMA foreign_keys = OFF;');

                $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name != 'migrations'");

                foreach ($tables as $table) {
                    $tableName = $table->name;

                    // Skip system tables
                    if (in_array($tableName, ['migrations', 'failed_jobs', 'jobs', 'job_batches'])) {
                        continue;
                    }

                    // Special handling for users table - keep admin users
                    if ($tableName === 'users') {
                        $adminCount = DB::table('users')->where('is_admin', true)->count();
                        $totalCount = DB::table('users')->count();
                        $userCount = $totalCount - $adminCount;

                        DB::table('users')->where('is_admin', false)->delete();
                        continue;
                    }

                    // Delete all records instead of truncate for SQLite
                    DB::table($tableName)->delete();
                }

                DB::statement('PRAGMA foreign_keys = ON;');

            } else {
                // Generic approach for other databases
                $tables = Schema::getAllTables();

                foreach ($tables as $table) {
                    $tableName = array_values((array) $table)[0];

                    // Skip system tables
                    if (in_array($tableName, ['migrations', 'failed_jobs', 'jobs', 'job_batches'])) {
                        continue;
                    }

                    // Special handling for users table - keep admin users
                    if ($tableName === 'users') {
                        $adminCount = DB::table('users')->where('is_admin', true)->count();
                        $totalCount = DB::table('users')->count();
                        $userCount = $totalCount - $adminCount;

                        DB::table('users')->where('is_admin', false)->delete();
                        continue;
                    }

                    // Delete all records
                    DB::table($tableName)->delete();
                }
            }

            // Data cleared successfully

        } catch (\Exception $e) {
            // Failed to clear data - log error silently

            // Re-enable foreign key checks in case of error
            try {
                $driver = DB::getDriverName();
                if ($driver === 'mysql') {
                    DB::statement('SET FOREIGN_KEY_CHECKS=1;');
                } elseif ($driver === 'sqlite') {
                    DB::statement('PRAGMA foreign_keys = ON;');
                }
            } catch (\Exception $e2) {
                // Ignore this error
            }

            return 1;
        }

        return 0;
    }

    /**
     * Get appropriate emoji for table name.
     */
    private function getTableEmoji(string $tableName): string
    {
        return '';
    }
}
