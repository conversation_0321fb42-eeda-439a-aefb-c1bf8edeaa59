<?php

namespace App\Console\Commands;

use App\Models\TimeSpendingBooking;
use App\Models\AdminRevenue;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateAdminRevenue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:migrate-revenue {--force : Force migration even if records exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing booking data to create admin revenue records';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Check if admin revenue records already exist
        $existingRecords = AdminRevenue::count();
        if ($existingRecords > 0 && !$this->option('force')) {
            if (!$this->confirm('Do you want to continue and create records for bookings without revenue records?')) {
                return 0;
            }
        }

        // Get all paid bookings that don't have admin revenue records
        $bookingsQuery = TimeSpendingBooking::where('payment_status', 'paid')
            ->where('escrow_status', 'released')
            ->whereDoesntHave('adminRevenue');

        $totalBookings = $bookingsQuery->count();

        if ($totalBookings === 0) {
            return 0;
        }

        $createdRecords = 0;
        $errors = 0;

        // Process bookings in chunks to avoid memory issues
        $bookingsQuery->chunk(100, function ($bookings) use ($progressBar, &$createdRecords, &$errors) {
            foreach ($bookings as $booking) {
                try {
                    DB::transaction(function () use ($booking) {
                        // Ensure booking has required fields
                        if (!$booking->platform_fee && !$booking->commission_amount) {
                            // Calculate missing values if needed
                            $platformFee = $booking->platform_fee ?? 0;
                            $commissionAmount = $booking->commission_amount ?? 0;

                            // If commission amount is missing, calculate it
                            if (!$commissionAmount && $booking->base_amount && $booking->commission_percentage) {
                                $commissionAmount = ($booking->base_amount * $booking->commission_percentage) / 100;
                            }

                            // Update booking with calculated values if needed
                            if (!$booking->commission_amount && $commissionAmount > 0) {
                                $booking->update(['commission_amount' => $commissionAmount]);
                            }
                        }

                        // Create admin revenue record
                        AdminRevenue::createFromBooking($booking);
                    });

                    $createdRecords++;
                } catch (\Exception $e) {
                    $errors++;
                    // Log error silently for production
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();

        // Display summary statistics only if requested
        if ($this->option('verbose')) {
            $this->displaySummary();
        }

        return 0;
    }

    /**
     * Display summary statistics.
     */
    private function displaySummary(): void
    {
        $totalRevenue = AdminRevenue::getTotalPlatformRevenue();
        $totalPlatformFees = AdminRevenue::getTotalPlatformFees();
        $totalCommission = AdminRevenue::getTotalCommission();
        $totalTransactions = AdminRevenue::count();

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Revenue', '₹' . number_format($totalRevenue, 2)],
                ['Platform Fees', '₹' . number_format($totalPlatformFees, 2)],
                ['Commission Earnings', '₹' . number_format($totalCommission, 2)],
                ['Total Transactions', number_format($totalTransactions)],
            ]
        );
    }
}
