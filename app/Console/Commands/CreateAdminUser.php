<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create-super-admin {--force : Force creation even if user exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create or update the super admin user with specified credentials';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = '<EMAIL>';
        $password = 'RekTech@27';
        $name = 'RekTech Super Admin';

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser && !$this->option('force')) {
            if (!$this->confirm("User with email {$email} already exists. Do you want to update their credentials?")) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        try {
            if ($existingUser) {
                // Update existing user
                $existingUser->update([
                    'name' => $name,
                    'password' => Hash::make($password),
                    'is_admin' => true,
                    'role' => 'super_admin',
                    'email_verified_at' => now(),
                ]);

                $this->info("✅ Super admin user updated successfully!");
                $this->info("Email: {$email}");
                $this->info("Password: {$password}");
                $this->info("Role: Super Admin");
            } else {
                // Create new user
                $user = User::create([
                    'name' => $name,
                    'email' => $email,
                    'password' => Hash::make($password),
                    'is_admin' => true,
                    'role' => 'super_admin',
                    'email_verified_at' => now(),
                    'contact_number' => '+44 7000 000000',
                    'gender' => 'male',
                    'date_of_birth' => '1990-01-01',
                    'interests' => 'System Administration, Technology',
                    'expectation' => 'Platform Management and Administration',
                ]);

                $this->info("✅ Super admin user created successfully!");
                $this->info("Email: {$email}");
                $this->info("Password: {$password}");
                $this->info("Role: Super Admin");
                $this->info("User ID: {$user->id}");
            }

            $this->newLine();
            $this->info("🔐 Admin Login Details:");
            $this->info("URL: " . url('/admin/login'));
            $this->info("Email: {$email}");
            $this->info("Password: {$password}");

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Failed to create/update super admin user: " . $e->getMessage());
            return 1;
        }
    }
}
