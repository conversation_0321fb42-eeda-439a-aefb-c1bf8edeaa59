<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TimeSpendingBooking;
use App\Models\MeetingVerification;
use App\Models\Notification;
use Carbon\Carbon;

class DetectNoShowBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:detect-no-show';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Detect potential no-show bookings and notify clients about dispute options';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        // Find bookings that:
        // 1. Are accepted and paid
        // 2. Meeting time ended more than 2 hours ago
        // 3. No meeting verification photos uploaded
        // 4. Not already disputed
        // 5. Not already notified about no-show

        $cutoffTime = Carbon::now()->subHours(2);
        
        $potentialNoShows = TimeSpendingBooking::with(['client', 'provider', 'meetingVerification'])
            ->where('provider_status', 'accepted')
            ->where('payment_status', 'paid')
            ->where('escrow_status', 'held')
            ->whereNull('dispute_reason')
            ->whereRaw('DATE_ADD(booking_date, INTERVAL duration_hours HOUR) < ?', [$cutoffTime])
            ->get()
            ->filter(function ($booking) {
                // Check if meeting verification exists and has photos
                $verification = $booking->meetingVerification;
                if (!$verification) {
                    return true; // No verification at all
                }
                
                // Check if both parties uploaded start photos but no end photos
                $hasStartPhotos = !empty($verification->client_start_photo) && !empty($verification->provider_start_photo);
                $hasEndPhotos = !empty($verification->client_end_photo) && !empty($verification->provider_end_photo);
                
                // Potential no-show if no verification photos at all, or start photos but no end photos
                return !$hasStartPhotos || ($hasStartPhotos && !$hasEndPhotos);
            })
            ->filter(function ($booking) {
                // Check if client hasn't been notified about no-show option yet
                $existingNotification = Notification::where('user_id', $booking->client_id)
                    ->where('type', 'no_show_detected')
                    ->whereJsonContains('data->booking_id', $booking->id)
                    ->exists();
                
                return !$existingNotification;
            });

        $notifiedCount = 0;

        foreach ($potentialNoShows as $booking) {
            try {
                // Create notification for client about potential no-show
                Notification::create([
                    'user_id' => $booking->client_id,
                    'type' => 'no_show_detected',
                    'title' => 'Potential No-Show Detected',
                    'message' => "It appears your meeting with {$booking->provider->name} may not have taken place. You can report this if the provider didn't show up.",
                    'body' => "We noticed that your scheduled meeting with {$booking->provider->name} on {$booking->booking_date->format('M d, Y')} at {$booking->booking_date->format('h:i A')} may not have been completed. If the provider failed to show up, you can report this incident to request a refund.",
                    'data' => [
                        'booking_id' => $booking->id,
                        'provider_name' => $booking->provider->name,
                        'booking_date' => $booking->booking_date->toISOString(),
                        'can_dispute' => true,
                    ],
                ]);

                $notifiedCount++;

            } catch (\Exception $e) {
                \Log::error('Failed to create no-show notification', [
                    'booking_id' => $booking->id,
                    'client_id' => $booking->client_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return Command::SUCCESS;
    }
}
