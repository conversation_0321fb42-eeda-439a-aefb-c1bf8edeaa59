<?php

namespace App\Console\Commands;

use App\Models\Feature;
use App\Models\Setting;
use App\Models\User;
use App\Models\MeetingAddress;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class FixSystemIssues extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:fix-system {--force : Force fixes without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Fix common system issues after data cleanup';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will fix system issues and may modify data. Continue?')) {
                return;
            }
        }

        // Fix essential features
        $this->fixEssentialFeatures();

        // Ensure admin users exist
        $this->ensureAdminUsers();

        // Create missing meeting addresses
        $this->createMeetingAddresses();

        // Fix storage directories
        $this->fixStorageDirectories();

        // Clear and rebuild cache
        $this->rebuildCache();
    }

    private function fixEssentialFeatures()
    {
        // Ensure all features exist
        $this->call('db:seed', ['--class' => 'FeatureSeeder']);

        // Enable essential features
        $essentialFeatures = [
            'notifications' => true,
            'meeting_events' => true,
            'gallery' => true,
        ];

        foreach ($essentialFeatures as $featureName => $enabled) {
            $feature = Feature::where('name', $featureName)->first();
            if ($feature) {
                $feature->update(['is_enabled' => $enabled]);
            }
        }

        // Disable advanced features by default
        $advancedFeatures = ['time_spending', 'partner_swapping', 'sugar_partner']; // partner_swapping = Couple Events
        foreach ($advancedFeatures as $featureName) {
            $feature = Feature::where('name', $featureName)->first();
            if ($feature && $feature->is_enabled) {
                $feature->update(['is_enabled' => false]);
            }
        }
    }

    private function ensureAdminUsers()
    {
        $adminCount = User::where('is_admin', true)->count();
        if ($adminCount === 0) {
            // Create default admin user
            $admin = User::create([
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
                'is_admin' => true,
                'role' => 'admin',
                'contact_number' => '+91 9999999999',
                'gender' => 'male',
                'date_of_birth' => '1990-01-01',
                'interests' => 'System Administration',
                'expectation' => 'Manage the platform',
            ]);
        }
    }

    private function createMeetingAddresses()
    {
        $meetingCount = MeetingAddress::count();
        if ($meetingCount === 0) {
            $this->call('db:seed', ['--class' => 'EssentialDataSeeder']);
        }
    }

    private function fixStorageDirectories()
    {
        $directories = [
            'profile-pictures',
            'gallery',
            'logos',
            'uploads',
            'temp'
        ];

        foreach ($directories as $dir) {
            if (!Storage::disk('public')->exists($dir)) {
                Storage::disk('public')->makeDirectory($dir);
            }
        }

        // Ensure storage link exists
        if (!file_exists(public_path('storage'))) {
            $this->call('storage:link');
        }
    }

    private function rebuildCache()
    {
        // Clear all caches
        $this->call('cache:clear');
        $this->call('config:clear');
        $this->call('route:clear');
        $this->call('view:clear');

        // Rebuild optimized caches
        $this->call('config:cache');
        $this->call('route:cache');
    }
}
