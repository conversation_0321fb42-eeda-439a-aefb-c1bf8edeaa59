<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TimeSpendingBooking;
use Carbon\Carbon;

class CheckSchedulerStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scheduler:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check scheduler status and show pending auto-cancellation bookings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Check auto-cancellation configuration
        $this->checkAutoCancellationConfig();

        // Check for bookings that need auto-cancellation
        $this->checkPendingAutoCancellations();
    }

    /**
     * Check auto-cancellation configuration.
     */
    private function checkAutoCancellationConfig()
    {
        $this->info('Auto-Cancellation Configuration:');
        
        $enabled = config('booking.auto_cancellation.enabled', false);
        $interval = config('booking.auto_cancellation.check_interval_minutes', 5);
        $sendNotification = config('booking.auto_cancellation.send_provider_notification', true);

        $this->line("- Enabled: " . ($enabled ? '✅ Yes' : '❌ No'));
        $this->line("- Check Interval: {$interval} minutes");
        $this->line("- Send Provider Notifications: " . ($sendNotification ? '✅ Yes' : '❌ No'));
    }

    /**
     * Check for bookings that need auto-cancellation.
     */
    private function checkPendingAutoCancellations()
    {
        $currentTime = Carbon::now('Asia/Kolkata');

        $bookingsToCancel = TimeSpendingBooking::with(['client', 'provider'])
            ->where('provider_status', 'pending')
            ->where('payment_status', 'paid')
            ->where('status', 'confirmed')
            ->where('booking_date', '<=', $currentTime)
            ->get();

        // Process bookings silently for production
    }


}
