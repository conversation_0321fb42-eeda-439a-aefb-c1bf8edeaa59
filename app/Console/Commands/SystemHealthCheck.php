<?php

namespace App\Console\Commands;

use App\Models\Feature;
use App\Models\Setting;
use App\Models\User;
use App\Models\MeetingAddress;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SystemHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:health-check {--fix : Automatically fix issues where possible}';

    /**
     * The console command description.
     */
    protected $description = 'Check system health and identify potential issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $issues = [];
        $fixes = [];

        // Check database connectivity
        $this->checkDatabaseConnectivity($issues);

        // Check essential settings
        $this->checkEssentialSettings($issues, $fixes);

        // Check features configuration
        $this->checkFeaturesConfiguration($issues, $fixes);

        // Check admin users
        $this->checkAdminUsers($issues, $fixes);

        // Check meeting addresses
        $this->checkMeetingAddresses($issues, $fixes);

        // Check file storage
        $this->checkFileStorage($issues, $fixes);

        // Check environment configuration
        $this->checkEnvironmentConfiguration($issues);

        // Apply fixes if requested
        if ($this->option('fix') && !empty($fixes)) {
            foreach ($fixes as $fix) {
                $fix();
            }
        }

        return empty($issues) ? 0 : 1;
    }

    private function checkDatabaseConnectivity(&$issues)
    {
        try {
            DB::connection()->getPdo();
        } catch (\Exception $e) {
            $issues[] = 'Database connectivity failed: ' . $e->getMessage();
        }
    }

    private function checkEssentialSettings(&$issues, &$fixes)
    {
        $essentialSettings = [
            'google_client_id' => 'Google OAuth Client ID',
            'razorpay_key_id' => 'Razorpay Key ID',
            'copyright_text' => 'Copyright Text',
        ];

        $settingsOk = true;
        foreach ($essentialSettings as $key => $label) {
            $setting = Setting::where('key', $key)->first();
            if (!$setting || empty($setting->value)) {
                $issues[] = "Missing essential setting: {$label}";
                $settingsOk = false;
            }
        }

        // Settings checked silently
    }

    private function checkFeaturesConfiguration(&$issues, &$fixes)
    {
        $featureCount = Feature::count();
        if ($featureCount < 5) {
            $issues[] = "Insufficient features configured ({$featureCount} found, expected at least 5)";
            $fixes[] = function() {
                $this->call('db:seed', ['--class' => 'FeatureSeeder']);
            };
            // Features configuration insufficient
        }

        // Check if essential features are enabled
        $essentialFeatures = ['chat_system', 'notifications', 'meeting_events'];
        foreach ($essentialFeatures as $featureName) {
            $feature = Feature::where('name', $featureName)->first();
            if (!$feature || !$feature->is_enabled) {
                $issues[] = "Essential feature '{$featureName}' is not enabled";
                $fixes[] = function() use ($featureName) {
                    Feature::where('name', $featureName)->update(['is_enabled' => true]);
                };
            }
        }
    }

    private function checkAdminUsers(&$issues, &$fixes)
    {
        $adminCount = User::where('is_admin', true)->count();
        if ($adminCount === 0) {
            $issues[] = 'No admin users found';
        }
    }

    private function checkMeetingAddresses(&$issues, &$fixes)
    {
        $meetingCount = MeetingAddress::count();
        if ($meetingCount === 0) {
            $issues[] = 'No meeting addresses configured';
            $fixes[] = function() {
                $this->call('db:seed', ['--class' => 'EssentialDataSeeder']);
            };
        }
    }

    private function checkFileStorage(&$issues)
    {
        $directories = ['profile-pictures', 'gallery', 'logos'];
        $storageOk = true;

        foreach ($directories as $dir) {
            if (!Storage::disk('public')->exists($dir)) {
                $issues[] = "Storage directory missing: {$dir}";
                $storageOk = false;
            }
        }


    }

    private function checkEnvironmentConfiguration(&$issues)
    {
        $requiredEnvVars = [
            'APP_KEY' => 'Application encryption key',
            'GOOGLE_CLIENT_ID' => 'Google OAuth Client ID',
            'RAZORPAY_KEY_ID' => 'Razorpay Key ID',
        ];

        $envOk = true;
        foreach ($requiredEnvVars as $var => $description) {
            if (empty(env($var))) {
                $issues[] = "Missing environment variable: {$var} ({$description})";
                $envOk = false;
            }
        }

        // Environment configuration checked silently
    }
}
