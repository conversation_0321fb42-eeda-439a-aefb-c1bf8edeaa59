<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;
use Carbon\Carbon;

class CheckSiteModes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'site:check-modes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and automatically disable site modes when their scheduled time expires';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking site modes...');

        $modesDisabled = 0;

        // Check maintenance mode
        $maintenanceEnabled = Setting::get('maintenance_mode_enabled', '0') === '1';
        if ($maintenanceEnabled) {
            $endTime = Setting::get('maintenance_mode_end_time');
            if ($endTime && Carbon::parse($endTime)->isPast()) {
                Setting::set('maintenance_mode_enabled', '0');
                $this->info('Maintenance mode automatically disabled - scheduled end time reached.');
                $modesDisabled++;
            } else {
                $this->info('Maintenance mode is active' . ($endTime ? ' until ' . Carbon::parse($endTime)->format('M j, Y g:i A') : ' (no end time set)'));
            }
        }

        // Check coming soon mode
        $comingSoonEnabled = Setting::get('coming_soon_mode_enabled', '0') === '1';
        if ($comingSoonEnabled) {
            $launchTime = Setting::get('coming_soon_launch_time');
            if ($launchTime && Carbon::parse($launchTime)->isPast()) {
                Setting::set('coming_soon_mode_enabled', '0');
                $this->info('Coming soon mode automatically disabled - launch time reached.');
                $modesDisabled++;
            } else {
                $this->info('Coming soon mode is active' . ($launchTime ? ' until ' . Carbon::parse($launchTime)->format('M j, Y g:i A') : ' (no launch time set)'));
            }
        }

        if (!$maintenanceEnabled && !$comingSoonEnabled) {
            $this->info('No site modes are currently active.');
        }

        if ($modesDisabled > 0) {
            $this->info("Total modes disabled: {$modesDisabled}");
        }

        return Command::SUCCESS;
    }
}
