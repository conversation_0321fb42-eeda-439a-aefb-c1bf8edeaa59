<?php

namespace App\Console\Commands;

use App\Models\TimeSpendingBooking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessEscrowPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'escrow:process-payments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process escrow payments that are ready for auto-release';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing escrow payments for auto-release...');

        // Find bookings that should be auto-released
        $bookingsToRelease = TimeSpendingBooking::where('escrow_status', 'held')
            ->whereNotNull('auto_release_at')
            ->where('auto_release_at', '<=', now())
            ->get();

        $releasedCount = 0;

        foreach ($bookingsToRelease as $booking) {
            try {
                $booking->releaseFromEscrow();
                $releasedCount++;

                Log::info('Auto-released escrow payment', [
                    'booking_id' => $booking->id,
                    'provider_id' => $booking->provider_id,
                    'amount' => $booking->total_amount,
                    'commission' => $booking->commission_amount,
                    'provider_amount' => $booking->provider_amount,
                ]);

                $this->info("Released payment for booking #{$booking->id} - Provider notified");

            } catch (\Exception $e) {
                Log::error('Failed to auto-release escrow payment', [
                    'booking_id' => $booking->id,
                    'error' => $e->getMessage(),
                ]);

                $this->error("Failed to release payment for booking #{$booking->id}: {$e->getMessage()}");
            }
        }

        $this->info("Processed {$releasedCount} escrow payments for auto-release.");
        
        return Command::SUCCESS;
    }
}
