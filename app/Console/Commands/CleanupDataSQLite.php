<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use App\Models\SubscriptionPlan;
use App\Models\Feature;
use App\Models\Setting;

class CleanupDataSQLite extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:cleanup-db {--force : Force cleanup without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up all user data while preserving admin users, subscription plans and features (Database compatible)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will delete ALL user data while keeping subscription plans and features. Are you sure?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $driver = DB::connection()->getDriverName();
        $this->info("Starting data cleanup for {$driver} database...");

        try {
            // Disable foreign key checks based on database type
            $this->disableForeignKeyChecks($driver);

            // Backup subscription plans and features
            $this->info('Backing up subscription plans and features...');
            $subscriptionPlans = SubscriptionPlan::all()->toArray();
            $features = Feature::all()->toArray();
            $settings = Setting::all()->toArray();

            // Clean user-related data
            $this->cleanUserData();

            // Clean booking and transaction data
            $this->cleanBookingData();

            // Clean chat and notification data
            $this->cleanCommunicationData();

            // Clean file uploads
            $this->cleanFileUploads();

            // Clean session and cache data
            $this->cleanSessionData();

            // Clean essential data tables (we'll restore them)
            $this->cleanEssentialData();

            // Restore subscription plans and features
            $this->info('Restoring subscription plans and features...');
            $this->restoreEssentialData($subscriptionPlans, $features, $settings);

            // Re-enable foreign key checks
            $this->enableForeignKeyChecks($driver);

            $this->info('✅ Data cleanup completed successfully!');
            $this->info('📋 Preserved:');
            $this->info('   - Subscription Plans (' . count($subscriptionPlans) . ' plans)');
            $this->info('   - Features (' . count($features) . ' features)');
            $this->info('   - Settings (' . count($settings) . ' settings)');

        } catch (\Exception $e) {
            // Re-enable foreign key checks in case of error
            $this->enableForeignKeyChecks($driver);

            $this->error('❌ Error during cleanup: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Clean all user-related data (except admin users)
     */
    private function cleanUserData()
    {
        $this->info('🧹 Cleaning user data (preserving admin users)...');

        // Clean users table but preserve admin users
        if (Schema::hasTable('users')) {
            $adminCount = DB::table('users')->where('is_admin', true)->orWhere('role', 'admin')->orWhere('role', 'super_admin')->count();
            DB::table('users')->where('is_admin', false)->where('role', '!=', 'admin')->where('role', '!=', 'super_admin')->delete();
            $this->line("   - Cleaned users (preserved {$adminCount} admin users)");
        }

        // Clean other user-related tables completely
        $userTables = [
            'user_subscriptions',
            'user_wallets',
            'user_wallet_transactions',
            'user_galleries',
            'user_availability_schedules',
            'password_reset_tokens',
        ];

        foreach ($userTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->delete();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean booking and transaction data
     */
    private function cleanBookingData()
    {
        $this->info('🧹 Cleaning booking and transaction data...');

        $bookingTables = [
            'time_spending_bookings',
            'meeting_verifications',
            'disputes',
            'admin_revenues', // Keep this for revenue tracking
            'couple_activity_requests',
            'couple_activity_meetings',
            'meeting_event_bookings',
            'rating_reviews',
        ];

        foreach ($bookingTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->delete();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean communication data
     */
    private function cleanCommunicationData()
    {
        $this->info('🧹 Cleaning communication data...');

        $communicationTables = [
            'chats',
            'chat_messages',
            'notifications',
            'contact_submissions',
        ];

        foreach ($communicationTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->delete();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean file uploads
     */
    private function cleanFileUploads()
    {
        $this->info('🧹 Cleaning file uploads...');

        $directories = [
            'profile_pictures',
            'gallery_images',
            'meeting_verifications',
            'dispute_evidence',
            'temp',
        ];

        foreach ($directories as $directory) {
            $fullPath = 'public/' . $directory;
            if (Storage::exists($fullPath)) {
                Storage::deleteDirectory($fullPath);
                $this->line("   - Cleaned {$fullPath}");
            }
        }
    }

    /**
     * Clean session and cache data
     */
    private function cleanSessionData()
    {
        $this->info('🧹 Cleaning session and cache data...');

        $sessionTables = [
            'sessions',
            'cache',
            'cache_locks',
            'jobs',
            'job_batches',
            'failed_jobs',
        ];

        foreach ($sessionTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->delete();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Clean essential data that will be restored
     */
    private function cleanEssentialData()
    {
        $this->info('🧹 Cleaning essential data (will be restored)...');

        $essentialTables = [
            'subscription_plans',
            'features',
            'settings',
        ];

        foreach ($essentialTables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->delete();
                $this->line("   - Cleaned {$table}");
            }
        }
    }

    /**
     * Disable foreign key checks based on database type
     */
    private function disableForeignKeyChecks($driver)
    {
        switch ($driver) {
            case 'mysql':
            case 'mariadb':
                DB::statement('SET FOREIGN_KEY_CHECKS=0;');
                break;
            case 'sqlite':
                DB::statement('PRAGMA foreign_keys = OFF;');
                break;
            case 'pgsql':
                // PostgreSQL doesn't have a global foreign key disable
                break;
        }
    }

    /**
     * Enable foreign key checks based on database type
     */
    private function enableForeignKeyChecks($driver)
    {
        switch ($driver) {
            case 'mysql':
            case 'mariadb':
                DB::statement('SET FOREIGN_KEY_CHECKS=1;');
                break;
            case 'sqlite':
                DB::statement('PRAGMA foreign_keys = ON;');
                break;
            case 'pgsql':
                // PostgreSQL doesn't have a global foreign key disable
                break;
        }
    }

    /**
     * Restore essential data
     */
    private function restoreEssentialData($subscriptionPlans, $features, $settings)
    {
        // Restore subscription plans
        foreach ($subscriptionPlans as $plan) {
            unset($plan['id']); // Let auto-increment handle IDs
            unset($plan['created_at']);
            unset($plan['updated_at']);
            SubscriptionPlan::create($plan);
        }

        // Restore features
        foreach ($features as $feature) {
            unset($feature['id']); // Let auto-increment handle IDs
            unset($feature['created_at']);
            unset($feature['updated_at']);
            Feature::create($feature);
        }

        // Restore settings
        foreach ($settings as $setting) {
            unset($setting['id']); // Let auto-increment handle IDs
            unset($setting['created_at']);
            unset($setting['updated_at']);
            Setting::create($setting);
        }
    }
}
