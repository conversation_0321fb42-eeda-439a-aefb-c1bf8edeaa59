<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheService
{
    /**
     * Cache duration constants.
     */
    const CACHE_SHORT = 300; // 5 minutes
    const CACHE_MEDIUM = 3600; // 1 hour
    const CACHE_LONG = 86400; // 24 hours
    const CACHE_VERY_LONG = 604800; // 1 week

    /**
     * Get cached data or execute callback and cache result.
     */
    public static function remember(string $key, int $ttl, callable $callback)
    {
        try {
            return Cache::remember($key, $ttl, $callback);
        } catch (\Exception $e) {
            Log::error('Cache error', [
                'key' => $key,
                'error' => $e->getMessage(),
            ]);
            
            // Return callback result without caching on error
            return $callback();
        }
    }

    /**
     * Cache user-specific data.
     */
    public static function rememberUser(int $userId, string $suffix, int $ttl, callable $callback)
    {
        $key = "user:{$userId}:{$suffix}";
        return self::remember($key, $ttl, $callback);
    }

    /**
     * Cache settings data.
     */
    public static function rememberSettings(string $key, callable $callback)
    {
        return self::remember("settings:{$key}", self::CACHE_LONG, $callback);
    }

    /**
     * Cache query results.
     */
    public static function rememberQuery(string $query, array $params, int $ttl, callable $callback)
    {
        $key = 'query:' . md5($query . serialize($params));
        return self::remember($key, $ttl, $callback);
    }

    /**
     * Invalidate user cache.
     */
    public static function forgetUser(int $userId, string $suffix = null): void
    {
        if ($suffix) {
            Cache::forget("user:{$userId}:{$suffix}");
        } else {
            // Clear all user cache
            $pattern = "user:{$userId}:*";
            self::forgetPattern($pattern);
        }
    }

    /**
     * Invalidate cache by pattern.
     */
    public static function forgetPattern(string $pattern): void
    {
        try {
            $keys = Cache::getRedis()->keys($pattern);
            if (!empty($keys)) {
                Cache::getRedis()->del($keys);
            }
        } catch (\Exception $e) {
            Log::error('Cache pattern forget error', [
                'pattern' => $pattern,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Clear all cache.
     */
    public static function flush(): void
    {
        try {
            Cache::flush();
            Log::info('Cache flushed successfully');
        } catch (\Exception $e) {
            Log::error('Cache flush error', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get cache statistics.
     */
    public static function getStats(): array
    {
        try {
            $redis = Cache::getRedis();
            $info = $redis->info();
            
            return [
                'memory_used' => $info['used_memory_human'] ?? 'N/A',
                'total_keys' => $redis->dbsize(),
                'hits' => $info['keyspace_hits'] ?? 0,
                'misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate($info),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Calculate cache hit rate.
     */
    private function calculateHitRate(array $info): string
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;
        
        if ($total === 0) {
            return '0%';
        }
        
        return round(($hits / $total) * 100, 2) . '%';
    }
}
