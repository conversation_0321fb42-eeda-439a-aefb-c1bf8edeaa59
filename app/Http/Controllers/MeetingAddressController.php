<?php

namespace App\Http\Controllers;

use App\Models\MeetingAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MeetingAddressController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // If couple events feature is disabled, disable all couple events
        if (!\App\Models\Feature::isEnabled('partner_swapping')) {
            MeetingAddress::where('is_couple_event', true)
                ->where('is_event_enabled', true)
                ->update(['is_event_enabled' => false]);
        }

        // Get enabled events only
        $enabledEvents = MeetingAddress::where('is_event_enabled', true)->get();

        // Add payment status for each event (only if profile is complete)
        $events = $enabledEvents->map(function ($event) use ($user) {
            $event->user_has_paid = $user->isProfileComplete() ? $user->hasPaidForEvent($event->id) : false;
            return $event;
        });

        return view('meeting_address', [
            'events' => $events,
            'userGender' => $user->gender,
            'isProfileComplete' => $user->isProfileComplete(),
            'profileCompletionPercentage' => $user->getProfileCompletionPercentage()
        ]);
    }

    public function show($id)
    {
        $user = Auth::user();

        // Check if user's profile is 100% complete
        if (!$user->isProfileComplete()) {
            return redirect()->route('event.address')
                ->with('error', 'Please complete your profile 100% before joining any events. Complete all sections: Basic Info, Profile Picture, and Interests.');
        }

        // Get the specific event
        $event = MeetingAddress::where('id', $id)
            ->where('is_event_enabled', true)
            ->firstOrFail();

        // Check if this is a couple event and handle accordingly
        $canRegister = true;
        $redirectToPartnerSetup = false;
        $coupleEventMessage = '';

        if ($event->is_couple_event) {
            // Check if couple events feature is enabled
            if (!\App\Models\Feature::isEnabled('partner_swapping')) {
                return redirect()->route('event.address')
                    ->with('error', 'This couple event is no longer available as the couple events feature has been disabled.');
            }

            // For couple events, check if user has couple activity enabled and a partner
            if (!$user->is_couple_activity_enabled || !$user->hasOwnPartner()) {
                $canRegister = false;
                $redirectToPartnerSetup = true;
                $coupleEventMessage = 'To register for this couple event: Go to Settings > Couple Activity, enable it, and add a partner';
            }
        }

        // Check if user has paid for this specific event
        $hasPaidForEvent = $user->hasPaidForEvent($event->id);

        // If user can register but hasn't paid for this event, redirect to payment page
        if ($canRegister && !$hasPaidForEvent) {
            return redirect()->route('payment.meeting-event', $event->id)
                ->with('info', 'Payment required to view event details.');
        }

        // Add payment status to event object for view compatibility
        $event->user_has_paid = $hasPaidForEvent;

        // Calculate payment amount based on event type and user's gender
        if ($event->is_couple_event) {
            $paymentAmount = $event->payment_amount_couple;
        } else {
            $paymentAmount = $user->gender === 'female'
                ? $event->payment_amount_girls
                : $event->payment_amount_boys;
        }

        return view('meeting_event_detail', [
            'event' => $event,
            'paymentAmount' => $paymentAmount,
            'userGender' => $user->gender,
            'hasPaidForEvent' => $hasPaidForEvent,
            'canRegister' => $canRegister,
            'redirectToPartnerSetup' => $redirectToPartnerSetup,
            'coupleEventMessage' => $coupleEventMessage
        ]);
    }

    public function joinNow($id)
    {
        $user = Auth::user();

        // Check if user's profile is complete
        if (!$user->isProfileComplete()) {
            return redirect()->route('profile.edit')
                ->with('profile_incomplete_message', 'Complete your profile to join events! This is completely safe and helps us connect you with the right people.');
        }

        // Get the event to check if it's a couple event
        $event = MeetingAddress::where('id', $id)
            ->where('is_event_enabled', true)
            ->firstOrFail();

        // Check if this is a couple event and user is eligible
        if ($event->is_couple_event) {
            // Check if couple events feature is enabled
            if (!\App\Models\Feature::isEnabled('partner_swapping')) {
                return redirect()->route('event.address')
                    ->with('error', 'This couple event is no longer available as the couple events feature has been disabled.');
            }

            if (!$user->is_couple_activity_enabled || !$user->hasOwnPartner()) {
                return redirect()->route('couple-activity.select-partner')
                    ->with('error', 'To register for this couple event, you need to enable couple activity and add a partner first.');
            }
        }

        // If profile is complete and couple requirements are met, redirect to payment page
        return redirect()->route('payment.meeting-event', $id);
    }
}
