<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * Display a listing of the user's notifications.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();

        // Mark all notifications as read when user opens the notifications page
        $user->notifications()->where('is_read', false)->update([
            'is_read' => true,
            'read_at' => now()
        ]);

        $notifications = $user->notifications()->latest()->paginate(10);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * Store a newly created notification in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'type' => 'required|string|in:general,payment,match,partner_swapping',
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user = User::find($request->user_id);
        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => $request->title,
            'body' => $request->body,
            'type' => $request->type,
            'data' => $request->data ?? null,
        ]);

        // Send FCM notification if token exists
        if ($user->fcm_token) {
            $this->sendFcmNotification($notification);
        }

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification sent successfully.');
    }

    /**
     * Mark a notification as read.
     *
     * @param  \App\Models\Notification  $notification
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsRead(Notification $notification)
    {
        $notification->is_read = true;
        $notification->save();

        return redirect()->back()
            ->with('success', 'Notification marked as read.');
    }

    /**
     * Mark all notifications as read.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAllAsRead()
    {
        Auth::user()->notifications()->where('is_read', false)->update([
            'is_read' => true,
            'read_at' => now()
        ]);

        return redirect()->back()
            ->with('success', 'All notifications marked as read.');
    }

    /**
     * Get unread notification count for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadCount()
    {
        $user = Auth::user();
        $count = $user->getUnreadNotificationCount();
        $formattedCount = $user->getFormattedNotificationCount();

        return response()->json([
            'success' => true,
            'count' => $count,
            'formatted_count' => $formattedCount,
            'has_unread' => $count > 0
        ]);
    }

    /**
     * Send a notification via Firebase Cloud Messaging.
     *
     * @param  \App\Models\Notification  $notification
     * @return void
     */
    private function sendFcmNotification(Notification $notification)
    {
        $projectId = Setting::get('firebase_project_id');
        $clientEmail = Setting::get('firebase_client_email');
        $privateKey = Setting::get('firebase_private_key');

        // In a real implementation, you would use the Firebase Admin SDK
        // to send the notification. For now, we'll just mark it as sent.

        $notification->sent_at = now();
        $notification->save();
    }
}
