<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\URL;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap.
     */
    public function index(): Response
    {
        $urls = $this->getSitemapUrls();
        
        $xml = $this->generateSitemapXml($urls);
        
        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Get all URLs for sitemap.
     */
    protected function getSitemapUrls(): array
    {
        $urls = [];
        
        // Main pages
        $mainPages = [
            ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => '/about-us', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['url' => '/how-it-works', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['url' => '/safety-tips', 'priority' => '0.9', 'changefreq' => 'monthly'],
            ['url' => '/help-center', 'priority' => '0.6', 'changefreq' => 'monthly'],
            ['url' => '/contact-us', 'priority' => '0.6', 'changefreq' => 'monthly'],
            ['url' => '/user-guide', 'priority' => '0.9', 'changefreq' => 'monthly'],
            ['url' => '/privacy-policy', 'priority' => '0.5', 'changefreq' => 'yearly'],
            ['url' => '/terms-of-service', 'priority' => '0.5', 'changefreq' => 'yearly'],
            ['url' => '/refund-policy', 'priority' => '0.5', 'changefreq' => 'yearly'],
        ];

        foreach ($mainPages as $page) {
            $urls[] = [
                'loc' => URL::to($page['url']),
                'lastmod' => now()->toISOString(),
                'changefreq' => $page['changefreq'],
                'priority' => $page['priority'],
            ];
        }

        return $urls;
    }

    /**
     * Generate sitemap XML.
     */
    protected function generateSitemapXml(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }
}
