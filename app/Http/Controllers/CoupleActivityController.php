<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use App\Models\Notification;
use App\Models\CoupleActivityRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class CoupleActivityController extends Controller
{
    /**
     * Show the couple activity index page.
     */
    public function index()
    {
        $user = Auth::user();

        if (!$user->canAccessCoupleActivity()) {
            return redirect()->route('profile.edit')->with('error', 'Couple activity feature is not available.');
        }

        // Redirect based on user's current status
        if (!$user->hasOwnPartner()) {
            return redirect()->route('couple-activity.select-partner');
        } else {
            return redirect()->route('couple-activity.couple-event');
        }
    }

    /**
     * Show the select partner page.
     */
    public function selectPartner()
    {
        $user = Auth::user();

        if (!$user->canAccessCoupleActivity()) {
            return redirect()->route('profile.edit')->with('error', 'Couple activity feature is not available.');
        }

        // If user already has a partner, redirect to couple-event page
        if ($user->hasOwnPartner()) {
            return redirect()->route('couple-activity.couple-event')->with('success', 'You already have a partner! You can now participate in couple events.');
        }

        // Get pending own partner requests received by this user
        $pendingRequests = $user->getPendingOwnPartnerRequests();

        // Get sent requests by the user
        $sentRequests = $user->sentCoupleActivityRequests()
            ->where('type', 'own_partner')
            ->where('status', 'pending')
            ->with('partner')
            ->get();

        // Get available users for partner selection (opposite gender, no existing partner, couple activity enabled)
        $availableUsers = User::where('id', '!=', $user->id)
            ->where('is_couple_activity_enabled', true)
            ->where('gender', '!=', $user->gender) // Only show opposite gender
            ->whereDoesntHave('sentCoupleActivityRequests', function($query) {
                $query->where('type', 'own_partner')
                      ->where('status', 'approved');
            })
            ->whereDoesntHave('receivedCoupleActivityRequests', function($query) {
                $query->where('type', 'own_partner')
                      ->where('status', 'approved');
            })
            ->whereDoesntHave('sentCoupleActivityRequests', function($query) use ($user) {
                $query->where('partner_id', $user->id)
                      ->where('type', 'own_partner')
                      ->whereIn('status', ['pending', 'blocked']);
            })
            ->whereDoesntHave('receivedCoupleActivityRequests', function($query) use ($user) {
                $query->where('requester_id', $user->id)
                      ->where('type', 'own_partner')
                      ->whereIn('status', ['pending', 'blocked']);
            })
            ->paginate(12);

        return view('couple-activity.select-own-partner', compact('user', 'pendingRequests', 'sentRequests', 'availableUsers'));
    }

    /**
     * Show the couple event page.
     */
    public function coupleEvent()
    {
        $user = Auth::user();

        if (!$user->canAccessCoupleActivity()) {
            return redirect()->route('profile.edit')->with('error', 'Couple activity feature is not available.');
        }

        if (!$user->hasOwnPartner()) {
            return redirect()->route('couple-activity.select-partner');
        }

        // Get user's own partner
        $ownPartner = $user->getOwnPartner();

        // Get couple events (events marked as couple events)
        $coupleEvents = \App\Models\MeetingAddress::where('is_couple_event', true)
            ->where('is_event_enabled', true)
            ->orderBy('event_date', 'asc')
            ->get();

        // Check if couple events feature is enabled
        $coupleEventsEnabled = \App\Models\Feature::isEnabled('partner_swapping');

        // If couple events feature is disabled, disable all couple events
        if (!$coupleEventsEnabled) {
            \App\Models\MeetingAddress::where('is_couple_event', true)
                ->where('is_event_enabled', true)
                ->update(['is_event_enabled' => false]);
            $coupleEvents = collect(); // Empty collection
        }

        // Add payment status for each event
        foreach ($coupleEvents as $event) {
            $event->user_has_paid = $user->hasPaidForEvent($event->id);
        }

        return view('couple-activity.couple-event', compact('user', 'ownPartner', 'coupleEvents', 'coupleEventsEnabled'));
    }
    /**
     * Search users for partner selection via AJAX.
     */
    public function searchUsers(Request $request)
    {
        $user = Auth::user();
        $query = $request->get('q');

        // Check if current user has couple activity enabled
        if (!$user->is_couple_activity_enabled) {
            return response()->json(['users' => [], 'error' => 'Couple activity not enabled for current user']);
        }

        if (strlen($query) < 2) {
            return response()->json(['users' => []]);
        }

        $users = User::where('id', '!=', $user->id)
            ->where('is_couple_activity_enabled', true)
            ->where('gender', '!=', $user->gender) // Only show opposite gender
            ->where(function($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('email', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'name', 'email', 'gender']);

        // Filter out blocked users and users who already have partners
        $filteredUsers = $users->filter(function($searchUser) use ($user) {
            // Check if there's a blocking relationship
            if ($user->hasBlockingRelationshipWith($searchUser)) {
                return false;
            }

            // Check if user already has a partner
            if ($searchUser->hasOwnPartner()) {
                return false;
            }

            return true;
        })->values();

        return response()->json(['users' => $filteredUsers]);
    }

    /**
     * Send a partner request.
     */
    public function sendRequest(Request $request)
    {
        $user = Auth::user();

        // Check if feature is enabled and user has access
        if (!$user->canAccessCoupleActivity()) {
            return response()->json(['success' => false, 'message' => 'Couple activity feature is not available.']);
        }

        $request->validate([
            'partner_id' => 'required|exists:users,id',
            'type' => 'required|in:own_partner,couple_swap',
            'message' => 'nullable|string|max:500',
        ]);

        $partnerId = $request->partner_id;
        $partner = User::findOrFail($partnerId);
        $requestType = $request->type;

        // Check if partner has couple activity enabled
        if (!$partner->is_couple_activity_enabled) {
            return response()->json(['success' => false, 'message' => 'This user is not available for couple activity.']);
        }

        // Check if user is trying to send request to themselves
        if ($partnerId == $user->id) {
            return response()->json(['success' => false, 'message' => 'You cannot send a request to yourself.']);
        }

        // Different validation based on request type
        if ($requestType === 'own_partner') {
            // Check gender compatibility (male can only add female, female can only add male)
            if ($user->gender === $partner->gender) {
                return response()->json(['success' => false, 'message' => 'You can only add a partner of the opposite gender.']);
            }

            // Check if user already has own partner
            if ($user->hasOwnPartner()) {
                return response()->json(['success' => false, 'message' => 'You already have a partner.']);
            }

            // Check if target user already has own partner
            if ($partner->hasOwnPartner()) {
                return response()->json(['success' => false, 'message' => 'This user already has a partner.']);
            }

            // Check if user has reached the maximum limit of 3 pending requests
            $pendingRequestsCount = CoupleActivityRequest::where('requester_id', $user->id)
                ->where('type', 'own_partner')
                ->where('status', 'pending')
                ->count();

            if ($pendingRequestsCount >= 3) {
                return response()->json(['success' => false, 'message' => 'You can only send a maximum of 3 partner requests at a time. Please wait for responses before sending more.']);
            }

            // Check if there's already a pending request
            if ($user->hasPendingOwnPartnerRequestWith($partner) || $user->hasReceivedOwnPartnerRequestFrom($partner)) {
                return response()->json(['success' => false, 'message' => 'A partner request already exists between you and this user.']);
            }
        } elseif ($requestType === 'couple_swap') {
            // Check if user has own partner (required for couple swap)
            if (!$user->hasOwnPartner()) {
                return response()->json(['success' => false, 'message' => 'You need to have a partner before sending couple activity requests.']);
            }

            // Check if target user has own partner (required for couple swap)
            if (!$partner->hasOwnPartner()) {
                return response()->json(['success' => false, 'message' => 'This user does not have a partner for couple activities.']);
            }

            // Check if there's already a pending couple swap request
            if ($user->hasPendingCoupleSwapRequestWith($partner) || $user->hasReceivedCoupleSwapRequestFrom($partner)) {
                return response()->json(['success' => false, 'message' => 'A couple activity request already exists between you and this user.']);
            }
        }

        // Check if there's a blocking relationship
        if ($user->hasBlockingRelationshipWith($partner)) {
            return response()->json(['success' => false, 'message' => 'You cannot send a request to this user.']);
        }

        // Check for existing request (any status) and handle re-sending
        $existingRequest = CoupleActivityRequest::where('requester_id', $user->id)
            ->where('partner_id', $partnerId)
            ->where('type', $requestType)
            ->first();

        if ($existingRequest) {
            // If blocked, don't allow re-sending
            if ($existingRequest->status === 'blocked') {
                return response()->json(['success' => false, 'message' => 'You cannot send a request to this user.']);
            }

            // If rejected, allow re-sending by updating the existing request
            if ($existingRequest->status === 'rejected') {
                $existingRequest->update([
                    'status' => 'pending',
                    'message' => $request->message,
                    'rejection_reason' => null,
                    'responded_at' => null,
                    'updated_at' => now(),
                ]);
                $partnerRequest = $existingRequest;
            } else {
                // For any other status, don't allow
                return response()->json(['success' => false, 'message' => 'A request already exists with this user.']);
            }
        } else {
            // Create new request
            $partnerRequest = CoupleActivityRequest::create([
                'requester_id' => $user->id,
                'partner_id' => $partnerId,
                'type' => $requestType,
                'message' => $request->message,
                'status' => 'pending',
            ]);
        }

        // Send notification to the partner
        $notificationTitle = $requestType === 'own_partner' ? 'Partner Request' : 'Couple Activity Request';
        $notificationBody = $requestType === 'own_partner'
            ? "{$user->name} wants to be your partner."
            : "{$user->name} wants to connect for couple activities.";

        Notification::create([
            'user_id' => $partnerId,
            'title' => $notificationTitle,
            'body' => $notificationBody,
            'type' => 'couple_activity',
            'data' => [
                'request_id' => $partnerRequest->id,
                'requester_id' => $user->id,
                'requester_name' => $user->name,
                'request_type' => $requestType,
            ],
        ]);

        $successMessage = $requestType === 'own_partner'
            ? 'Partner request sent successfully!'
            : 'Couple activity request sent successfully!';

        return response()->json(['success' => true, 'message' => $successMessage]);
    }

    /**
     * Approve a partner request.
     */
    public function approveRequest(CoupleActivityRequest $coupleActivityRequest): RedirectResponse
    {
        $user = Auth::user();

        // Check if the request belongs to the current user
        if ($coupleActivityRequest->partner_id !== $user->id) {
            return redirect()->back()->with('error', 'You are not authorized to approve this request.');
        }

        // Check if request is still pending
        if (!$coupleActivityRequest->isPending()) {
            return redirect()->back()->with('error', 'This request has already been responded to.');
        }

        // Different validation based on request type
        if ($coupleActivityRequest->type === 'own_partner') {
            // Check if user already has a partner
            if ($user->hasOwnPartner()) {
                return redirect()->back()->with('error', 'You already have a partner.');
            }
        } elseif ($coupleActivityRequest->type === 'couple_swap') {
            // For couple swap, user should have a partner
            if (!$user->hasOwnPartner()) {
                return redirect()->back()->with('error', 'You need to have a partner to participate in couple activities.');
            }
        }

        // Approve the request
        $coupleActivityRequest->approve();

        // Update status only for own_partner requests
        if ($coupleActivityRequest->type === 'own_partner') {
            $user->update(['couple_activity_status' => 'paired']);
            $coupleActivityRequest->requester->update(['couple_activity_status' => 'paired']);

            // Auto-decline all other pending partner requests for both users
            $this->autoDeclineOtherPartnerRequests($user, $coupleActivityRequest->requester, $coupleActivityRequest->id);
        }

        // Send notification to the requester
        $notificationTitle = $coupleActivityRequest->type === 'own_partner'
            ? 'Partner Request Approved'
            : 'Couple Activity Request Approved';
        $notificationBody = $coupleActivityRequest->type === 'own_partner'
            ? "{$user->name} has accepted your partner request!"
            : "{$user->name} has accepted your couple activity request!";

        Notification::create([
            'user_id' => $coupleActivityRequest->requester_id,
            'title' => $notificationTitle,
            'body' => $notificationBody,
            'type' => 'couple_activity',
            'data' => [
                'request_id' => $coupleActivityRequest->id,
                'partner_id' => $user->id,
                'partner_name' => $user->name,
                'status' => 'approved',
            ],
        ]);

        $successMessage = $coupleActivityRequest->type === 'own_partner'
            ? 'Partner request approved successfully!'
            : 'Couple activity request approved successfully!';

        return redirect()->back()->with('success', $successMessage);
    }

    /**
     * Reject a partner request.
     */
    public function rejectRequest(Request $requestData, CoupleActivityRequest $coupleActivityRequest): RedirectResponse
    {
        $user = Auth::user();

        // Check if the request belongs to the current user
        if ($coupleActivityRequest->partner_id !== $user->id) {
            return redirect()->back()->with('error', 'You are not authorized to reject this request.');
        }

        // Check if request is still pending
        if (!$coupleActivityRequest->isPending()) {
            return redirect()->back()->with('error', 'This request has already been responded to.');
        }

        // Reject the request
        $coupleActivityRequest->reject('Not interested');

        // Send notification to the requester
        $notificationTitle = $coupleActivityRequest->type === 'own_partner'
            ? 'Partner Request Declined'
            : 'Couple Activity Request Declined';
        $notificationBody = $coupleActivityRequest->type === 'own_partner'
            ? "{$user->name} is not interested in being your partner at this time."
            : "{$user->name} is not interested in couple activities at this time.";

        Notification::create([
            'user_id' => $coupleActivityRequest->requester_id,
            'title' => $notificationTitle,
            'body' => $notificationBody,
            'type' => 'couple_activity',
            'data' => [
                'request_id' => $coupleActivityRequest->id,
                'partner_id' => $user->id,
                'partner_name' => $user->name,
                'status' => 'rejected',
            ],
        ]);

        $successMessage = $coupleActivityRequest->type === 'own_partner'
            ? 'Partner request declined.'
            : 'Couple activity request declined.';

        return redirect()->back()->with('success', $successMessage);
    }

    /**
     * Block a partner request.
     */
    public function blockRequest(Request $requestData, CoupleActivityRequest $coupleActivityRequest): RedirectResponse
    {
        $user = Auth::user();

        // Check if the request belongs to the current user
        if ($coupleActivityRequest->partner_id !== $user->id) {
            return redirect()->back()->with('error', 'You are not authorized to block this request.');
        }

        // Check if request is still pending
        if (!$coupleActivityRequest->isPending()) {
            return redirect()->back()->with('error', 'This request has already been responded to.');
        }

        // Block the request
        $coupleActivityRequest->block('User blocked');

        // Send notification to the requester
        Notification::create([
            'user_id' => $coupleActivityRequest->requester_id,
            'title' => 'Partner Request Blocked',
            'body' => "{$user->name} has blocked your partner request.",
            'type' => 'couple_activity',
            'data' => [
                'request_id' => $coupleActivityRequest->id,
                'partner_id' => $user->id,
                'partner_name' => $user->name,
                'status' => 'blocked',
            ],
        ]);

        return redirect()->back()->with('success', 'User blocked successfully.');
    }

    /**
     * Unblock a user.
     */
    public function unblockUser(Request $request): RedirectResponse
    {
        $user = Auth::user();
        $blockedUserId = $request->blocked_user_id;

        // Find the blocked request
        $blockedRequest = $user->receivedCoupleActivityRequests()
            ->where('requester_id', $blockedUserId)
            ->where('type', 'own_partner')
            ->where('status', 'blocked')
            ->first();

        if (!$blockedRequest) {
            return redirect()->back()->with('error', 'No blocked request found for this user.');
        }

        // Delete the blocked request to allow future requests
        $blockedRequest->delete();

        // Send notification to the unblocked user
        Notification::create([
            'user_id' => $blockedUserId,
            'title' => 'You have been unblocked',
            'body' => "{$user->name} has unblocked you. You can now send partner requests again.",
            'type' => 'couple_activity',
            'data' => [
                'unblocked_by_id' => $user->id,
                'unblocked_by_name' => $user->name,
                'status' => 'unblocked',
            ],
        ]);

        return redirect()->back()->with('success', 'User unblocked successfully.');
    }

    /**
     * Cancel a pending partner request.
     */
    public function cancelRequest(CoupleActivityRequest $coupleActivityRequest): RedirectResponse
    {
        $user = Auth::user();

        // Check if the request belongs to the current user
        if ($coupleActivityRequest->requester_id !== $user->id) {
            return redirect()->back()->with('error', 'You are not authorized to cancel this request.');
        }

        // Check if request is still pending
        if (!$coupleActivityRequest->isPending()) {
            return redirect()->back()->with('error', 'This request has already been responded to.');
        }

        // Delete the request
        $coupleActivityRequest->delete();

        return redirect()->back()->with('success', 'Partner request cancelled successfully.');
    }

    /**
     * End current partnership.
     */
    public function endPartnership(): RedirectResponse
    {
        $user = Auth::user();
        $currentPartner = $user->getOwnPartner();

        if (!$currentPartner) {
            return redirect()->back()->with('error', 'You do not have a partner.');
        }

        // Find the approved request and delete it
        $approvedRequest = $user->sentCoupleActivityRequests()
            ->where('partner_id', $currentPartner->id)
            ->where('type', 'own_partner')
            ->where('status', 'approved')
            ->first();

        if (!$approvedRequest) {
            $approvedRequest = $user->receivedCoupleActivityRequests()
                ->where('requester_id', $currentPartner->id)
                ->where('type', 'own_partner')
                ->where('status', 'approved')
                ->first();
        }

        if ($approvedRequest) {
            $approvedRequest->delete();
        }

        // Update both users' status back to available
        $user->update(['couple_activity_status' => 'available']);
        $currentPartner->update(['couple_activity_status' => 'available']);

        // Send notification to the partner
        Notification::create([
            'user_id' => $currentPartner->id,
            'title' => 'Partnership Ended',
            'body' => "{$user->name} has ended the partnership.",
            'type' => 'couple_activity',
            'data' => [
                'partner_id' => $user->id,
                'partner_name' => $user->name,
                'status' => 'ended',
            ],
        ]);

        return redirect()->back()->with('success', 'Partnership ended successfully.');
    }

    /**
     * Auto-decline all other pending partner requests when one is accepted.
     */
    private function autoDeclineOtherPartnerRequests($user, $requester, $acceptedRequestId)
    {
        // Get all pending partner requests for the user (received)
        $userPendingRequests = CoupleActivityRequest::where('partner_id', $user->id)
            ->where('type', 'own_partner')
            ->where('status', 'pending')
            ->where('id', '!=', $acceptedRequestId)
            ->get();

        // Get all pending partner requests for the requester (received)
        $requesterPendingRequests = CoupleActivityRequest::where('partner_id', $requester->id)
            ->where('type', 'own_partner')
            ->where('status', 'pending')
            ->get();

        // Combine all requests to decline
        $allRequestsToDecline = $userPendingRequests->merge($requesterPendingRequests);

        foreach ($allRequestsToDecline as $request) {
            // Decline the request
            $request->update([
                'status' => 'rejected',
                'rejection_reason' => 'User has accepted another partner request',
                'responded_at' => now(),
            ]);

            // Send notification to the requester
            Notification::create([
                'user_id' => $request->requester_id,
                'title' => 'Partner Request Auto-Declined',
                'body' => 'Your partner request has been automatically declined because the user has accepted another partner.',
                'type' => 'couple_activity',
                'data' => [
                    'request_id' => $request->id,
                    'partner_id' => $request->partner_id,
                    'status' => 'auto_rejected',
                ],
            ]);
        }
    }
}
