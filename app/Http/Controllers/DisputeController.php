<?php

namespace App\Http\Controllers;

use App\Models\TimeSpendingBooking;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class DisputeController extends Controller
{
    /**
     * Raise a dispute for a booking.
     */
    public function raiseDispute(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_id' => 'required|exists:time_spending_bookings,id',
                'dispute_type' => 'required|in:no_show,service_issue,payment_issue,other',
                'reason' => 'required|string|min:10|max:1000',
                'evidence_photos' => 'nullable|array|max:5',
                'evidence_photos.*' => 'image|mimes:jpeg,png,jpg|max:5120', // 5MB max per image
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid input data.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($request->booking_id);

            // Check if user is the client of this booking
            if ($booking->client_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to dispute this booking.'
                ], 403);
            }

            // Check if booking is eligible for dispute
            if ($booking->escrow_status === 'disputed') {
                return response()->json([
                    'success' => false,
                    'message' => 'A dispute has already been raised for this booking.'
                ], 400);
            }

            if ($booking->provider_status !== 'accepted' || $booking->payment_status !== 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking is not eligible for dispute.'
                ], 400);
            }

            // For no-show disputes, check if enough time has passed
            if ($request->dispute_type === 'no_show' && !$booking->canRaiseNoShowDispute()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only raise a no-show dispute 30 minutes after the meeting end time.'
                ], 400);
            }

            // Handle evidence photos
            $evidenceData = [];
            if ($request->hasFile('evidence_photos')) {
                foreach ($request->file('evidence_photos') as $index => $photo) {
                    $filename = 'dispute_evidence_' . $booking->id . '_' . time() . '_' . $index . '.' . $photo->getClientOriginalExtension();
                    $path = $photo->storeAs('dispute_evidence', $filename, 'public');
                    $evidenceData[] = [
                        'type' => 'photo',
                        'path' => $path,
                        'uploaded_at' => now()->toISOString()
                    ];
                }
            }

            // Raise the dispute
            $booking->raiseDispute(
                $user->id,
                $request->reason,
                $request->dispute_type,
                $evidenceData
            );

            // Create notifications using helper methods
            Notification::createDisputeRaised($booking, $booking->dispute_type_display);
            Notification::createDisputeSubmitted($booking, $booking->dispute_type_display);

            return response()->json([
                'success' => true,
                'message' => 'Dispute raised successfully. Our team will review your case and get back to you within 24-48 hours.',
                'dispute_id' => $booking->id
            ]);

        } catch (\Exception $e) {
            \Log::error('Error raising dispute:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'booking_id' => $request->booking_id ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while raising the dispute. Please try again.'
            ], 500);
        }
    }

    /**
     * Get dispute details for a booking.
     */
    public function getDisputeDetails($bookingId): JsonResponse
    {
        try {
            $user = Auth::user();
            $booking = TimeSpendingBooking::with(['client', 'provider', 'disputedBy', 'resolvedBy'])
                ->findOrFail($bookingId);

            // Check if user is involved in this booking
            if ($booking->client_id !== $user->id && $booking->provider_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to view this dispute.'
                ], 403);
            }

            if ($booking->escrow_status !== 'disputed') {
                return response()->json([
                    'success' => false,
                    'message' => 'No dispute found for this booking.'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'dispute' => [
                    'booking_id' => $booking->id,
                    'dispute_type' => $booking->dispute_type,
                    'dispute_type_display' => $booking->dispute_type_display,
                    'dispute_status' => $booking->dispute_status,
                    'dispute_status_display' => $booking->dispute_status_display,
                    'dispute_reason' => $booking->dispute_reason,
                    'disputed_at' => $booking->disputed_at,
                    'disputed_by' => $booking->disputedBy ? $booking->disputedBy->name : null,
                    'evidence' => $booking->dispute_evidence,
                    'admin_notes' => $booking->admin_notes,
                    'resolved_at' => $booking->resolved_at,
                    'resolved_by' => $booking->resolvedBy ? $booking->resolvedBy->name : null,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting dispute details:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'booking_id' => $bookingId
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching dispute details.'
            ], 500);
        }
    }

    /**
     * Get user's disputes.
     */
    public function getUserDisputes(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $disputes = TimeSpendingBooking::with(['client', 'provider'])
                ->where(function($query) use ($user) {
                    $query->where('client_id', $user->id)
                          ->orWhere('provider_id', $user->id);
                })
                ->where('escrow_status', 'disputed')
                ->orderBy('disputed_at', 'desc')
                ->get()
                ->map(function($booking) use ($user) {
                    return [
                        'booking_id' => $booking->id,
                        'dispute_type' => $booking->dispute_type,
                        'dispute_type_display' => $booking->dispute_type_display,
                        'dispute_status' => $booking->dispute_status,
                        'dispute_status_display' => $booking->dispute_status_display,
                        'dispute_reason' => $booking->dispute_reason,
                        'disputed_at' => $booking->disputed_at,
                        'booking_date' => $booking->booking_date,
                        'total_amount' => $booking->total_amount,
                        'other_party' => $booking->client_id === $user->id ? $booking->provider->name : $booking->client->name,
                        'is_my_dispute' => $booking->disputed_by === $user->id,
                    ];
                });

            return response()->json([
                'success' => true,
                'disputes' => $disputes
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting user disputes:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching disputes.'
            ], 500);
        }
    }

    /**
     * Show dispute history page.
     */
    public function disputeHistory()
    {
        $user = auth()->user();

        $disputes = TimeSpendingBooking::with(['client', 'provider', 'disputedBy', 'resolvedBy'])
            ->where(function($query) use ($user) {
                $query->where('client_id', $user->id)
                      ->orWhere('provider_id', $user->id);
            })
            ->where('escrow_status', 'disputed')
            ->orderBy('disputed_at', 'desc')
            ->paginate(10);

        return view('disputes.history', compact('disputes'));
    }
}
