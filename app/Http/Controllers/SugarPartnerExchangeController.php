<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use App\Models\SugarPartnerExchange;
use App\Models\SugarPartnerExchangePayment;
use App\Models\SugarPartnerRejection;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SugarPartnerExchangeController extends Controller
{
    /**
     * Show exchange payment page for a user.
     */
    public function showPayment(SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('info', 'You have already paid for this exchange.');
        }

        // Check if exchange is in correct status
        if ($exchange->status !== 'pending_payment') {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('info', 'This exchange is no longer accepting payments.');
        }

        $otherUser = $exchange->getOtherUser($user->id);
        $razorpayKey = Setting::get('razorpay_key_id');

        return view('sugar-partner.exchange.payment', compact(
            'exchange',
            'user',
            'otherUser',
            'razorpayKey'
        ));
    }

    /**
     * Process payment for exchange.
     */
    public function processPayment(Request $request, SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to pay for this exchange.'
                ], 403);
            }
            abort(403, 'You are not authorized to pay for this exchange.');
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already paid for this exchange.'
                ], 400);
            }
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You have already paid for this exchange.');
        }

        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:razorpay,wallet',
            'amount' => 'sometimes|numeric|min:0',
            'is_partial' => 'sometimes|boolean',
            'wallet_amount_used' => 'sometimes|numeric|min:0',
            'razorpay_payment_id' => 'sometimes|string', // Made optional for demo
            'razorpay_order_id' => 'sometimes|string',   // Made optional for demo
            'razorpay_signature' => 'sometimes|string',  // Made optional for demo
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            $paymentAmount = $request->input('amount');
            $isPartial = $request->input('is_partial', false);
            $walletAmountUsed = $request->input('wallet_amount_used', 0);

            \Log::info('Processing payment', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'payment_method' => $request->payment_method,
                'amount' => $paymentAmount,
                'is_partial' => $isPartial,
                'wallet_amount_used' => $walletAmountUsed,
                'expects_json' => $request->expectsJson()
            ]);

            if ($request->payment_method === 'wallet') {
                $this->processWalletPayment($exchange, $user, $paymentAmount, $isPartial);
            } else {
                $this->processRazorpayPayment($exchange, $user, $request, $paymentAmount, $walletAmountUsed);
            }

            DB::commit();

            \Log::info('Payment completed successfully', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'payment_method' => $request->payment_method
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully!'
                ]);
            }

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('success', 'Payment completed successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Payment processing failed', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'payment_method' => $request->payment_method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment failed: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Payment failed: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show exchange status page.
     */
    public function showStatus(SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        $otherUser = $exchange->getOtherUser($user->id);
        $userHasPaid = $exchange->userHasPaid($user->id);
        $otherUserHasPaid = $exchange->userHasPaid($otherUser->id);
        $bothPaid = $exchange->bothUsersPaid();

        // Load user's payment if exists
        $userPayment = $exchange->payments()
            ->where('user_id', $user->id)
            ->first();

        // Load user's response if exists
        $userResponse = $exchange->getUserResponse($user->id);

        return view('sugar-partner.exchange.status', compact(
            'exchange',
            'user',
            'otherUser',
            'userHasPaid',
            'otherUserHasPaid',
            'bothPaid',
            'userPayment',
            'userResponse'
        ));
    }

    /**
     * Show other user's profile after payment.
     */
    public function showProfile(Request $request, SugarPartnerExchange $exchange, User $profileUser)
    {
        $user = Auth::user();



        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {


            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to view this exchange.'
                ], 403);
            }
            abort(403, 'You are not authorized to view this exchange.');
        }

        // Verify the profile user is the other user in the exchange
        $otherUser = $exchange->getOtherUser($user->id);


        if ($profileUser->id !== $otherUser->id) {


            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid profile access.'
                ], 403);
            }
            abort(403, 'Invalid profile access.');
        }

        // Verify both users have paid
        $bothPaid = $exchange->bothUsersPaid();


        if (!$bothPaid) {


            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Both users must complete payment before viewing profiles.'
                ], 403);
            }
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'Both users must complete payment before viewing profiles.');
        }

        // Mark profiles as viewed if this is the first time
        if ($exchange->status === 'payment_completed') {
            $exchange->markProfilesViewed();
        }

        // Load profile user with all necessary data (bypass privacy settings)
        $profileUser->load([
            'galleryImages' => function ($query) {
                $query->active()->ordered();
            }
        ]);

        if ($request->expectsJson()) {
            // Return profile data for AJAX requests
            return response()->json([
                'success' => true,
                'profile' => [
                    'name' => $profileUser->name,
                    'age' => $profileUser->getAge(),
                    'gender' => $profileUser->gender,
                    'interests' => $profileUser->interests,
                    'expectation' => $profileUser->expectation,
                    'profile_picture' => $profileUser->profile_picture ? asset('storage/' . $profileUser->profile_picture) : asset('images/default-avatar.png'),
                    'gallery_images' => $profileUser->galleryImages->map(function($image) {
                        return asset('storage/' . $image->image_path);
                    }),
                    'sugar_partner_bio' => $profileUser->sugar_partner_bio,
                    'sugar_partner_expectations' => $profileUser->sugar_partner_expectations,
                ]
            ]);
        }

        return view('sugar-partner.exchange.profile', compact(
            'exchange',
            'user',
            'profileUser'
        ));
    }

    /**
     * Submit response to exchange.
     */
    public function submitResponse(Request $request, SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to respond to this exchange.');
        }

        // Verify both users have paid
        if (!$exchange->bothUsersPaid()) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'Both users must complete payment before responding.');
        }

        // Check if user has already responded
        if ($exchange->getUserResponse($user->id)) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You have already responded to this exchange.');
        }

        $validator = Validator::make($request->all(), [
            'rejection_type' => 'required|in:accept,soft_reject,hard_reject',
            'rejection_reason' => 'nullable|string|max:1000',
            'admin_note' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            SugarPartnerRejection::createRejection(
                $exchange,
                $user->id,
                $request->rejection_type,
                $request->rejection_reason,
                $request->admin_note
            );

            DB::commit();

            $responseType = match($request->rejection_type) {
                'accept' => 'acceptance',
                'soft_reject' => 'soft rejection',
                'hard_reject' => 'hard rejection',
            };

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Your {$responseType} has been recorded and the other user has been notified."
                ]);
            }

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('success', "Your {$responseType} has been recorded and the other user has been notified.");

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to submit response: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to submit response: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Process wallet payment.
     */
    protected function processWalletPayment(SugarPartnerExchange $exchange, User $user, $paymentAmount = null, $isPartial = false): void
    {
        $wallet = $user->getWallet();
        $userPrice = $exchange->getPriceForUser($user->id);
        $amountToDeduct = $paymentAmount ?? $userPrice;

        // For partial payments, we allow any amount up to wallet balance
        // For full payments, we need the full amount
        if (!$isPartial && $wallet->balance < $userPrice) {
            throw new \Exception('Insufficient wallet balance.');
        }

        if ($isPartial && $wallet->balance < $amountToDeduct) {
            throw new \Exception('Insufficient wallet balance for partial payment.');
        }

        if ($amountToDeduct > $userPrice) {
            throw new \Exception('Payment amount cannot exceed required amount.');
        }

        // Deduct from wallet
        $wallet->deductMoney(
            $amountToDeduct,
            "Sugar Partner Exchange Payment - Exchange #{$exchange->id}" . ($isPartial ? ' (Partial)' : '')
        );

        // Create payment record
        $payment = SugarPartnerExchangePayment::createForExchange($exchange, $user->id, [
            'payment_method' => $isPartial ? 'wallet_partial' : 'wallet',
            'amount' => $amountToDeduct,
            'payment_details' => [
                'wallet_balance_before' => $wallet->balance + $amountToDeduct,
                'wallet_balance_after' => $wallet->balance,
                'is_partial' => $isPartial,
                'amount_paid' => $amountToDeduct,
                'total_required' => $userPrice,
            ]
        ]);

        // Only mark as completed if this is a full payment
        if (!$isPartial) {
            $payment->markAsCompleted([
                'payment_source' => 'wallet',
                'transaction_id' => 'wallet_' . time() . '_' . $user->id,
            ]);
        } else {
            // For partial payments, just mark the payment as processed but not completed
            $payment->update([
                'status' => 'partial_completed',
                'payment_details' => array_merge($payment->payment_details ?? [], [
                    'partial_payment_id' => 'wallet_partial_' . time() . '_' . $user->id,
                    'processed_at' => now(),
                ])
            ]);
        }
    }

    /**
     * Process Razorpay payment.
     */
    protected function processRazorpayPayment(SugarPartnerExchange $exchange, User $user, Request $request, $paymentAmount = null, $walletAmountUsed = 0): void
    {
        try {
            $userPrice = $exchange->getPriceForUser($user->id);
            $razorpayAmount = $paymentAmount ?? $userPrice;
            $isHybridPayment = $walletAmountUsed > 0;

            // For automatic payment processing, we'll simulate successful payment
            // In a real implementation, you would integrate with Razorpay API here

            $paymentMethod = $isHybridPayment ? 'hybrid' : 'razorpay';

            $paymentId = $request->input('razorpay_payment_id', 'auto_' . time() . '_' . $user->id);

            // Create payment record with proper amount handling
            $payment = new SugarPartnerExchangePayment([
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'amount' => $razorpayAmount, // Use the actual payment amount, not the user's default price
                'currency' => $exchange->currency,
                'payment_method' => $paymentMethod,
                'payment_id' => $paymentId,
                'order_id' => $request->input('razorpay_order_id'),
                'signature' => $request->input('razorpay_signature'),
                'payment_details' => [
                    'razorpay_payment_id' => $request->input('razorpay_payment_id'),
                    'razorpay_order_id' => $request->input('razorpay_order_id'),
                    'razorpay_signature' => $request->input('razorpay_signature'),
                    'razorpay_amount' => $razorpayAmount,
                    'wallet_amount_used' => $walletAmountUsed,
                    'total_amount' => $razorpayAmount + $walletAmountUsed,
                    'is_hybrid' => $isHybridPayment,
                    'auto_processed' => true,
                    'processed_at' => now(),
                ]
            ]);

            $payment->save();

            $payment->markAsCompleted([
                'payment_source' => $isHybridPayment ? 'hybrid_auto' : 'razorpay_auto',
                'verified' => true,
                'auto_payment' => true,
            ]);

        } catch (\Exception $e) {
            \Log::error('Razorpay payment processing failed', [
                'exchange_id' => $exchange->id,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \Exception('Payment processing failed: ' . $e->getMessage());
        }
    }
}
