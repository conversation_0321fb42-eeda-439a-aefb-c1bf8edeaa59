<?php

namespace App\Http\Controllers;

use App\Models\SugarPartnerHardReject;
use App\Models\SugarPartnerRejection;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SugarPartnerHardRejectController extends Controller
{
    /**
     * Display user's hard rejected users.
     */
    public function index()
    {
        $user = Auth::user();
        $blockedUsers = SugarPartnerHardReject::getBlockedUsersFor($user->id);

        return view('sugar-partner.hard-rejects.index', compact('blockedUsers'));
    }

    /**
     * Convert hard reject to soft reject.
     */
    public function convertToSoftReject(Request $request)
    {
        $request->validate([
            'user1_id' => 'required|exists:users,id',
            'user2_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $user1Id = (int) $request->user1_id;
        $user2Id = (int) $request->user2_id;

        // Verify user is involved in this hard reject
        if ($user->id !== $user1Id && $user->id !== $user2Id) {
            return redirect()->back()->with('error', 'You are not authorized to modify this rejection.');
        }

        DB::beginTransaction();
        try {
            // Get the hard reject record
            $hardReject = SugarPartnerHardReject::getBetweenUsers($user1Id, $user2Id);
            
            if (!$hardReject) {
                return redirect()->back()->with('error', 'Hard reject record not found.');
            }

            // Create a new soft reject record to replace the hard reject
            $originalExchange = $hardReject->originalExchange;
            
            if ($originalExchange) {
                SugarPartnerRejection::create([
                    'exchange_id' => $originalExchange->id,
                    'rejector_id' => $hardReject->rejector_id,
                    'rejected_user_id' => $hardReject->rejector_id === $user1Id ? $user2Id : $user1Id,
                    'rejection_type' => 'soft_reject',
                    'rejection_reason' => $request->reason ?: 'Converted from hard reject',
                    'admin_note' => "Converted from hard reject by user on " . now()->format('Y-m-d H:i:s'),
                    'notification_sent' => true, // Skip notification since this is a conversion
                    'notification_sent_at' => now(),
                ]);
            }

            // Remove the hard reject record
            $hardReject->delete();

            // Send notification to the other user
            $otherUserId = $user->id === $user1Id ? $user2Id : $user1Id;
            $otherUser = User::find($otherUserId);

            if ($otherUser) {
                \App\Models\Notification::create([
                    'user_id' => $otherUserId,
                    'type' => 'sugar_partner_hard_reject_converted',
                    'title' => 'Hard Reject Converted to Soft Reject',
                    'message' => "{$user->name} has converted their hard reject to a soft reject. Future profile exchanges may be possible.",
                    'body' => "{$user->name} has converted their hard reject to a soft reject. This means future profile exchanges between you two may be possible again.",
                    'data' => [
                        'converter_id' => $user->id,
                        'converter_name' => $user->name,
                        'conversion_reason' => $request->reason,
                    ],
                ]);
            }

            DB::commit();

            return redirect()->back()->with('success', 'Hard reject has been converted to soft reject. Future exchanges may be possible.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to convert hard reject: ' . $e->getMessage());
        }
    }

    /**
     * Remove rejection completely (neutral state).
     */
    public function removeCompletely(Request $request)
    {
        $request->validate([
            'user1_id' => 'required|exists:users,id',
            'user2_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $user1Id = (int) $request->user1_id;
        $user2Id = (int) $request->user2_id;

        // Verify user is involved in this hard reject
        if ($user->id !== $user1Id && $user->id !== $user2Id) {
            return redirect()->back()->with('error', 'You are not authorized to modify this rejection.');
        }

        DB::beginTransaction();
        try {
            // Get the hard reject record
            $hardReject = SugarPartnerHardReject::getBetweenUsers($user1Id, $user2Id);
            
            if (!$hardReject) {
                return redirect()->back()->with('error', 'Hard reject record not found.');
            }

            // Remove all related rejection records
            $originalExchange = $hardReject->originalExchange;
            
            if ($originalExchange) {
                // Remove soft reject records related to this exchange
                SugarPartnerRejection::where('exchange_id', $originalExchange->id)
                    ->where(function ($query) use ($user1Id, $user2Id) {
                        $query->where(function ($q) use ($user1Id, $user2Id) {
                            $q->where('rejector_id', $user1Id)->where('rejected_user_id', $user2Id);
                        })->orWhere(function ($q) use ($user1Id, $user2Id) {
                            $q->where('rejector_id', $user2Id)->where('rejected_user_id', $user1Id);
                        });
                    })
                    ->delete();
            }

            // Remove the hard reject record
            $hardReject->delete();

            // Send notification to the other user
            $otherUserId = $user->id === $user1Id ? $user2Id : $user1Id;
            $otherUser = User::find($otherUserId);

            if ($otherUser) {
                \App\Models\Notification::create([
                    'user_id' => $otherUserId,
                    'type' => 'sugar_partner_rejection_cleared',
                    'title' => 'Rejection History Cleared',
                    'message' => "{$user->name} has cleared all rejection history between you. You now have a neutral relationship status.",
                    'body' => "{$user->name} has cleared all rejection history between you two. This means you now have a neutral relationship status with no previous rejections on record.",
                    'data' => [
                        'clearer_id' => $user->id,
                        'clearer_name' => $user->name,
                        'clear_reason' => $request->reason,
                    ],
                ]);
            }

            DB::commit();

            return redirect()->back()->with('success', 'All rejection history has been cleared. You now have a neutral relationship status.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to clear rejection history: ' . $e->getMessage());
        }
    }

    /**
     * Get hard reject details for AJAX.
     */
    public function getDetails(Request $request)
    {
        $request->validate([
            'user1_id' => 'required|exists:users,id',
            'user2_id' => 'required|exists:users,id',
        ]);

        $user = Auth::user();
        $user1Id = (int) $request->user1_id;
        $user2Id = (int) $request->user2_id;

        // Verify user is involved in this hard reject
        if ($user->id !== $user1Id && $user->id !== $user2Id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $hardReject = SugarPartnerHardReject::getBetweenUsers($user1Id, $user2Id);
        
        if (!$hardReject) {
            return response()->json(['error' => 'Hard reject not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $hardReject->getFormattedDetails()
        ]);
    }
}
