<?php

namespace App\Http\Controllers;

use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Store a new contact form submission.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'privacy' => 'required|accepted',
        ], [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'subject.required' => 'Please select a subject.',
            'message.required' => 'Message is required.',
            'message.max' => 'Message cannot exceed 2000 characters.',
            'privacy.required' => 'You must agree to the privacy policy.',
            'privacy.accepted' => 'You must agree to the privacy policy.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create the contact submission
            $submission = ContactSubmission::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'subject' => $request->subject,
                'message' => $request->message,
                'status' => 'pending',
                'priority' => $this->determinePriority($request->subject),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for contacting us! We have received your message and will get back to you soon.',
                'submission_id' => $submission->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sorry, there was an error submitting your message. Please try again later.'
            ], 500);
        }
    }

    /**
     * Determine priority based on subject.
     */
    private function determinePriority($subject): string
    {
        return match($subject) {
            'safety' => 'urgent',
            'payment', 'account' => 'high',
            'technical', 'events' => 'medium',
            'feedback', 'other' => 'low',
            default => 'medium'
        };
    }
}
