<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash; // Or Str::random() for password
use Laravel\Socialite\Facades\Socialite;

class GoogleLoginController extends Controller
{
    public function redirectToGoogle()
    {
        try {
            \Log::info('Google OAuth redirect initiated');

            return Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes(['email', 'profile'])
                ->redirect();
        } catch (\Exception $e) {
            \Log::error('Google OAuth redirect failed: ' . $e->getMessage());
            return redirect('/login')->with('error', 'Unable to connect to Google. Please try again.');
        }
    }

    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');

            $googleUser = Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->user();

            \Log::info('Google user data received: ' . $googleUser->getEmail());

            // Find user by google_id or create new one
            $user = User::updateOrCreate([
                'google_id' => $googleUser->getId(),
            ], [
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'password' => Hash::make(uniqid()) // Password is required but not used
            ]);

            Auth::login($user, true); // Log the user in

            \Log::info('User logged in successfully: ' . $user->email);

            // Redirect to home page - the root route will handle profile completion check
            return redirect('/')->with('success', 'Welcome back! You have successfully logged in.');

        } catch (\Exception $e) {
            \Log::error('Google OAuth callback failed: ' . $e->getMessage());
            return redirect('/login')->with('error', 'Login with Google failed. Please try again.');
        }
    }
}
