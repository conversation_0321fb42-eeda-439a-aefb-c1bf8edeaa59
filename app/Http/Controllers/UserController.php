<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\TimeSpendingBooking;

class UserController extends Controller
{
    /**
     * Get user availability for a specific date.
     */
    public function getAvailability(Request $request, User $user)
    {
        $request->validate([
            'date' => 'required|date'
        ]);

        $date = $request->input('date');
        $dayOfWeek = strtolower(date('l', strtotime($date))); // Get day name (monday, tuesday, etc.)

        // Get user's availability schedule
        $availabilitySchedule = $user->availability_schedule;

        if (!$availabilitySchedule || !isset($availabilitySchedule[$dayOfWeek])) {
            return response()->json([
                'success' => false,
                'message' => 'No availability schedule found for this day.'
            ]);
        }

        $daySchedule = $availabilitySchedule[$dayOfWeek];

        // Check if it's a holiday
        if (isset($daySchedule['is_holiday']) && $daySchedule['is_holiday']) {
            return response()->json([
                'success' => true,
                'is_holiday' => true,
                'message' => 'User is not available on this date (Holiday).'
            ]);
        }

        // Check if start and end times are available
        if (!isset($daySchedule['start_time']) || !isset($daySchedule['end_time'])) {
            return response()->json([
                'success' => false,
                'message' => 'No time schedule found for this day.'
            ]);
        }

        // Get available time slots considering existing bookings
        $availableSlots = TimeSpendingBooking::getAvailableTimeSlots(
            $user->id,
            $date,
            $daySchedule['start_time'],
            $daySchedule['end_time']
        );

        // Get booked slots for additional information
        $bookedSlots = TimeSpendingBooking::getBookedSlotsForDate($user->id, $date);

        return response()->json([
            'success' => true,
            'is_holiday' => false,
            'available_hours' => [
                'start_time' => $daySchedule['start_time'],
                'end_time' => $daySchedule['end_time']
            ],
            'available_slots' => $availableSlots,
            'booked_slots' => $bookedSlots,
            'day_of_week' => $dayOfWeek,
            'date' => $date
        ]);
    }
}
