<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Display the contact submissions index page.
     */
    public function index()
    {
        $stats = [
            'total' => ContactSubmission::count(),
            'pending' => ContactSubmission::where('status', 'pending')->count(),
            'in_progress' => ContactSubmission::where('status', 'in_progress')->count(),
            'solved' => ContactSubmission::where('status', 'solved')->count(),
            'follow_up' => ContactSubmission::where('status', 'follow_up')->count(),
            'closed' => ContactSubmission::where('status', 'closed')->count(),
        ];

        $adminUsers = User::where(function($q) {
            $q->whereIn('role', ['admin', 'super_admin', 'editor'])
              ->orWhere('is_admin', true);
        })->select('id', 'name', 'email')->get();

        return view('admin.contact.index', compact('stats', 'adminUsers'));
    }

    /**
     * Get contact submissions data for DataTables.
     */
    public function getData(Request $request)
    {
        $query = ContactSubmission::with('assignedAdmin')
            ->byStatus($request->status)
            ->byPriority($request->priority)
            ->search($request->search);

        // Apply sorting
        $sortColumn = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        $allowedSortColumns = ['id', 'first_name', 'last_name', 'email', 'subject', 'status', 'priority', 'created_at'];
        if (in_array($sortColumn, $allowedSortColumns)) {
            $query->orderBy($sortColumn, $sortDirection);
        }

        $submissions = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => $submissions->items(),
            'pagination' => [
                'current_page' => $submissions->currentPage(),
                'last_page' => $submissions->lastPage(),
                'per_page' => $submissions->perPage(),
                'total' => $submissions->total(),
                'from' => $submissions->firstItem(),
                'to' => $submissions->lastItem(),
            ]
        ]);
    }

    /**
     * Get a specific contact submission.
     */
    public function show($id)
    {
        try {
            $submission = ContactSubmission::with('assignedAdmin')->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'submission' => $submission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Contact submission not found.'
            ], 404);
        }
    }

    /**
     * Update contact submission status and details.
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:pending,in_progress,solved,follow_up,closed',
                'priority' => 'required|in:low,medium,high,urgent',
                'admin_notes' => 'nullable|string|max:2000',
                'assigned_to' => 'nullable|exists:users,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $submission = ContactSubmission::findOrFail($id);

            $updateData = [
                'status' => $request->status,
                'priority' => $request->priority,
                'admin_notes' => $request->admin_notes,
                'assigned_to' => $request->assigned_to ?: null,
            ];

            // Set responded_at if status is changed from pending
            if ($submission->status === 'pending' && $request->status !== 'pending') {
                $updateData['responded_at'] = now();
            }

            $submission->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Contact submission updated successfully.',
                'submission' => $submission->load('assignedAdmin')
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Contact submission not found.'
            ], 404);
        } catch (\Exception $e) {
            \Log::error('Contact update error: ' . $e->getMessage(), [
                'id' => $id,
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error updating contact submission: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a contact submission.
     */
    public function destroy($id)
    {
        try {
            $submission = ContactSubmission::findOrFail($id);
            $submission->delete();

            return response()->json([
                'success' => true,
                'message' => 'Contact submission deleted successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting contact submission.'
            ], 500);
        }
    }

    /**
     * Get dashboard statistics for contact submissions.
     */
    public function getStats()
    {
        $stats = [
            'total' => ContactSubmission::count(),
            'pending' => ContactSubmission::where('status', 'pending')->count(),
            'in_progress' => ContactSubmission::where('status', 'in_progress')->count(),
            'solved' => ContactSubmission::where('status', 'solved')->count(),
            'follow_up' => ContactSubmission::where('status', 'follow_up')->count(),
            'closed' => ContactSubmission::where('status', 'closed')->count(),
            'urgent' => ContactSubmission::where('priority', 'urgent')->count(),
            'high' => ContactSubmission::where('priority', 'high')->count(),
        ];

        return response()->json(['stats' => $stats]);
    }
}
