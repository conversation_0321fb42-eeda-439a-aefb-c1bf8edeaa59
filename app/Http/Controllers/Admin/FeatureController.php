<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Feature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FeatureController extends Controller
{
    /**
     * Display a listing of the features.
     */
    public function index()
    {
        $features = Feature::where('is_hidden', false)->orderBy('name')->get();
        return view('admin.features.index', compact('features'));
    }

    /**
     * Update feature status.
     */
    public function updateStatus(Request $request, Feature $feature)
    {
        $request->validate([
            'is_enabled' => 'required|in:0,1'
        ]);

        $isEnabled = $request->is_enabled == '1';

        $feature->update([
            'is_enabled' => $isEnabled
        ]);

        // Auto-enable related features when Time Spending is enabled
        if ($feature->name === 'time_spending' && $isEnabled) {
            $relatedFeatures = ['gallery', 'chat_system', 'meeting_verification', 'notifications', 'rating_review_system'];

            foreach ($relatedFeatures as $featureName) {
                Feature::where('name', $featureName)->update(['is_enabled' => true]);
            }
        }

        $status = $isEnabled ? 'enabled' : 'disabled';
        $message = "Feature '{$feature->label}' has been {$status} successfully.";

        // Add message about auto-enabled features
        if ($feature->name === 'time_spending' && $isEnabled) {
            $message .= " Related features (Gallery, Chat System, Meeting Verification, Notifications, Rating & Review System) have been auto-enabled.";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Update Sugar Partner feature pricing.
     */
    public function updateSugarPartnerPricing(Request $request, Feature $feature)
    {
        if ($feature->name !== 'sugar_partner') {
            return redirect()->back()->with('error', 'Invalid feature for pricing update.');
        }

        $request->validate([
            'sugar_daddy_price' => 'required|numeric|min:0|max:99999.99',
            'sugar_mommy_price' => 'required|numeric|min:0|max:99999.99',
            'sugar_companion_female_price' => 'required|numeric|min:0|max:99999.99',
            'sugar_companion_male_price' => 'required|numeric|min:0|max:99999.99',
            'currency' => 'required|string|size:3',
        ]);

        $options = $feature->options ?? [];
        $options['currency'] = strtoupper($request->currency);
        $options['pricing'] = [
            'sugar_daddy' => (float) $request->sugar_daddy_price,
            'sugar_mommy' => (float) $request->sugar_mommy_price,
            'sugar_companion_female' => (float) $request->sugar_companion_female_price,
            'sugar_companion_male' => (float) $request->sugar_companion_male_price,
        ];

        $feature->update(['options' => $options]);

        return redirect()->back()->with('success', 'Sugar Partner pricing updated successfully.');
    }
}
