<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MeetingAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MeetingEventController extends Controller
{
    /**
     * Display a listing of meeting events.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // If couple events feature is disabled, disable all couple events
        if (!\App\Models\Feature::isEnabled('partner_swapping')) {
            MeetingAddress::where('is_couple_event', true)
                ->where('is_event_enabled', true)
                ->update(['is_event_enabled' => false]);
        }

        $events = MeetingAddress::latest()->paginate(10);

        // Check if this is an AJAX request
        if (request()->ajax()) {
            $html = view('admin.meeting-events.index', compact('events'))->render();
            return $html;
        }

        return view('admin.meeting-events.index', compact('events'));
    }

    /**
     * Show the form for creating a new meeting event.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.meeting-events.create');
    }

    /**
     * Store a newly created meeting event in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Dynamic validation based on couple event checkbox
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'rules_and_regulations' => 'nullable|string',
            'is_event_enabled' => 'boolean',
            'is_couple_event' => 'boolean',
            'event_date' => 'nullable|date',
            'location' => 'nullable|string|max:255',
        ];

        // Add payment validation based on event type
        if ($request->has('is_couple_event') && $request->is_couple_event) {
            $rules['payment_amount_couple'] = 'required|numeric|min:0';
            $rules['payment_amount_boys'] = 'nullable|numeric|min:0';
            $rules['payment_amount_girls'] = 'nullable|numeric|min:0';
        } else {
            $rules['payment_amount_boys'] = 'required|numeric|min:0';
            $rules['payment_amount_girls'] = 'required|numeric|min:0';
            $rules['payment_amount_couple'] = 'nullable|numeric|min:0';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Determine payment amounts based on event type
        $isCoupleEvent = $request->has('is_couple_event');

        if ($isCoupleEvent) {
            // For couple events: save couple amount, set individual amounts to 0
            $paymentAmountBoys = 0;
            $paymentAmountGirls = 0;
            $paymentAmountCouple = $request->payment_amount_couple ?? 0;
        } else {
            // For regular events: save individual amounts, set couple amount to 0
            $paymentAmountBoys = $request->payment_amount_boys ?? 0;
            $paymentAmountGirls = $request->payment_amount_girls ?? 0;
            $paymentAmountCouple = 0;
        }

        MeetingAddress::create([
            'title' => $request->title,
            'description' => $request->description,
            'rules_and_regulations' => $request->rules_and_regulations,
            'payment_amount_boys' => $paymentAmountBoys,
            'payment_amount_girls' => $paymentAmountGirls,
            'payment_amount_couple' => $paymentAmountCouple,
            'is_event_enabled' => $request->has('is_event_enabled'),
            'is_couple_event' => $isCoupleEvent,
            'event_date' => $request->event_date,
            'location' => $request->location,
        ]);

        return redirect()->route('admin.meeting-events.index')
            ->with('success', 'Meeting event created successfully.');
    }

    /**
     * Show the form for editing the specified meeting event.
     *
     * @param  \App\Models\MeetingAddress  $meetingEvent
     * @return \Illuminate\View\View
     */
    public function edit(MeetingAddress $meetingEvent)
    {
        return view('admin.meeting-events.edit', compact('meetingEvent'));
    }

    /**
     * Update the specified meeting event in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\MeetingAddress  $meetingEvent
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, MeetingAddress $meetingEvent)
    {
        // Dynamic validation based on couple event checkbox
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'rules_and_regulations' => 'nullable|string',
            'is_event_enabled' => 'boolean',
            'is_couple_event' => 'boolean',
            'event_date' => 'nullable|date',
            'location' => 'nullable|string|max:255',
        ];

        // Add payment validation based on event type
        if ($request->has('is_couple_event') && $request->is_couple_event) {
            $rules['payment_amount_couple'] = 'required|numeric|min:0';
            $rules['payment_amount_boys'] = 'nullable|numeric|min:0';
            $rules['payment_amount_girls'] = 'nullable|numeric|min:0';
        } else {
            $rules['payment_amount_boys'] = 'required|numeric|min:0';
            $rules['payment_amount_girls'] = 'required|numeric|min:0';
            $rules['payment_amount_couple'] = 'nullable|numeric|min:0';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Determine payment amounts based on event type
        $isCoupleEvent = $request->has('is_couple_event');

        if ($isCoupleEvent) {
            // For couple events: save couple amount, set individual amounts to 0
            $paymentAmountBoys = 0;
            $paymentAmountGirls = 0;
            $paymentAmountCouple = $request->payment_amount_couple ?? 0;
        } else {
            // For regular events: save individual amounts, set couple amount to 0
            $paymentAmountBoys = $request->payment_amount_boys ?? 0;
            $paymentAmountGirls = $request->payment_amount_girls ?? 0;
            $paymentAmountCouple = 0;
        }

        $meetingEvent->update([
            'title' => $request->title,
            'description' => $request->description,
            'rules_and_regulations' => $request->rules_and_regulations,
            'payment_amount_boys' => $paymentAmountBoys,
            'payment_amount_girls' => $paymentAmountGirls,
            'payment_amount_couple' => $paymentAmountCouple,
            'is_event_enabled' => $request->has('is_event_enabled'),
            'is_couple_event' => $isCoupleEvent,
            'event_date' => $request->event_date,
            'location' => $request->location,
        ]);

        return redirect()->route('admin.meeting-events.index')
            ->with('success', 'Meeting event updated successfully.');
    }

    /**
     * Remove the specified meeting event from storage.
     *
     * @param  \App\Models\MeetingAddress  $meetingEvent
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(MeetingAddress $meetingEvent)
    {
        $meetingEvent->delete();

        return redirect()->route('admin.meeting-events.index')
            ->with('success', 'Meeting event deleted successfully.');
    }

    /**
     * Toggle the event status.
     *
     * @param  \App\Models\MeetingAddress  $meetingEvent
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleStatus(MeetingAddress $meetingEvent)
    {
        $meetingEvent->update([
            'is_event_enabled' => !$meetingEvent->is_event_enabled
        ]);

        $status = $meetingEvent->is_event_enabled ? 'enabled' : 'disabled';

        return redirect()->route('admin.meeting-events.index')
            ->with('success', "Meeting event has been {$status}.");
    }

    /**
     * Get events data for AJAX requests.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEventsData(Request $request)
    {
        // If couple events feature is disabled, disable all couple events
        if (!\App\Models\Feature::isEnabled('partner_swapping')) {
            MeetingAddress::where('is_couple_event', true)
                ->where('is_event_enabled', true)
                ->update(['is_event_enabled' => false]);
        }

        $query = MeetingAddress::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'enabled') {
                $query->where('is_event_enabled', true);
            } elseif ($status === 'disabled') {
                $query->where('is_event_enabled', false);
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $allowedSortFields = ['title', 'event_date', 'created_at', 'updated_at'];

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $events = $query->paginate(20);

        $html = view('admin.meeting-events.table', compact('events'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $events->currentPage(),
                'last_page' => $events->lastPage(),
                'total' => $events->total()
            ]
        ]);
    }

    /**
     * Toggle event status via AJAX.
     *
     * @param  \App\Models\MeetingAddress  $meetingEvent
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleStatusAjax(MeetingAddress $meetingEvent)
    {
        $meetingEvent->is_event_enabled = !$meetingEvent->is_event_enabled;
        $meetingEvent->save();

        $status = $meetingEvent->is_event_enabled ? 'enabled' : 'disabled';

        return response()->json([
            'success' => true,
            'message' => "Meeting event {$status} successfully.",
            'status' => $meetingEvent->is_event_enabled
        ]);
    }

    /**
     * Delete event via AJAX.
     *
     * @param  \App\Models\MeetingAddress  $meetingEvent
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyAjax(MeetingAddress $meetingEvent)
    {
        $meetingEvent->delete();

        return response()->json([
            'success' => true,
            'message' => 'Meeting event deleted successfully.'
        ]);
    }
}
