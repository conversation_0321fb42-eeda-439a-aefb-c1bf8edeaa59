<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WithdrawalRequest;
use App\Models\UserBankAccount;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class WithdrawalController extends Controller
{
    /**
     * Display withdrawal requests.
     */
    public function index()
    {
        $withdrawals = WithdrawalRequest::with(['user', 'bankAccount', 'processedBy'])
                                       ->orderBy('requested_at', 'desc')
                                       ->paginate(20);



        $stats = [
            'pending' => WithdrawalRequest::where('status', 'pending')->count(),
            'processing' => WithdrawalRequest::where('status', 'processing')->count(),
            'completed' => WithdrawalRequest::where('status', 'completed')->count(),
            'failed' => WithdrawalRequest::where('status', 'failed')->count(),
            'total_amount_pending' => WithdrawalRequest::where('status', 'pending')->sum('amount'),
            'total_amount_completed' => WithdrawalRequest::where('status', 'completed')->sum('amount'),
        ];

        return view('admin.withdrawals.index', compact('withdrawals', 'stats'));
    }

    /**
     * Show withdrawal details.
     */
    public function show($id)
    {
        $withdrawal = WithdrawalRequest::with(['user', 'bankAccount', 'processedBy'])
                                      ->findOrFail($id);

        return view('admin.withdrawals.show', compact('withdrawal'));
    }

    /**
     * Process withdrawal (mark as completed).
     */
    public function process(Request $request, $id): JsonResponse
    {
        $request->validate([
            'transaction_reference' => 'required|string|max:255',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $withdrawal = WithdrawalRequest::findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'This withdrawal request cannot be processed.'
                ], 400);
            }

            $withdrawal->update([
                'admin_notes' => $request->admin_notes,
            ]);

            $withdrawal->markCompleted(
                $request->transaction_reference,
                auth()->user()->id
            );

            Log::info('Admin processed withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'admin_user' => auth()->user()->id,
                'transaction_reference' => $request->transaction_reference,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal processed successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process withdrawal', [
                'withdrawal_id' => $id,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process withdrawal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject withdrawal request.
     */
    public function reject(Request $request, $id): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $withdrawal = WithdrawalRequest::findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'This withdrawal request cannot be rejected.'
                ], 400);
            }

            $withdrawal->update([
                'admin_notes' => $request->admin_notes,
            ]);

            $withdrawal->markFailed(
                $request->reason,
                auth()->user()->id
            );

            Log::info('Admin rejected withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'admin_user' => auth()->user()->id,
                'reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal request rejected successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to reject withdrawal', [
                'withdrawal_id' => $id,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to reject withdrawal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update withdrawal status.
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,processing,completed,failed',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $withdrawal = WithdrawalRequest::findOrFail($id);

            $withdrawal->update([
                'status' => $request->status,
                'admin_notes' => $request->admin_notes,
                'processed_by' => auth()->user()->id,
            ]);

            // Create notification for user
            \App\Models\Notification::create([
                'user_id' => $withdrawal->user_id,
                'type' => 'withdrawal_status_updated',
                'title' => 'Withdrawal Status Updated',
                'message' => "Your withdrawal request status has been updated to: " . ucfirst($request->status),
                'body' => "Your withdrawal request of ₹{$withdrawal->amount} has been updated. Status: " . ucfirst($request->status) . ($request->admin_notes ? ". Notes: {$request->admin_notes}" : ""),
                'data' => [
                    'withdrawal_id' => $withdrawal->id,
                    'status' => $request->status,
                    'admin_notes' => $request->admin_notes,
                ],
            ]);

            Log::info('Admin updated withdrawal status', [
                'withdrawal_id' => $withdrawal->id,
                'admin_user' => auth()->user()->id,
                'old_status' => $withdrawal->status,
                'new_status' => $request->status,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal status updated successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update withdrawal status', [
                'withdrawal_id' => $id,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update withdrawal status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get withdrawal details for modal view.
     */
    public function getDetails($id): JsonResponse
    {
        try {
            $withdrawal = WithdrawalRequest::with(['user', 'bankAccount', 'processedBy'])
                                          ->findOrFail($id);

            return response()->json([
                'success' => true,
                'withdrawal' => $withdrawal
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load withdrawal details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get withdrawal statistics.
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = [
                'today' => [
                    'requests' => WithdrawalRequest::whereDate('requested_at', today())->count(),
                    'amount' => WithdrawalRequest::whereDate('requested_at', today())->sum('amount'),
                ],
                'this_month' => [
                    'requests' => WithdrawalRequest::whereMonth('requested_at', now()->month)->count(),
                    'amount' => WithdrawalRequest::whereMonth('requested_at', now()->month)->sum('amount'),
                ],
                'pending' => [
                    'count' => WithdrawalRequest::where('status', 'pending')->count(),
                    'amount' => WithdrawalRequest::where('status', 'pending')->sum('amount'),
                ],
                'completed' => [
                    'count' => WithdrawalRequest::where('status', 'completed')->count(),
                    'amount' => WithdrawalRequest::where('status', 'completed')->sum('amount'),
                ],
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics.'
            ], 500);
        }
    }

    /**
     * Export withdrawals data.
     */
    public function export(Request $request)
    {
        $query = WithdrawalRequest::with(['user', 'bankAccount']);

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->from_date) {
            $query->whereDate('requested_at', '>=', $request->from_date);
        }

        if ($request->to_date) {
            $query->whereDate('requested_at', '<=', $request->to_date);
        }

        $withdrawals = $query->orderBy('requested_at', 'desc')->get();

        $filename = 'withdrawals_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($withdrawals) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID', 'User Name', 'User Email', 'Amount', 'Processing Fee', 'Net Amount',
                'Bank Name', 'Account Number', 'Status', 'Requested At', 'Processed At',
                'Transaction Reference', 'Admin Notes'
            ]);

            // CSV data
            foreach ($withdrawals as $withdrawal) {
                fputcsv($file, [
                    $withdrawal->id,
                    $withdrawal->user->name,
                    $withdrawal->user->email,
                    $withdrawal->amount,
                    $withdrawal->processing_fee,
                    $withdrawal->net_amount,
                    $withdrawal->bankAccount->bank_name,
                    $withdrawal->bankAccount->masked_account_number,
                    $withdrawal->status_display,
                    $withdrawal->requested_at->format('Y-m-d H:i:s'),
                    $withdrawal->processed_at ? $withdrawal->processed_at->format('Y-m-d H:i:s') : '',
                    $withdrawal->transaction_reference ?? '',
                    $withdrawal->admin_notes ?? '',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
