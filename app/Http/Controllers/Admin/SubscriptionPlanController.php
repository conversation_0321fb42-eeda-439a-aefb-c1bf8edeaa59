<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class SubscriptionPlanController extends Controller
{
    /**
     * Display a listing of subscription plans.
     */
    public function index(Request $request): View
    {
        $query = SubscriptionPlan::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDirection = $request->get('sort_direction', 'asc');
        $allowedSortFields = ['name', 'duration_months', 'amount', 'sort_order', 'created_at'];
        $allowedDirections = ['asc', 'desc'];

        // Validate sort direction
        if (!in_array($sortDirection, $allowedDirections)) {
            $sortDirection = 'asc';
        }

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortDirection);
        } else {
            $query->orderBy('sort_order')->orderBy('duration_months');
        }

        $plans = $query->withCount('userSubscriptions')->paginate(20);

        return view('admin.subscription-plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new subscription plan.
     */
    public function create(): View
    {
        return view('admin.subscription-plans.create');
    }

    /**
     * Store a newly created subscription plan.
     */
    public function store(Request $request): JsonResponse
    {


        try {
            $request->validate([
                'name' => 'required|string|max:255|min:1',
                'duration_months' => 'required|integer|min:1|max:12',
                'amount' => 'required|numeric|min:0.01|max:999999',
                'original_price' => 'required|numeric|min:0.01|max:999999',
                'discount_price' => 'nullable|numeric|min:0.01|max:999999|lt:original_price',
                'description' => 'nullable|string|max:1000',
                'is_active' => 'nullable|boolean',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            // Business rule: Yearly plans (12 months) should not exceed ₹499
            if ($request->duration_months == 12) {
                $effectivePrice = $request->discount_price ?? $request->original_price;
                if ($effectivePrice > 499) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Yearly subscription plans cannot exceed ₹499. Please adjust the pricing.',
                        'errors' => [
                            'discount_price' => ['Yearly plans must be ₹499 or less'],
                            'original_price' => ['Yearly plans must be ₹499 or less']
                        ]
                    ], 422);
                }
            }
        } catch (\Illuminate\Validation\ValidationException $e) {

            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        }

        try {
            $plan = SubscriptionPlan::create([
                'name' => $request->name,
                'duration_months' => $request->duration_months,
                'amount' => $request->original_price, // Keep amount for backward compatibility
                'original_price' => $request->original_price,
                'discount_price' => $request->discount_price,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->sort_order ?? 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription plan created successfully.',
                'plan' => $plan
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to create subscription plan:', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create subscription plan. Please try again.'
            ], 500);
        }
    }

    /**
     * Display the specified subscription plan.
     */
    public function show(SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        $subscriptionPlan->load(['userSubscriptions' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'plan' => $subscriptionPlan
        ]);
    }

    /**
     * Show the form for editing the specified subscription plan.
     */
    public function edit(SubscriptionPlan $subscriptionPlan): View
    {
        return view('admin.subscription-plans.edit', compact('subscriptionPlan'));
    }

    /**
     * Update the specified subscription plan.
     */
    public function update(Request $request, SubscriptionPlan $subscriptionPlan): JsonResponse
    {


        try {
            $request->validate([
                'name' => 'required|string|max:255|min:1',
                'duration_months' => 'required|integer|min:1|max:12',
                'amount' => 'required|numeric|min:0.01|max:999999',
                'original_price' => 'required|numeric|min:0.01|max:999999',
                'discount_price' => 'nullable|numeric|min:0.01|max:999999|lt:original_price',
                'description' => 'nullable|string|max:1000',
                'is_active' => 'nullable|boolean',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            // Business rule: Yearly plans (12 months) should not exceed ₹499
            if ($request->duration_months == 12) {
                $effectivePrice = $request->discount_price ?? $request->original_price;
                if ($effectivePrice > 499) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Yearly subscription plans cannot exceed ₹499. Please adjust the pricing.',
                        'errors' => [
                            'discount_price' => ['Yearly plans must be ₹499 or less'],
                            'original_price' => ['Yearly plans must be ₹499 or less']
                        ]
                    ], 422);
                }
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Subscription plan update validation failed:', [
                'plan_id' => $subscriptionPlan->id,
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        }

        try {
            $subscriptionPlan->update([
                'name' => $request->name,
                'duration_months' => $request->duration_months,
                'amount' => $request->original_price, // Keep amount for backward compatibility
                'original_price' => $request->original_price,
                'discount_price' => $request->discount_price,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active'),
                'sort_order' => $request->sort_order ?? $subscriptionPlan->sort_order,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription plan updated successfully.',
                'plan' => $subscriptionPlan->fresh()
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to update subscription plan:', [
                'plan_id' => $subscriptionPlan->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update subscription plan. Please try again.'
            ], 500);
        }
    }

    /**
     * Remove the specified subscription plan.
     */
    public function destroy(SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        // Check if plan has active subscriptions
        $activeSubscriptionsCount = $subscriptionPlan->userSubscriptions()
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        if ($activeSubscriptionsCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete plan. It has {$activeSubscriptionsCount} active subscriptions."
            ], 400);
        }

        $subscriptionPlan->delete();

        return response()->json([
            'success' => true,
            'message' => 'Subscription plan deleted successfully.'
        ]);
    }

    /**
     * Toggle the active status of a subscription plan.
     */
    public function toggleStatus(SubscriptionPlan $subscriptionPlan): JsonResponse
    {
        $subscriptionPlan->update([
            'is_active' => !$subscriptionPlan->is_active
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Plan status updated successfully.',
            'is_active' => $subscriptionPlan->is_active
        ]);
    }

    /**
     * Get subscription plans data for AJAX requests.
     */
    public function getData(Request $request): JsonResponse
    {
        $query = SubscriptionPlan::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDirection = $request->get('sort_direction', 'asc');
        $allowedSortFields = ['name', 'duration_months', 'amount', 'sort_order', 'created_at'];
        $allowedDirections = ['asc', 'desc'];

        // Validate sort direction
        if (!in_array($sortDirection, $allowedDirections)) {
            $sortDirection = 'asc';
        }

        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $plans = $query->withCount('userSubscriptions')->paginate(20);

        $html = view('admin.subscription-plans.table', compact('plans'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $plans->currentPage(),
                'last_page' => $plans->lastPage(),
                'total' => $plans->total()
            ]
        ]);
    }
}
