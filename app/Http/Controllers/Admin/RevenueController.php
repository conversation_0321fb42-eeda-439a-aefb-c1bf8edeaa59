<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminRevenue;
use App\Models\TimeSpendingBooking;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class RevenueController extends Controller
{
    /**
     * Display the revenue dashboard.
     */
    public function index(Request $request)
    {
        // Get date range from request or default to current month
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->toDateString());

        // Get revenue statistics
        $revenueStats = AdminRevenue::getRevenueStats($startDate, $endDate);

        // Get recent revenue records
        $recentRevenues = AdminRevenue::with(['booking.client', 'booking.provider', 'subscription.user', 'subscription.subscriptionPlan'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get monthly revenue trend (last 6 months)
        $monthlyTrend = $this->getMonthlyRevenueTrend();

        // Get total platform statistics
        $totalStats = [
            'total_platform_fees' => AdminRevenue::getTotalPlatformFees(),
            'total_commission' => AdminRevenue::getTotalCommission(),
            'total_revenue' => AdminRevenue::getTotalPlatformRevenue(),
            'total_subscription_revenue' => AdminRevenue::where('revenue_type', 'subscription_payment')->sum('total_admin_earnings'),
            'total_bookings' => TimeSpendingBooking::where('payment_status', 'paid')->count(),
        ];

        return view('admin.revenue.index', compact(
            'revenueStats',
            'recentRevenues',
            'monthlyTrend',
            'totalStats',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Get revenue statistics via AJAX.
     */
    public function getStats(Request $request): JsonResponse
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $stats = AdminRevenue::getRevenueStats($startDate, $endDate);

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Get detailed revenue records.
     */
    public function getRevenues(Request $request): JsonResponse
    {
        $query = AdminRevenue::with(['booking.client', 'booking.provider', 'subscription.user', 'subscription.subscriptionPlan']);

        // Apply date filters
        if ($request->has('start_date') && $request->start_date) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Apply search filter
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('booking', function ($q) use ($search) {
                $q->whereHas('client', function ($clientQuery) use ($search) {
                    $clientQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                })->orWhereHas('provider', function ($providerQuery) use ($search) {
                    $providerQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                });
            });
        }

        $revenues = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'revenues' => $revenues
        ]);
    }

    /**
     * Get monthly revenue trend for the last 6 months.
     */
    private function getMonthlyRevenueTrend(): array
    {
        $months = [];
        $data = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            $monthStats = AdminRevenue::getRevenueStats(
                $startOfMonth->toDateString(),
                $endOfMonth->toDateString()
            );

            $months[] = $date->format('M Y');
            $data[] = $monthStats['total_revenue'];
        }

        return [
            'months' => $months,
            'data' => $data
        ];
    }

    /**
     * Export revenue data to CSV.
     */
    public function export(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $query = AdminRevenue::with('booking.client', 'booking.provider');

        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $revenues = $query->orderBy('created_at', 'desc')->get();

        $filename = 'admin_revenue_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($revenues) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Date',
                'Booking ID',
                'Client Name',
                'Provider Name',
                'Platform Fee',
                'Commission Amount',
                'Total Admin Earnings',
                'Base Amount',
                'Total Amount'
            ]);

            // CSV data
            foreach ($revenues as $revenue) {
                fputcsv($file, [
                    $revenue->created_at->format('Y-m-d H:i:s'),
                    $revenue->booking_id,
                    $revenue->booking->client->name ?? 'N/A',
                    $revenue->booking->provider->name ?? 'N/A',
                    $revenue->platform_fee,
                    $revenue->commission_amount,
                    $revenue->total_admin_earnings,
                    $revenue->booking->base_amount ?? 0,
                    $revenue->booking->total_amount ?? 0,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
