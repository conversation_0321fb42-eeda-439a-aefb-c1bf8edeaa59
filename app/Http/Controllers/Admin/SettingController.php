<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    /**
     * Display a listing of the settings.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Auto-disable expired site modes before loading settings
        $disabledModes = $this->autoDisableExpiredModes();

        $settings = Setting::all()->groupBy('group');

        // Check if this is an AJAX request
        if (request()->ajax()) {
            $html = view('admin.settings.index', compact('settings'))->render();
            return $html;
        }

        // Add notification if any modes were auto-disabled
        $request = request();
        if (!empty($disabledModes)) {
            $message = 'The following site modes have been automatically disabled due to expired time: ' . implode(', ', $disabledModes);
            $request->session()->flash('info', $message);
        }

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the specified setting in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'nullable|array',
            'settings.*' => 'nullable|string',
            'settings.platform_fee' => 'nullable|numeric|min:0|max:10000',
            'settings.commission_percentage' => 'nullable|numeric|min:0|max:100',
            'settings.maintenance_mode_enabled' => 'nullable|in:0,1',
            'settings.maintenance_mode_end_time' => 'nullable|date',
            'settings.coming_soon_mode_enabled' => 'nullable|in:0,1',
            'settings.coming_soon_launch_time' => 'nullable|date',
            'settings.custom_admin_url' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\-_]*$/|not_in:admin,api,auth,login,register,home,about,contact,privacy,terms,sitemap,manifest,sw',
            // Social Media URL validations
            'settings.facebook_url' => 'nullable|url|max:255',
            'settings.instagram_url' => 'nullable|url|max:255',
            'settings.twitter_url' => 'nullable|url|max:255',
            'settings.linkedin_url' => 'nullable|url|max:255',
            'settings.youtube_url' => 'nullable|url|max:255',
            'settings.whatsapp_url' => 'nullable|url|max:255',
            'header_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'footer_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'favicon' => 'nullable|file|mimes:jpeg,png,jpg,gif,ico|max:1024',
            'mobile_icon' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Additional validation for custom admin URL
        if ($request->has('settings.custom_admin_url')) {
            $customAdminUrl = trim($request->input('settings.custom_admin_url'), '/');

            if (!empty($customAdminUrl)) {
                // Check if the custom URL conflicts with existing routes
                $reservedPaths = [
                    'admin', 'api', 'auth', 'login', 'register', 'home', 'about', 'contact',
                    'privacy', 'terms', 'sitemap', 'manifest', 'sw', 'storage', 'public',
                    'assets', 'css', 'js', 'images', 'uploads', 'files', 'media',
                    'users', 'events', 'bookings', 'chat', 'notifications', 'profile',
                    'settings', 'dashboard', 'maintenance', 'coming-soon'
                ];

                if (in_array(strtolower($customAdminUrl), $reservedPaths)) {
                    return redirect()->back()
                        ->withErrors(['settings.custom_admin_url' => 'This URL path is reserved and cannot be used. Please choose a different path.'])
                        ->withInput();
                }

                // Validate format
                if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $customAdminUrl)) {
                    return redirect()->back()
                        ->withErrors(['settings.custom_admin_url' => 'Custom admin URL can only contain letters, numbers, hyphens, and underscores.'])
                        ->withInput();
                }

                // Check minimum length
                if (strlen($customAdminUrl) < 3) {
                    return redirect()->back()
                        ->withErrors(['settings.custom_admin_url' => 'Custom admin URL must be at least 3 characters long.'])
                        ->withInput();
                }
            }
        }

        // Update regular settings
        if ($request->has('settings')) {
            foreach ($request->settings as $key => $value) {
                // Convert datetime-local format to MySQL datetime format
                if (in_array($key, ['maintenance_mode_end_time', 'coming_soon_launch_time']) && !empty($value)) {
                    try {
                        // Convert from Y-m-d\TH:i to Y-m-d H:i:s
                        $value = \Carbon\Carbon::createFromFormat('Y-m-d\TH:i', $value)->format('Y-m-d H:i:s');
                    } catch (\Exception $e) {
                        // If conversion fails, try to parse as is
                        try {
                            $value = \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s');
                        } catch (\Exception $e2) {
                            // If all fails, keep original value
                        }
                    }
                }
                Setting::set($key, $value);
            }

            // Handle mutual exclusivity of site modes
            $this->handleSiteModeExclusivity($request);
        }

        // Handle logo uploads
        $logoFields = ['header_logo', 'footer_logo', 'favicon', 'mobile_icon'];

        foreach ($logoFields as $field) {
            if ($request->hasFile($field)) {
                try {
                    // Delete old file if exists
                    $oldFile = Setting::get($field);
                    if ($oldFile && Storage::disk('public')->exists($oldFile)) {
                        Storage::disk('public')->delete($oldFile);
                    }

                    // Store new file
                    $file = $request->file($field);
                    $path = $file->store('logos', 'public');
                    Setting::set($field, $path);

                } catch (\Exception $e) {
                    return redirect()->back()
                        ->withErrors([$field => "Failed to upload {$field}. Please try again."])
                        ->withInput();
                }
            }
        }

        // Check if any site mode was activated and show appropriate message
        $maintenanceEnabled = $request->input('settings.maintenance_mode_enabled', '0') === '1';
        $comingSoonEnabled = $request->input('settings.coming_soon_mode_enabled', '0') === '1';
        $customAdminUrl = $request->input('settings.custom_admin_url', '');

        $message = 'Settings updated successfully.';
        if ($maintenanceEnabled) {
            $message = 'Settings updated successfully. Maintenance mode is now active - only admin users can access the site.';
        } elseif ($comingSoonEnabled) {
            $message = 'Settings updated successfully. Coming soon mode is now active - only admin users can access the site.';
        }

        // Add custom admin URL message if changed
        if (!empty($customAdminUrl)) {
            $customAdminUrl = trim($customAdminUrl, '/');
            $message .= ' Custom admin URL is now active. Access admin panel at: ' . url($customAdminUrl);
        } elseif ($request->has('settings.custom_admin_url') && empty($customAdminUrl)) {
            $message .= ' Custom admin URL has been disabled. Admin panel is now accessible at the default URL: ' . url('admin');
        }

        return redirect()->route('admin.settings.index')
            ->with('success', $message);
    }

    /**
     * Handle mutual exclusivity of site modes
     */
    private function handleSiteModeExclusivity($request)
    {
        $maintenanceEnabled = $request->input('settings.maintenance_mode_enabled', '0') === '1';
        $comingSoonEnabled = $request->input('settings.coming_soon_mode_enabled', '0') === '1';

        // If both are enabled, maintenance mode takes priority
        if ($maintenanceEnabled && $comingSoonEnabled) {
            Setting::set('coming_soon_mode_enabled', '0');
        }

        // Validate required fields when modes are enabled
        if ($comingSoonEnabled && !$request->input('settings.coming_soon_launch_time')) {
            throw new \Exception('Launch date and time is required when Coming Soon mode is enabled.');
        }
    }

    /**
     * Handle mutual exclusivity of site modes for AJAX requests
     */
    private function handleSiteModeExclusivityAjax($request)
    {
        $maintenanceEnabled = $request->input('settings.maintenance_mode_enabled', '0') === '1';
        $comingSoonEnabled = $request->input('settings.coming_soon_mode_enabled', '0') === '1';

        // If both are enabled, maintenance mode takes priority
        if ($maintenanceEnabled && $comingSoonEnabled) {
            Setting::set('coming_soon_mode_enabled', '0');
        }

        // Validate required fields when modes are enabled
        if ($comingSoonEnabled && !$request->input('settings.coming_soon_launch_time')) {
            throw new \Exception('Launch date and time is required when Coming Soon mode is enabled.');
        }
    }

    /**
     * Upload a file for a setting.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function uploadFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string',
            'file' => 'required|file|max:' . (Setting::get('max_upload_size', 10) * 1024),
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $file = $request->file('file');
        $path = $file->store('settings', 'public');

        Setting::set($request->key, $path);

        return redirect()->route('admin.settings.index')
            ->with('success', 'File uploaded successfully.');
    }

    /**
     * Update settings via AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAjax(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*' => 'nullable|string',
            'settings.maintenance_mode_enabled' => 'nullable|in:0,1',
            'settings.maintenance_mode_end_time' => 'nullable|date',
            'settings.coming_soon_mode_enabled' => 'nullable|in:0,1',
            'settings.coming_soon_launch_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            foreach ($request->settings as $key => $value) {
                // Convert datetime-local format to MySQL datetime format
                if (in_array($key, ['maintenance_mode_end_time', 'coming_soon_launch_time']) && !empty($value)) {
                    try {
                        // Convert from Y-m-d\TH:i to Y-m-d H:i:s
                        $value = \Carbon\Carbon::createFromFormat('Y-m-d\TH:i', $value)->format('Y-m-d H:i:s');
                    } catch (\Exception $e) {
                        // If conversion fails, try to parse as is
                        try {
                            $value = \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s');
                        } catch (\Exception $e2) {
                            // If all fails, keep original value
                        }
                    }
                }
                Setting::set($key, $value);
            }

            // Handle mutual exclusivity of site modes
            $this->handleSiteModeExclusivityAjax($request);

            return response()->json([
                'success' => true,
                'message' => 'Settings updated successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Test Razorpay configuration.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testRazorpay()
    {
        $razorpayKeyId = Setting::get('razorpay_key_id');
        $razorpayKeySecret = Setting::get('razorpay_key_secret');

        if (empty($razorpayKeyId) || empty($razorpayKeySecret)) {
            return response()->json([
                'success' => false,
                'message' => 'Razorpay keys are not configured. Please set both Key ID and Key Secret.'
            ], 400);
        }

        try {
            // Try to create a test order to validate the keys
            $api = new \Razorpay\Api\Api($razorpayKeyId, $razorpayKeySecret);

            $orderData = [
                'receipt' => 'test_' . time(),
                'amount' => 100, // ₹1 in paise
                'currency' => 'INR',
                'notes' => [
                    'test' => 'configuration_test'
                ]
            ];

            $order = $api->order->create($orderData);

            return response()->json([
                'success' => true,
                'message' => 'Razorpay configuration is working correctly!',
                'order_id' => $order['id']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Razorpay configuration error: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get current site mode status for admin panel
     */
    public function getSiteModeStatus()
    {
        // Auto-disable expired modes before returning status
        $disabledModes = $this->autoDisableExpiredModes();

        return response()->json([
            'maintenance_enabled' => Setting::get('maintenance_mode_enabled', '0') === '1',
            'coming_soon_enabled' => Setting::get('coming_soon_mode_enabled', '0') === '1',
            'maintenance_end_time' => Setting::get('maintenance_mode_end_time'),
            'coming_soon_launch_time' => Setting::get('coming_soon_launch_time'),
            'disabled_modes' => $disabledModes, // Include info about auto-disabled modes
        ]);
    }

    /**
     * Auto-disable expired site modes
     *
     * @return array List of disabled modes
     */
    private function autoDisableExpiredModes()
    {
        $disabledModes = [];

        // Check maintenance mode
        $maintenanceModeEnabled = Setting::get('maintenance_mode_enabled', '0') === '1';
        if ($maintenanceModeEnabled) {
            $endTime = Setting::get('maintenance_mode_end_time');
            if ($endTime && \Carbon\Carbon::parse($endTime)->isPast()) {
                Setting::set('maintenance_mode_enabled', '0');
                $disabledModes[] = 'Maintenance Mode';
            }
        }

        // Check coming soon mode
        $comingSoonModeEnabled = Setting::get('coming_soon_mode_enabled', '0') === '1';
        if ($comingSoonModeEnabled) {
            $launchTime = Setting::get('coming_soon_launch_time');
            if ($launchTime && \Carbon\Carbon::parse($launchTime)->isPast()) {
                Setting::set('coming_soon_mode_enabled', '0');
                $disabledModes[] = 'Coming Soon Mode';
            }
        }

        return $disabledModes;
    }
}
