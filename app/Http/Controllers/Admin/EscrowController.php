<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TimeSpendingBooking;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class EscrowController extends Controller
{
    /**
     * Display escrow management dashboard.
     */
    public function index()
    {
        // Get bookings with escrow status
        $heldBookings = TimeSpendingBooking::where('escrow_status', 'held')
            ->with(['client', 'provider', 'meetingVerification'])
            ->orderBy('escrow_held_at', 'desc')
            ->paginate(20);

        $disputedBookings = TimeSpendingBooking::where('escrow_status', 'disputed')
            ->with(['client', 'provider', 'meetingVerification', 'disputedBy', 'resolvedBy'])
            ->orderBy('disputed_at', 'desc')
            ->paginate(10);

        $recentReleases = TimeSpendingBooking::where('escrow_status', 'released')
            ->with(['client', 'provider'])
            ->orderBy('escrow_released_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.escrow.index', compact('heldBookings', 'disputedBookings', 'recentReleases'));
    }

    /**
     * Manually release payment from escrow.
     */
    public function releasePayment(Request $request, $bookingId): JsonResponse
    {
        try {
            $booking = TimeSpendingBooking::findOrFail($bookingId);

            if ($booking->escrow_status !== 'held') {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment is not currently held in escrow.'
                ], 400);
            }

            $booking->releaseFromEscrow();

            Log::info('Admin manually released escrow payment', [
                'booking_id' => $booking->id,
                'admin_user' => auth()->user()->id,
                'provider_id' => $booking->provider_id,
                'amount' => $booking->total_amount,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment released successfully to provider.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to manually release escrow payment', [
                'booking_id' => $bookingId,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to release payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refund payment to client.
     */
    public function refundPayment(Request $request, $bookingId): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $booking = TimeSpendingBooking::findOrFail($bookingId);

            if (!in_array($booking->escrow_status, ['held', 'disputed'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment cannot be refunded in current status.'
                ], 400);
            }

            // Process refund to client's wallet
            $clientWallet = \App\Models\UserWallet::getOrCreate($booking->client_id);
            $clientWallet->addMoney(
                $booking->total_amount,
                "Refund for booking #{$booking->id} - {$request->reason}",
                $booking->id,
                ['refund_reason' => $request->reason]
            );

            $booking->update([
                'escrow_status' => 'refunded',
                'refund_status' => 'processed',
                'refund_amount' => $booking->total_amount,
                'refunded_at' => now(),
                'dispute_status' => 'resolved',
                'resolved_at' => now(),
                'resolved_by' => auth()->user()->id,
                'admin_notes' => "Refund processed by admin. Reason: {$request->reason}",
            ]);

            Log::info('Admin processed escrow refund', [
                'booking_id' => $booking->id,
                'admin_user' => auth()->user()->id,
                'client_id' => $booking->client_id,
                'amount' => $booking->total_amount,
                'reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully to client.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process escrow refund', [
                'booking_id' => $bookingId,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process refund: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking details for dispute resolution.
     */
    public function getBookingDetails($bookingId): JsonResponse
    {
        try {
            $booking = TimeSpendingBooking::with([
                'client',
                'provider',
                'meetingVerification',
                'disputedBy',
                'resolvedBy'
            ])->findOrFail($bookingId);

            return response()->json([
                'success' => true,
                'booking' => $booking,
                'verification_status' => $booking->meetingVerification
                    ? $booking->meetingVerification->getUserVerificationStatus($booking->client_id)
                    : null,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch booking details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update dispute status and add admin notes.
     */
    public function updateDisputeStatus(Request $request, $bookingId): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,investigating,resolved,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $booking = TimeSpendingBooking::findOrFail($bookingId);

            if ($booking->escrow_status !== 'disputed') {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking is not in dispute status.'
                ], 400);
            }

            $updateData = [
                'dispute_status' => $request->status,
            ];

            if ($request->admin_notes) {
                $updateData['admin_notes'] = $request->admin_notes;
            }

            if ($request->status === 'resolved' || $request->status === 'rejected') {
                $updateData['resolved_at'] = now();
                $updateData['resolved_by'] = auth()->user()->id;
            }

            $booking->update($updateData);

            // Create notification for the client
            \App\Models\Notification::create([
                'user_id' => $booking->client_id,
                'type' => 'dispute_updated',
                'title' => 'Dispute Status Updated',
                'message' => "Your dispute for booking #{$booking->id} has been updated to: " . ucfirst($request->status),
                'body' => "Your dispute regarding the booking with {$booking->provider->name} has been updated. Status: " . ucfirst($request->status) . ($request->admin_notes ? ". Admin notes: {$request->admin_notes}" : ""),
                'data' => [
                    'booking_id' => $booking->id,
                    'dispute_status' => $request->status,
                ]
            ]);

            Log::info('Admin updated dispute status', [
                'booking_id' => $booking->id,
                'admin_user' => auth()->user()->id,
                'old_status' => $booking->dispute_status,
                'new_status' => $request->status,
                'admin_notes' => $request->admin_notes,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Dispute status updated successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update dispute status', [
                'booking_id' => $bookingId,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update dispute status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resolve dispute in favor of client (refund).
     */
    public function resolveForClient(Request $request, $bookingId): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $booking = TimeSpendingBooking::findOrFail($bookingId);

            if ($booking->escrow_status !== 'disputed') {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking is not in dispute status.'
                ], 400);
            }

            // Process refund to client's wallet
            $clientWallet = \App\Models\UserWallet::getOrCreate($booking->client_id);
            $clientWallet->addMoney(
                $booking->total_amount,
                "Dispute resolved in your favor - Booking #{$booking->id}",
                $booking->id,
                [
                    'refund_reason' => $request->reason,
                    'dispute_resolution' => 'client_favor'
                ]
            );

            $booking->update([
                'escrow_status' => 'refunded',
                'refund_status' => 'processed',
                'refund_amount' => $booking->total_amount,
                'refunded_at' => now(),
                'dispute_status' => 'resolved',
                'resolved_at' => now(),
                'resolved_by' => auth()->user()->id,
                'admin_notes' => $request->admin_notes ?: "Dispute resolved in client's favor. Reason: {$request->reason}",
            ]);

            // Create notifications
            \App\Models\Notification::create([
                'user_id' => $booking->client_id,
                'type' => 'dispute_resolved',
                'title' => 'Dispute Resolved - Refund Processed',
                'message' => "Your dispute has been resolved in your favor. ₹{$booking->total_amount} has been refunded to your wallet.",
                'body' => "Good news! Your dispute for booking #{$booking->id} has been resolved in your favor. The full amount of ₹{$booking->total_amount} has been refunded to your wallet.",
                'data' => [
                    'booking_id' => $booking->id,
                    'refund_amount' => $booking->total_amount,
                ]
            ]);

            \App\Models\Notification::create([
                'user_id' => $booking->provider_id,
                'type' => 'dispute_resolved',
                'title' => 'Dispute Resolution Notice',
                'message' => "A dispute for booking #{$booking->id} has been resolved. Payment has been refunded to the client.",
                'body' => "A dispute raised by {$booking->client->name} for booking #{$booking->id} has been resolved. The payment has been refunded to the client.",
                'data' => [
                    'booking_id' => $booking->id,
                ]
            ]);

            Log::info('Admin resolved dispute in favor of client', [
                'booking_id' => $booking->id,
                'admin_user' => auth()->user()->id,
                'client_id' => $booking->client_id,
                'provider_id' => $booking->provider_id,
                'amount' => $booking->total_amount,
                'reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Dispute resolved in favor of client. Refund processed successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to resolve dispute for client', [
                'booking_id' => $bookingId,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to resolve dispute: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resolve dispute in favor of provider (release payment).
     */
    public function resolveForProvider(Request $request, $bookingId): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $booking = TimeSpendingBooking::findOrFail($bookingId);

            if ($booking->escrow_status !== 'disputed') {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking is not in dispute status.'
                ], 400);
            }

            // Release payment to provider
            $booking->releaseFromEscrow();

            $booking->update([
                'dispute_status' => 'resolved',
                'resolved_at' => now(),
                'resolved_by' => auth()->user()->id,
                'admin_notes' => $request->admin_notes ?: "Dispute resolved in provider's favor. Reason: {$request->reason}",
            ]);

            // Create notifications
            \App\Models\Notification::create([
                'user_id' => $booking->provider_id,
                'type' => 'dispute_resolved',
                'title' => 'Dispute Resolved - Payment Released',
                'message' => "The dispute for booking #{$booking->id} has been resolved in your favor. Payment has been released.",
                'body' => "Good news! The dispute for booking #{$booking->id} has been resolved in your favor. The payment of ₹{$booking->provider_amount} has been released to you.",
                'data' => [
                    'booking_id' => $booking->id,
                    'amount' => $booking->provider_amount,
                ]
            ]);

            \App\Models\Notification::create([
                'user_id' => $booking->client_id,
                'type' => 'dispute_resolved',
                'title' => 'Dispute Resolution Notice',
                'message' => "Your dispute for booking #{$booking->id} has been resolved. Payment has been released to the provider.",
                'body' => "Your dispute for booking #{$booking->id} has been reviewed and resolved. The payment has been released to the provider.",
                'data' => [
                    'booking_id' => $booking->id,
                ]
            ]);

            Log::info('Admin resolved dispute in favor of provider', [
                'booking_id' => $booking->id,
                'admin_user' => auth()->user()->id,
                'client_id' => $booking->client_id,
                'provider_id' => $booking->provider_id,
                'amount' => $booking->provider_amount,
                'reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Dispute resolved in favor of provider. Payment released successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to resolve dispute for provider', [
                'booking_id' => $bookingId,
                'admin_user' => auth()->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to resolve dispute: ' . $e->getMessage()
            ], 500);
        }
    }
}
