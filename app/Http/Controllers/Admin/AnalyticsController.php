<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\MeetingAddress;
use App\Models\TimeSpendingBooking;
use App\Models\UserSubscription;
use App\Models\EventPayment;
use App\Models\WalletTransaction;
use App\Models\ChatMessage;
use App\Models\CoupleActivityRequest;
use App\Models\AdminRevenue;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Display the analytics dashboard.
     */
    public function index(Request $request)
    {
        try {
            $timeFilter = $request->get('filter', 'month');
            $startDate = $this->getStartDate($timeFilter, $request->get('start_date'));
            $endDate = $this->getEndDate($timeFilter, $request->get('end_date'));

            // Get overview statistics
            $overviewStats = $this->getOverviewStats($startDate, $endDate);

            // Get chart data
            $userRegistrationData = $this->getUserRegistrationData($startDate, $endDate, $timeFilter);
            $revenueData = $this->getRevenueData($startDate, $endDate, $timeFilter);
            $demographicsData = $this->getDemographicsData();
            $featureUsageData = $this->getFeatureUsageData($startDate, $endDate);

            return view('admin.analytics.index', compact(
                'overviewStats',
                'userRegistrationData',
                'revenueData',
                'demographicsData',
                'featureUsageData',
                'timeFilter',
                'startDate',
                'endDate'
            ));
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Analytics dashboard error: ' . $e->getMessage());

            // Return with default empty data
            $overviewStats = [
                'total_users' => 0,
                'new_users' => 0,
                'total_revenue' => 0,
                'total_bookings' => 0,
                'total_events' => 0,
                'event_participants' => 0,
                'active_subscriptions' => 0,
                'chat_messages' => 0,
            ];

            $userRegistrationData = ['labels' => [], 'data' => [], 'total' => 0];
            $revenueData = ['timeline' => ['labels' => [], 'data' => []], 'by_source' => [], 'total' => 0];
            $demographicsData = ['gender' => [], 'age_groups' => []];
            $featureUsageData = [];
            $timeFilter = 'month';
            $startDate = now()->startOfMonth()->toDateString();
            $endDate = now()->endOfMonth()->toDateString();

            return view('admin.analytics.index', compact(
                'overviewStats',
                'userRegistrationData',
                'revenueData',
                'demographicsData',
                'featureUsageData',
                'timeFilter',
                'startDate',
                'endDate'
            ))->with('error', 'Unable to load analytics data. Please try again.');
        }
    }

    /**
     * Get analytics data via AJAX.
     */
    public function getData(Request $request)
    {
        try {
            $type = $request->get('type');
            $timeFilter = $request->get('filter', 'month');
            $startDate = $this->getStartDate($timeFilter, $request->get('start_date'));
            $endDate = $this->getEndDate($timeFilter, $request->get('end_date'));

            switch ($type) {
                case 'user_registrations':
                    return response()->json($this->getUserRegistrationData($startDate, $endDate, $timeFilter));
                case 'revenue':
                    return response()->json($this->getRevenueData($startDate, $endDate, $timeFilter));
                case 'demographics':
                    return response()->json($this->getDemographicsData());
                case 'feature_usage':
                    return response()->json($this->getFeatureUsageData($startDate, $endDate));
                case 'overview':
                    return response()->json($this->getOverviewStats($startDate, $endDate));
                default:
                    return response()->json(['error' => 'Invalid data type'], 400);
            }
        } catch (\Exception $e) {
            \Log::error('Analytics data error: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch analytics data'], 500);
        }
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($startDate, $endDate)
    {
        $totalUsers = User::where('role', 'user')->where('is_admin', false)->count();
        $newUsers = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $totalRevenue = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_admin_earnings');

        $totalBookings = TimeSpendingBooking::where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $totalEvents = MeetingAddress::where('is_event_enabled', true)->count();
        $eventParticipants = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $activeSubscriptions = UserSubscription::where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        $chatMessages = ChatMessage::whereBetween('created_at', [$startDate, $endDate])->count();

        return [
            'total_users' => $totalUsers,
            'new_users' => $newUsers,
            'total_revenue' => $totalRevenue,
            'total_bookings' => $totalBookings,
            'total_events' => $totalEvents,
            'event_participants' => $eventParticipants,
            'active_subscriptions' => $activeSubscriptions,
            'chat_messages' => $chatMessages,
        ];
    }

    /**
     * Get user registration data for charts.
     */
    private function getUserRegistrationData($startDate, $endDate, $timeFilter)
    {
        $registrations = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw($this->getSelectRaw($timeFilter) . ' as date, COUNT(*) as count')
            ->groupByRaw($this->getGroupByRaw($timeFilter))
            ->orderBy('date')
            ->get();

        $labels = [];
        $data = [];
        $period = Carbon::parse($startDate);
        $endPeriod = Carbon::parse($endDate);

        while ($period <= $endPeriod) {
            $dateKey = $period->format($this->getCarbonFormat($timeFilter));
            $labels[] = $this->formatLabel($period, $timeFilter);

            $count = $registrations->where('date', $dateKey)->first();
            $data[] = $count ? $count->count : 0;

            $period = $this->incrementPeriod($period, $timeFilter);
        }

        return [
            'labels' => $labels,
            'data' => $data,
            'total' => array_sum($data)
        ];
    }

    /**
     * Get revenue data for charts.
     */
    private function getRevenueData($startDate, $endDate, $timeFilter)
    {
        // Revenue by source
        $revenueBySource = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('revenue_type, SUM(total_admin_earnings) as total')
            ->groupBy('revenue_type')
            ->get();

        // Revenue over time
        $revenueOverTime = AdminRevenue::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw($this->getSelectRaw($timeFilter) . ' as date, SUM(total_admin_earnings) as total')
            ->groupByRaw($this->getGroupByRaw($timeFilter))
            ->orderBy('date')
            ->get();

        $labels = [];
        $data = [];
        $period = Carbon::parse($startDate);
        $endPeriod = Carbon::parse($endDate);

        while ($period <= $endPeriod) {
            $dateKey = $period->format($this->getCarbonFormat($timeFilter));
            $labels[] = $this->formatLabel($period, $timeFilter);

            $revenue = $revenueOverTime->where('date', $dateKey)->first();
            $data[] = $revenue ? (float) $revenue->total : 0;

            $period = $this->incrementPeriod($period, $timeFilter);
        }

        return [
            'timeline' => [
                'labels' => $labels,
                'data' => $data
            ],
            'by_source' => $revenueBySource->mapWithKeys(function ($item) {
                return [$this->formatRevenueType($item->revenue_type) => (float) $item->total];
            }),
            'total' => array_sum($data)
        ];
    }

    /**
     * Get demographics data.
     */
    private function getDemographicsData()
    {
        $genderData = User::where('role', 'user')
            ->where('is_admin', false)
            ->selectRaw('gender, COUNT(*) as count')
            ->groupBy('gender')
            ->get()
            ->mapWithKeys(function ($item) {
                return [ucfirst($item->gender ?? 'Unknown') => $item->count];
            });

        $ageGroups = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereNotNull('date_of_birth')
            ->selectRaw($this->getAgeGroupSelectRaw() . ' as age_group, COUNT(*) as count')
            ->groupBy('age_group')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->age_group => $item->count];
            });

        return [
            'gender' => $genderData,
            'age_groups' => $ageGroups
        ];
    }

    /**
     * Get feature usage data.
     */
    private function getFeatureUsageData($startDate, $endDate)
    {
        $profileCompletion = $this->getProfileCompletionStats();
        $eventStats = $this->getEventStats($startDate, $endDate);
        $subscriptionStats = $this->getSubscriptionStats($startDate, $endDate);
        $chatStats = $this->getChatStats($startDate, $endDate);
        $coupleActivityStats = $this->getCoupleActivityStats($startDate, $endDate);

        return [
            'profile_completion' => $profileCompletion,
            'events' => $eventStats,
            'subscriptions' => $subscriptionStats,
            'chat' => $chatStats,
            'couple_activity' => $coupleActivityStats
        ];
    }

    /**
     * Get profile completion statistics.
     */
    private function getProfileCompletionStats()
    {
        $totalUsers = User::where('role', 'user')->where('is_admin', false)->count();

        $completedProfiles = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereNotNull('name')
            ->whereNotNull('email')
            ->whereNotNull('gender')
            ->whereNotNull('date_of_birth')
            ->whereNotNull('profile_picture')
            ->count();

        $withGallery = User::where('role', 'user')
            ->where('is_admin', false)
            ->whereHas('galleryImages')
            ->count();

        return [
            'total_users' => $totalUsers,
            'completed_profiles' => $completedProfiles,
            'completion_rate' => $totalUsers > 0 ? round(($completedProfiles / $totalUsers) * 100, 2) : 0,
            'with_gallery' => $withGallery,
            'gallery_rate' => $totalUsers > 0 ? round(($withGallery / $totalUsers) * 100, 2) : 0
        ];
    }

    /**
     * Get event statistics.
     */
    private function getEventStats($startDate, $endDate)
    {
        $totalEvents = MeetingAddress::count();
        $activeEvents = MeetingAddress::where('is_event_enabled', true)->count();
        $coupleEvents = MeetingAddress::where('is_couple_event', true)->count();

        $eventPayments = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $eventRevenue = EventPayment::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount_paid');

        return [
            'total_events' => $totalEvents,
            'active_events' => $activeEvents,
            'couple_events' => $coupleEvents,
            'participants' => $eventPayments,
            'revenue' => $eventRevenue
        ];
    }

    /**
     * Get subscription statistics.
     */
    private function getSubscriptionStats($startDate, $endDate)
    {
        $activeSubscriptions = UserSubscription::where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        $newSubscriptions = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $subscriptionRevenue = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount_paid');

        $renewalRate = $this->calculateRenewalRate($startDate, $endDate);

        return [
            'active_subscriptions' => $activeSubscriptions,
            'new_subscriptions' => $newSubscriptions,
            'revenue' => $subscriptionRevenue,
            'renewal_rate' => $renewalRate
        ];
    }

    /**
     * Get chat statistics.
     */
    private function getChatStats($startDate, $endDate)
    {
        $totalMessages = ChatMessage::whereBetween('created_at', [$startDate, $endDate])->count();

        $activeChats = ChatMessage::whereBetween('created_at', [$startDate, $endDate])
            ->distinct('booking_id')
            ->count();

        $averageMessagesPerChat = $activeChats > 0 ? round($totalMessages / $activeChats, 2) : 0;

        return [
            'total_messages' => $totalMessages,
            'active_chats' => $activeChats,
            'average_messages_per_chat' => $averageMessagesPerChat
        ];
    }

    /**
     * Get couple activity statistics.
     */
    private function getCoupleActivityStats($startDate, $endDate)
    {
        $totalRequests = CoupleActivityRequest::whereBetween('created_at', [$startDate, $endDate])->count();
        $approvedRequests = CoupleActivityRequest::where('status', 'approved')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $activeCouples = User::where('role', 'user')
            ->where('is_admin', false)
            ->where('is_couple_activity_enabled', true)
            ->whereHas('ownPartnerRequest', function ($query) {
                $query->where('status', 'approved');
            })
            ->count();

        $approvalRate = $totalRequests > 0 ? round(($approvedRequests / $totalRequests) * 100, 2) : 0;

        return [
            'total_requests' => $totalRequests,
            'approved_requests' => $approvedRequests,
            'active_couples' => $activeCouples,
            'approval_rate' => $approvalRate
        ];
    }

    /**
     * Calculate subscription renewal rate.
     */
    private function calculateRenewalRate($startDate, $endDate)
    {
        $expiredSubscriptions = UserSubscription::whereBetween('expires_at', [$startDate, $endDate])
            ->where('status', 'expired')
            ->count();

        $renewedSubscriptions = UserSubscription::whereBetween('created_at', [$startDate, $endDate])
            ->whereHas('user.subscriptions', function ($query) use ($startDate) {
                $query->where('expires_at', '<', $startDate);
            })
            ->count();

        return $expiredSubscriptions > 0 ? round(($renewedSubscriptions / $expiredSubscriptions) * 100, 2) : 0;
    }

    /**
     * Export analytics data.
     */
    public function export(Request $request)
    {
        $timeFilter = $request->get('filter', 'month');
        $startDate = $this->getStartDate($timeFilter, $request->get('start_date'));
        $endDate = $this->getEndDate($timeFilter, $request->get('end_date'));

        $data = [
            'overview' => $this->getOverviewStats($startDate, $endDate),
            'user_registrations' => $this->getUserRegistrationData($startDate, $endDate, $timeFilter),
            'revenue' => $this->getRevenueData($startDate, $endDate, $timeFilter),
            'demographics' => $this->getDemographicsData(),
            'feature_usage' => $this->getFeatureUsageData($startDate, $endDate)
        ];

        $filename = 'analytics_' . $startDate . '_to_' . $endDate . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Get start date based on filter.
     */
    private function getStartDate($filter, $customStart = null)
    {
        if ($filter === 'custom' && $customStart) {
            return Carbon::parse($customStart)->startOfDay();
        }

        switch ($filter) {
            case 'today':
                return Carbon::today();
            case 'week':
                return Carbon::now()->startOfWeek();
            case 'month':
                return Carbon::now()->startOfMonth();
            case 'year':
                return Carbon::now()->startOfYear();
            default:
                return Carbon::now()->startOfMonth();
        }
    }

    /**
     * Get end date based on filter.
     */
    private function getEndDate($filter, $customEnd = null)
    {
        if ($filter === 'custom' && $customEnd) {
            return Carbon::parse($customEnd)->endOfDay();
        }

        switch ($filter) {
            case 'today':
                return Carbon::today()->endOfDay();
            case 'week':
                return Carbon::now()->endOfWeek();
            case 'month':
                return Carbon::now()->endOfMonth();
            case 'year':
                return Carbon::now()->endOfYear();
            default:
                return Carbon::now()->endOfMonth();
        }
    }

    /**
     * Get database-agnostic SELECT clause for date grouping.
     */
    private function getSelectRaw($filter)
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        if ($connection === 'sqlite') {
            switch ($filter) {
                case 'today':
                    return "strftime('%Y-%m-%d %H:00:00', created_at)";
                case 'week':
                case 'month':
                    return "strftime('%Y-%m-%d', created_at)";
                case 'year':
                    return "strftime('%Y-%m', created_at)";
                default:
                    return "strftime('%Y-%m-%d', created_at)";
            }
        } else {
            // MySQL/PostgreSQL
            switch ($filter) {
                case 'today':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00')";
                case 'week':
                case 'month':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
                case 'year':
                    return "DATE_FORMAT(created_at, '%Y-%m')";
                default:
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
            }
        }
    }

    /**
     * Get database-agnostic GROUP BY clause.
     */
    private function getGroupByRaw($filter)
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        if ($connection === 'sqlite') {
            switch ($filter) {
                case 'today':
                    return "strftime('%Y-%m-%d %H:00:00', created_at)";
                case 'week':
                case 'month':
                    return "strftime('%Y-%m-%d', created_at)";
                case 'year':
                    return "strftime('%Y-%m', created_at)";
                default:
                    return "strftime('%Y-%m-%d', created_at)";
            }
        } else {
            // MySQL/PostgreSQL
            switch ($filter) {
                case 'today':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00')";
                case 'week':
                case 'month':
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
                case 'year':
                    return "DATE_FORMAT(created_at, '%Y-%m')";
                default:
                    return "DATE_FORMAT(created_at, '%Y-%m-%d')";
            }
        }
    }

    /**
     * Get Carbon date format.
     */
    private function getCarbonFormat($filter)
    {
        switch ($filter) {
            case 'today':
                return 'Y-m-d H:00:00';
            case 'week':
            case 'month':
                return 'Y-m-d';
            case 'year':
                return 'Y-m';
            default:
                return 'Y-m-d';
        }
    }

    /**
     * Format label for charts.
     */
    private function formatLabel($period, $filter)
    {
        switch ($filter) {
            case 'today':
                return $period->format('H:00');
            case 'week':
            case 'month':
                return $period->format('M d');
            case 'year':
                return $period->format('M Y');
            default:
                return $period->format('M d');
        }
    }

    /**
     * Increment period based on filter.
     */
    private function incrementPeriod($period, $filter)
    {
        switch ($filter) {
            case 'today':
                return $period->addHour();
            case 'week':
            case 'month':
                return $period->addDay();
            case 'year':
                return $period->addMonth();
            default:
                return $period->addDay();
        }
    }

    /**
     * Get database-agnostic age group calculation.
     */
    private function getAgeGroupSelectRaw()
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        if ($connection === 'sqlite') {
            return "
                CASE
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 < 20 THEN 'Under 20'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 20 AND 25 THEN '20-25'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 26 AND 30 THEN '26-30'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 31 AND 35 THEN '31-35'
                    WHEN (julianday('now') - julianday(date_of_birth)) / 365.25 BETWEEN 36 AND 40 THEN '36-40'
                    ELSE 'Over 40'
                END
            ";
        } else {
            // MySQL/PostgreSQL
            return "
                CASE
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) < 20 THEN 'Under 20'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 20 AND 25 THEN '20-25'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 26 AND 30 THEN '26-30'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 31 AND 35 THEN '31-35'
                    WHEN TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) BETWEEN 36 AND 40 THEN '36-40'
                    ELSE 'Over 40'
                END
            ";
        }
    }

    /**
     * Format revenue type for display.
     */
    private function formatRevenueType($type)
    {
        switch ($type) {
            case 'booking_commission':
                return 'Time Spending Commission';
            case 'subscription_payment':
                return 'Subscription Revenue';
            case 'event_payment':
                return 'Event Revenue';
            case 'couple_event_payment':
                return 'Couple Event Revenue';
            default:
                return ucwords(str_replace('_', ' ', $type));
        }
    }
}
