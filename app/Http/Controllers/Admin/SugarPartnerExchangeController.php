<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Feature;
use App\Models\SugarPartnerExchange;
use App\Models\SugarPartnerHardReject;
use App\Models\SugarPartnerRejection;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SugarPartnerExchangeController extends Controller
{
    /**
     * Display a listing of exchanges.
     */
    public function index(Request $request)
    {
        $query = SugarPartnerExchange::with(['user1', 'user2', 'initiatedByAdmin', 'payments', 'rejections']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user1', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })->orWhereHas('user2', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                });
            });
        }

        $exchanges = $query->orderBy('created_at', 'desc')->paginate(15);

        $statusCounts = [
            'all' => SugarPartnerExchange::count(),
            'pending_payment' => SugarPartnerExchange::where('status', 'pending_payment')->count(),
            'payment_completed' => SugarPartnerExchange::where('status', 'payment_completed')->count(),
            'profiles_viewed' => SugarPartnerExchange::where('status', 'profiles_viewed')->count(),
            'responses_completed' => SugarPartnerExchange::where('status', 'responses_completed')->count(),
            'cancelled' => SugarPartnerExchange::where('status', 'cancelled')->count(),
        ];

        return view('admin.sugar-partners.exchanges.index', compact('exchanges', 'statusCounts'));
    }

    /**
     * Show the form for initiating a new exchange.
     */
    public function create()
    {
        // Get Sugar Partner users
        $sugarPartnerUsers = User::where(function($q) {
            $q->where('interested_in_sugar_partner', true)
              ->orWhereNotNull('sugar_partner_types');
        })->orderBy('name')->get();

        return view('admin.sugar-partners.exchanges.create', compact('sugarPartnerUsers'));
    }

    /**
     * Initiate a new profile exchange.
     */
    public function initiateExchange(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user1_id' => 'required|exists:users,id',
            'user2_id' => 'required|exists:users,id|different:user1_id',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user1Id = $request->user1_id;
        $user2Id = $request->user2_id;

        // Check if exchange can be initiated
        $canInitiate = SugarPartnerExchange::canInitiateExchange($user1Id, $user2Id);

        if (!$canInitiate['can_initiate']) {
            return redirect()->back()
                ->with('error', $canInitiate['message'])
                ->withInput();
        }

        // Get Sugar Partner feature pricing
        $sugarPartnerFeature = Feature::where('name', 'sugar_partner')->first();
        $currency = $sugarPartnerFeature?->options['currency'] ?? 'INR';

        // For display purposes, we'll use a base price (actual pricing is calculated per user)
        $basePrice = 100.00;

        DB::beginTransaction();
        try {
            $exchange = SugarPartnerExchange::create([
                'user1_id' => $user1Id,
                'user2_id' => $user2Id,
                'initiated_by_admin_id' => Auth::id(),
                'exchange_price' => $basePrice, // This is just for reference, actual pricing is calculated per user
                'currency' => $currency,
                'admin_notes' => $request->admin_notes,
            ]);

            // Send notifications to both users
            $this->sendExchangeNotifications($exchange);

            DB::commit();

            return redirect()->route('admin.sugar-partners.exchanges.show', $exchange)
                ->with('success', 'Profile exchange initiated successfully. Both users have been notified.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Failed to initiate exchange: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified exchange.
     */
    public function show(SugarPartnerExchange $exchange)
    {
        $exchange->load([
            'user1', 'user2', 'initiatedByAdmin',
            'payments.user', 'rejections.rejector', 'rejections.rejectedUser'
        ]);

        // Get rejection history between these users
        $rejectionHistory = SugarPartnerRejection::getRejectionHistory(
            $exchange->user1_id,
            $exchange->user2_id
        );

        // Check if there's a hard reject between these users
        $hardReject = SugarPartnerHardReject::getBetweenUsers(
            $exchange->user1_id,
            $exchange->user2_id
        );

        return view('admin.sugar-partners.exchanges.show', compact(
            'exchange',
            'rejectionHistory',
            'hardReject'
        ));
    }

    /**
     * Cancel an exchange.
     */
    public function cancel(Request $request, SugarPartnerExchange $exchange)
    {
        if ($exchange->status === 'cancelled') {
            return redirect()->back()->with('error', 'Exchange is already cancelled.');
        }

        if ($exchange->status === 'responses_completed') {
            return redirect()->back()->with('error', 'Cannot cancel a completed exchange.');
        }

        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        DB::beginTransaction();
        try {
            // Process refunds if payments were made
            $this->processRefunds($exchange, $request->cancellation_reason);

            // Update exchange status
            $exchange->update([
                'status' => 'cancelled',
                'admin_notes' => ($exchange->admin_notes ? $exchange->admin_notes . "\n\n" : '') . 
                               "Cancelled by admin: " . $request->cancellation_reason,
            ]);

            // Send cancellation notifications
            $this->sendCancellationNotifications($exchange, $request->cancellation_reason);

            DB::commit();

            return redirect()->back()->with('success', 'Exchange cancelled successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to cancel exchange: ' . $e->getMessage());
        }
    }

    /**
     * Get exchange data for AJAX requests.
     */
    public function getData(Request $request)
    {
        $query = SugarPartnerExchange::with(['user1', 'user2', 'initiatedByAdmin']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user1', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })->orWhereHas('user2', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                });
            });
        }

        $exchanges = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.sugar-partners.exchanges.table', compact('exchanges'))->render();
    }

    /**
     * Send exchange notifications to both users.
     */
    protected function sendExchangeNotifications(SugarPartnerExchange $exchange): void
    {
        $adminName = $exchange->initiatedByAdmin->name;

        foreach ([$exchange->user1, $exchange->user2] as $user) {
            // Skip notification if user has disabled Sugar Partner notifications
            if ($user->hide_sugar_partner_notifications) {
                continue;
            }

            $otherUser = $user->id === $exchange->user1_id ? $exchange->user2 : $exchange->user1;
            $userPrice = $exchange->getPriceForUser($user->id);

            \App\Models\Notification::create([
                'user_id' => $user->id,
                'type' => 'sugar_partner_exchange_initiated',
                'title' => 'Sugar Partner Profile Exchange',
                'message' => 'Sugar Partner Exchange',
                'body' => "Admin {$adminName} has initiated a profile exchange between you and {$otherUser->name}. Please complete payment of {$exchange->currency} {$userPrice} to proceed.",
                'data' => [
                    'exchange_id' => $exchange->id,
                    'other_user_id' => $otherUser->id,
                    'other_user_name' => $otherUser->name,
                    'user_price' => $userPrice,
                    'currency' => $exchange->currency,
                    'admin_name' => $adminName,
                    'action_url' => route('sugar-partner.exchange.payment', $exchange),
                    'action_text' => 'View'
                ],
            ]);
        }
    }

    /**
     * Send cancellation notifications.
     */
    protected function sendCancellationNotifications(SugarPartnerExchange $exchange, string $reason): void
    {
        foreach ([$exchange->user1, $exchange->user2] as $user) {
            $otherUser = $user->id === $exchange->user1_id ? $exchange->user2 : $exchange->user1;

            \App\Models\Notification::create([
                'user_id' => $user->id,
                'type' => 'sugar_partner_exchange_cancelled',
                'title' => 'Sugar Partner Exchange Cancelled',
                'message' => 'Sugar Partner Exchange',
                'body' => "The profile exchange has been cancelled by an administrator. Reason: {$reason}",
                'data' => [
                    'exchange_id' => $exchange->id,
                    'other_user_id' => $otherUser->id,
                    'cancellation_reason' => $reason,
                ],
            ]);
        }
    }

    /**
     * Process refunds for cancelled exchange.
     */
    protected function processRefunds(SugarPartnerExchange $exchange, string $reason): void
    {
        $completedPayments = $exchange->payments()->where('status', 'completed')->get();

        foreach ($completedPayments as $payment) {
            $payment->processRefund("Exchange cancelled: {$reason}");
        }
    }
}
