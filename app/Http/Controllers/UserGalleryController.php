<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use App\Models\UserGallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UserGalleryController extends Controller
{
    public function upload(Request $request)
    {
        // Check if gallery feature is enabled by admin
        if (!Feature::isEnabled('gallery')) {
            return response()->json([
                'success' => false,
                'message' => 'Gallery feature is not available.'
            ], 403);
        }

        // Check if user has gallery enabled in their privacy settings
        $user = Auth::user();
        if (!$user->show_gallery_images) {
            return response()->json([
                'success' => false,
                'message' => 'Please enable "Show Images" in your privacy settings to upload images.'
            ], 403);
        }

        try {
            \Log::info('Gallery upload started', [
                'user_id' => Auth::id(),
                'has_files' => $request->hasFile('images'),
                'files_count' => $request->hasFile('images') ? count($request->file('images')) : 0
            ]);

            // Check if files were uploaded first
            if (!$request->hasFile('images')) {
                \Log::error('No files in request', [
                    'all_files' => $request->allFiles(),
                    'has_any_files' => $request->hasAnyFile(),
                    'request_method' => $request->method(),
                    'content_type' => $request->header('Content-Type')
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'No files were uploaded. Please select images to upload.'
                ], 400);
            }

            // Simple validation - just check if it's an image
            $files = $request->file('images');
            foreach ($files as $file) {
                if (!$file->isValid()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'One or more files are invalid.'
                    ], 400);
                }

                // Check if it's an image
                $mimeType = $file->getMimeType();
                if (!str_starts_with($mimeType, 'image/')) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Only image files are allowed.'
                    ], 400);
                }
            }

            $user = Auth::user();
            $uploadedImages = [];

            // Get the current highest sort order
            $maxSortOrder = UserGallery::where('user_id', $user->id)->max('sort_order') ?? 0;

            foreach ($files as $image) {
                $filename = Str::uuid() . '.' . $image->getClientOriginalExtension();

                \Log::info('Storing file', [
                    'filename' => $filename,
                    'original_name' => $image->getClientOriginalName(),
                    'size' => $image->getSize()
                ]);

                $path = $image->storeAs('gallery/' . $user->id, $filename, 'public');

                if (!$path) {
                    \Log::error('Failed to store file', ['filename' => $filename]);
                    continue;
                }

                $galleryImage = UserGallery::create([
                    'user_id' => $user->id,
                    'image_path' => $path,
                    'original_name' => $image->getClientOriginalName(),
                    'sort_order' => ++$maxSortOrder,
                ]);

                $uploadedImages[] = [
                    'id' => $galleryImage->id,
                    'url' => $galleryImage->image_url,
                    'sort_order' => $galleryImage->sort_order,
                ];
            }

            \Log::info('Gallery upload completed', [
                'uploaded_count' => count($uploadedImages)
            ]);

            return response()->json([
                'success' => true,
                'images' => $uploadedImages,
                'message' => 'Images uploaded successfully!'
            ]);

        } catch (\Exception $e) {
            \Log::error('Gallery upload error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function reorder(Request $request)
    {
        // Check if gallery feature is enabled by admin
        if (!Feature::isEnabled('gallery')) {
            return response()->json([
                'success' => false,
                'message' => 'Gallery feature is not available.'
            ], 403);
        }

        $request->validate([
            'images' => 'required|array',
            'images.*.id' => 'required|exists:user_gallery,id',
            'images.*.sort_order' => 'required|integer',
        ]);

        $user = Auth::user();

        foreach ($request->images as $imageData) {
            UserGallery::where('id', $imageData['id'])
                ->where('user_id', $user->id)
                ->update(['sort_order' => $imageData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Images reordered successfully!'
        ]);
    }

    public function delete(Request $request, $id)
    {
        // Check if gallery feature is enabled by admin
        if (!Feature::isEnabled('gallery')) {
            return response()->json([
                'success' => false,
                'message' => 'Gallery feature is not available.'
            ], 403);
        }

        $user = Auth::user();
        $galleryImage = UserGallery::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // Delete the file from storage
        if (Storage::disk('public')->exists($galleryImage->image_path)) {
            Storage::disk('public')->delete($galleryImage->image_path);
        }

        // Delete the record
        $galleryImage->delete();

        return response()->json([
            'success' => true,
            'message' => 'Image deleted successfully!'
        ]);
    }
}
