<?php

namespace App\Http\Controllers;

use App\Models\TimeSpendingBooking;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CalendarController extends Controller
{
    /**
     * Display the calendar booking management interface.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if user has time spending enabled
        if (!$user->is_time_spending_enabled) {
            return redirect()->route('home')->with('error', 'Time spending feature is not enabled for your account.');
        }

        // Get current month and year from request or use current date
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        // Validate month and year
        $month = max(1, min(12, (int)$month));
        $year = max(2020, min(2030, (int)$year));

        $currentDate = Carbon::create($year, $month, 1);
        $startOfMonth = $currentDate->copy()->startOfMonth();
        $endOfMonth = $currentDate->copy()->endOfMonth();

        // Get all bookings for the month
        $bookings = TimeSpendingBooking::with(['client'])
            ->where('provider_id', $user->id)
            ->where('payment_status', 'paid')
            ->whereBetween('booking_date', [$startOfMonth, $endOfMonth])
            ->orderBy('booking_date')
            ->get();

        // Group bookings by date
        $bookingsByDate = [];
        foreach ($bookings as $booking) {
            $date = Carbon::parse($booking->booking_date)->format('Y-m-d');
            if (!isset($bookingsByDate[$date])) {
                $bookingsByDate[$date] = [];
            }
            $bookingsByDate[$date][] = $booking;
        }

        // Calculate booking counts for each date
        $bookingCounts = [];
        foreach ($bookingsByDate as $date => $dateBookings) {
            $bookingCounts[$date] = count($dateBookings);
        }

        return view('calendar.index', [
            'currentDate' => $currentDate,
            'bookingCounts' => $bookingCounts,
            'month' => $month,
            'year' => $year
        ]);
    }

    /**
     * Get bookings for a specific date.
     */
    public function getBookingsForDate(Request $request, $date): JsonResponse
    {
        $user = Auth::user();

        // Validate date format
        try {
            $requestedDate = Carbon::createFromFormat('Y-m-d', $date);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid date format.'
            ], 400);
        }

        $startOfDay = $requestedDate->copy()->startOfDay();
        $endOfDay = $requestedDate->copy()->endOfDay();

        // Get all bookings for the specific date
        $bookings = TimeSpendingBooking::with(['client'])
            ->where('provider_id', $user->id)
            ->where('payment_status', 'paid')
            ->whereBetween('booking_date', [$startOfDay, $endOfDay])
            ->orderBy('booking_date')
            ->get();

        // Format bookings for display
        $formattedBookings = $bookings->map(function ($booking) {
            $startTime = Carbon::parse($booking->booking_date);
            $endTime = $startTime->copy()->addHours((float) $booking->duration_hours);

            return [
                'id' => $booking->id,
                'client_name' => $booking->client->name,
                'client_id' => $booking->client->id,
                'client_profile_picture' => $booking->client->profile_picture_url,
                'start_time' => $startTime->format('g:i A'),
                'end_time' => $endTime->format('g:i A'),
                'time_range' => $startTime->format('g:i A') . ' - ' . $endTime->format('g:i A'),
                'duration' => $booking->duration_hours,
                'location' => $booking->meeting_location,
                'client_location' => $booking->client_location,
                'notes' => $booking->notes,
                'total_amount' => $booking->total_amount,
                'hourly_rate' => $booking->hourly_rate,
                'provider_status' => $booking->provider_status,
                'status' => $booking->status,
                'created_at' => $booking->created_at->format('M j, Y g:i A'),
                'booking_date' => $startTime->format('M j, Y'),
                'can_accept' => $booking->provider_status === 'pending',
                'can_reject' => $booking->provider_status === 'pending',
                'can_block' => $booking->provider_status === 'pending',
                'is_accepted' => $booking->provider_status === 'accepted',
                'is_rejected' => $booking->provider_status === 'rejected',
                'is_blocked' => $booking->provider_status === 'blocked'
            ];
        });

        // Group bookings by status
        $groupedBookings = [
            'pending' => $formattedBookings->where('provider_status', 'pending')->values(),
            'accepted' => $formattedBookings->where('provider_status', 'accepted')->values(),
            'rejected' => $formattedBookings->where('provider_status', 'rejected')->values()
        ];

        // Calculate counts for each tab
        $counts = [
            'pending' => $groupedBookings['pending']->count(),
            'accepted' => $groupedBookings['accepted']->count(),
            'rejected' => $groupedBookings['rejected']->count(),
            'total' => $formattedBookings->count()
        ];

        return response()->json([
            'success' => true,
            'date' => $requestedDate->format('M j, Y'),
            'bookings' => $formattedBookings,
            'bookings_by_status' => $groupedBookings,
            'counts' => $counts,
            'total_count' => $formattedBookings->count()
        ]);
    }
}
