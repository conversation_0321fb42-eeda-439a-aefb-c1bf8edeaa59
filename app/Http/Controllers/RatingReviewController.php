<?php

namespace App\Http\Controllers;

use App\Models\RatingReview;
use App\Models\TimeSpendingBooking;
use App\Models\Notification;
use App\Helpers\FeatureHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RatingReviewController extends Controller
{
    /**
     * Show the rating form for a booking.
     */
    public function showRatingForm($bookingId)
    {
        // Check if rating & review system is active
        if (!FeatureHelper::isRatingReviewSystemActive()) {
            return redirect()->route('notifications.index')
                ->with('error', 'Rating & Review system is not available.');
        }

        $booking = TimeSpendingBooking::with(['client', 'provider'])->findOrFail($bookingId);
        $user = Auth::user();

        // Check if user can review this booking
        if (!RatingReview::canReviewBooking($bookingId, $user->id)) {
            return redirect()->route('notifications.index')
                ->with('error', 'You cannot review this booking.');
        }

        $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;

        return view('rating-review.form', compact('booking', 'otherUser'));
    }

    /**
     * Submit a rating and review.
     */
    public function submitRating(Request $request): JsonResponse
    {
        // Check if rating & review system is active
        if (!FeatureHelper::isRatingReviewSystemActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Rating & Review system is not available.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|exists:time_spending_bookings,id',
            'rating' => 'required|integer|min:1|max:5',
            'review_text' => 'nullable|string|max:1000',
            'is_anonymous' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid input data.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = Auth::user();
            $bookingId = $request->booking_id;

            // Check if user can review this booking
            if (!RatingReview::canReviewBooking($bookingId, $user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot review this booking.'
                ], 403);
            }

            // Create the review
            $review = RatingReview::createReview(
                $bookingId,
                $user->id,
                $request->rating,
                $request->review_text,
                $request->boolean('is_anonymous', false)
            );

            if (!$review) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create review. Please try again.'
                ], 500);
            }

            // Get booking details for notification
            $booking = TimeSpendingBooking::with(['client', 'provider'])->find($bookingId);
            $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;

            // Create notification for the reviewed user
            Notification::createReviewReceived(
                $otherUser->id,
                $booking,
                $review,
                $user->name
            );

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your review! Your feedback has been submitted successfully.',
                'review' => [
                    'id' => $review->id,
                    'rating' => $review->rating,
                    'review_text' => $review->review_text,
                    'is_anonymous' => $review->is_anonymous,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit review: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get reviews for a user (for profile display).
     */
    public function getUserReviews($userId): JsonResponse
    {
        try {
            $reviews = RatingReview::approved()
                ->forUser($userId)
                ->with(['reviewer', 'booking'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            // Ensure reviewer data includes profile_picture_url
            $reviews->getCollection()->transform(function ($review) {
                if ($review->reviewer) {
                    // Force load the profile_picture_url accessor
                    $review->reviewer->append('profile_picture_url');
                }
                return $review;
            });

            $averageRating = RatingReview::getAverageRatingForUser($userId);
            $totalReviews = RatingReview::getTotalReviewsForUser($userId);
            $ratingDistribution = RatingReview::getRatingDistributionForUser($userId);

            return response()->json([
                'success' => true,
                'reviews' => $reviews,
                'statistics' => [
                    'average_rating' => $averageRating,
                    'total_reviews' => $totalReviews,
                    'rating_distribution' => $ratingDistribution,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load reviews: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if user can review a booking.
     */
    public function canReview($bookingId): JsonResponse
    {
        try {
            $user = Auth::user();
            $canReview = RatingReview::canReviewBooking($bookingId, $user->id);

            return response()->json([
                'success' => true,
                'can_review' => $canReview,
                'rating_system_active' => FeatureHelper::isRatingReviewSystemActive(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check review eligibility: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pending reviews for a user (bookings that can be reviewed).
     */
    public function getPendingReviews(): JsonResponse
    {
        try {
            if (!FeatureHelper::isRatingReviewSystemActive()) {
                return response()->json([
                    'success' => true,
                    'pending_reviews' => [],
                    'message' => 'Rating & Review system is not active.'
                ]);
            }

            $user = Auth::user();
            
            // Get completed bookings that haven't been reviewed yet
            $completedBookings = TimeSpendingBooking::with(['client', 'provider'])
                ->where(function($query) use ($user) {
                    $query->where('client_id', $user->id)
                          ->orWhere('provider_id', $user->id);
                })
                ->where('provider_status', 'accepted')
                ->where('payment_status', 'paid')
                ->whereRaw('DATE_ADD(booking_date, INTERVAL duration_hours HOUR) < NOW()')
                ->get();

            $pendingReviews = [];
            
            foreach ($completedBookings as $booking) {
                if (RatingReview::canReviewBooking($booking->id, $user->id)) {
                    $otherUser = $booking->client_id === $user->id ? $booking->provider : $booking->client;
                    
                    $pendingReviews[] = [
                        'booking_id' => $booking->id,
                        'other_user' => [
                            'id' => $otherUser->id,
                            'name' => $otherUser->name,
                            'profile_picture_url' => $otherUser->profile_picture_url,
                        ],
                        'booking_date' => $booking->booking_date->format('M d, Y'),
                        'booking_time' => $booking->booking_date->format('h:i A'),
                        'duration' => $booking->duration_hours,
                        'role' => $booking->client_id === $user->id ? 'client' : 'provider',
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'pending_reviews' => $pendingReviews,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get pending reviews: ' . $e->getMessage()
            ], 500);
        }
    }
}
