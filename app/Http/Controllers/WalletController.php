<?php

namespace App\Http\Controllers;

use App\Models\UserWallet;
use App\Models\WalletTransaction;
use App\Models\UserBankAccount;
use App\Models\WithdrawalRequest;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WalletController extends Controller
{
    /**
     * Get user's wallet details.
     */
    public function getWallet(): JsonResponse
    {
        $user = Auth::user();
        $wallet = UserWallet::getOrCreate($user->id);
        
        $recentTransactions = WalletTransaction::with(['booking.client', 'booking.provider'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'wallet' => $wallet,
            'recent_transactions' => $recentTransactions
        ]);
    }

    /**
     * Get wallet transactions with pagination.
     */
    public function getTransactions(Request $request): JsonResponse
    {
        $user = Auth::user();
        $perPage = $request->input('per_page', 20);
        
        $transactions = WalletTransaction::with(['booking.client', 'booking.provider'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'transactions' => $transactions
        ]);
    }

    /**
     * Check if user can afford a booking amount.
     */
    public function checkAffordability(Request $request): JsonResponse
    {
        $user = Auth::user();
        $amount = (float) $request->input('amount', 0);
        
        $wallet = UserWallet::getOrCreate($user->id);
        
        $canAfford = $wallet->balance >= $amount;
        $shortfall = $canAfford ? 0 : $amount - $wallet->balance;

        return response()->json([
            'success' => true,
            'can_afford' => $canAfford,
            'wallet_balance' => $wallet->balance,
            'required_amount' => $amount,
            'shortfall' => $shortfall,
            'message' => $canAfford 
                ? 'You have sufficient balance in your wallet.' 
                : "You need ₹{$shortfall} more to complete this booking."
        ]);
    }

    /**
     * Use wallet balance for booking payment.
     */
    public function useWalletForBooking(Request $request): JsonResponse
    {
        $user = Auth::user();
        $bookingAmount = (float) $request->input('booking_amount', 0);
        
        $wallet = UserWallet::getOrCreate($user->id);
        
        if ($wallet->balance < $bookingAmount) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient wallet balance.',
                'wallet_balance' => $wallet->balance,
                'required_amount' => $bookingAmount,
                'shortfall' => $bookingAmount - $wallet->balance
            ], 400);
        }

        try {
            // Deduct amount from wallet
            $transaction = $wallet->deductMoney(
                $bookingAmount,
                'Payment for time spending booking',
                null,
                ['payment_method' => 'wallet']
            );

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to deduct amount from wallet.'
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment successful using wallet balance.',
                'transaction' => $transaction,
                'remaining_balance' => $wallet->fresh()->balance
            ]);

        } catch (\Exception $e) {
            \Log::error('Wallet payment failed:', [
                'user_id' => $user->id,
                'amount' => $bookingAmount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Wallet payment failed. Please try again.'
            ], 500);
        }
    }

    /**
     * Get wallet balance for display.
     */
    public function getBalance(): JsonResponse
    {
        $user = Auth::user();
        $wallet = UserWallet::getOrCreate($user->id);

        return response()->json([
            'success' => true,
            'balance' => $wallet->balance,
            'formatted_balance' => '₹' . number_format($wallet->balance, 2),
            'total_earned' => $wallet->total_earned,
            'total_withdrawn' => $wallet->total_withdrawn
        ]);
    }

    /**
     * Show wallet page.
     */
    public function index()
    {
        $user = Auth::user();
        $wallet = UserWallet::getOrCreate($user->id);
        $bankAccounts = UserBankAccount::where('user_id', $user->id)->get();
        $withdrawalRequests = WithdrawalRequest::where('user_id', $user->id)
                                              ->with('bankAccount')
                                              ->orderBy('requested_at', 'desc')
                                              ->paginate(10);

        // Get all transactions for display (wallet + event payments + time spending)
        $walletTransactions = WalletTransaction::with(['booking.client', 'booking.provider'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get event payments
        $eventPayments = $user->eventPayments()
            ->with('meetingAddress')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get time spending payments (as client)
        $timeSpendingPayments = $user->timeSpendingBookingsAsClient()
            ->with('provider')
            ->where('payment_status', 'paid')
            ->orderBy('paid_at', 'desc')
            ->get();

        // Get time spending earnings (as provider) - only show released payments
        $timeSpendingEarnings = $user->providerBookings()
            ->with('client')
            ->where('payment_status', 'paid')
            ->where('provider_status', 'accepted')
            ->where('escrow_status', 'released') // Only show payments that have been released from escrow
            ->orderBy('escrow_released_at', 'desc') // Order by when payment was actually released
            ->get();

        // Get withdrawal settings
        $minWithdrawal = (float) Setting::get('minimum_withdrawal_amount', 100);
        $maxWithdrawal = (float) Setting::get('maximum_withdrawal_amount', 50000);
        $dailyLimit = (float) Setting::get('daily_withdrawal_limit', 100000);
        $processingFee = (float) Setting::get('withdrawal_processing_fee', 5);
        $processingTime = Setting::get('withdrawal_processing_time', '1-3 business days');

        // Calculate today's withdrawal total
        $todayWithdrawal = WithdrawalRequest::getDailyWithdrawalTotal($user->id);
        $remainingDailyLimit = max(0, $dailyLimit - $todayWithdrawal);

        // Calculate spending summary data
        $totalSpending = $timeSpendingPayments->sum('total_amount') + $eventPayments->sum('amount_paid');
        $totalEarnings = $wallet->total_earned;
        $totalTransactions = $walletTransactions->count() + $eventPayments->count() + $timeSpendingPayments->count() + $timeSpendingEarnings->count();

        return view('wallet.index', compact(
            'wallet',
            'bankAccounts',
            'withdrawalRequests',
            'walletTransactions',
            'eventPayments',
            'timeSpendingPayments',
            'timeSpendingEarnings',
            'minWithdrawal',
            'maxWithdrawal',
            'dailyLimit',
            'processingFee',
            'processingTime',
            'todayWithdrawal',
            'remainingDailyLimit',
            'totalSpending',
            'totalEarnings',
            'totalTransactions'
        ));
    }

    /**
     * Add bank account.
     */
    public function addBankAccount(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $paymentType = $request->input('payment_method_type', 'bank');

            if ($paymentType === 'bank') {
                // Check if user already has a bank account
                $existingBankAccount = UserBankAccount::where('user_id', $user->id)
                                                     ->where('payment_method_type', 'bank')
                                                     ->first();

                if ($existingBankAccount) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You can only add one bank account. Please delete the existing one to add a new one.'
                    ], 422);
                }

                $validator = Validator::make($request->all(), [
                    'account_holder_name' => 'required|string|max:255',
                    'account_number' => 'required|string|min:9|max:20',
                    'confirm_account_number' => 'required|same:account_number',
                    'ifsc_code' => 'required|string|size:11',
                    'account_type' => 'required|in:savings,current',
                ]);

                if ($validator->fails()) {
                    Log::info('Bank account validation failed:', [
                        'errors' => $validator->errors(),
                        'request_data' => $request->all(),
                        'user_id' => Auth::id(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid input data.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                $ifscCode = strtoupper($request->ifsc_code);

                // Validate IFSC code format
                if (!UserBankAccount::validateIfscCode($ifscCode)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid IFSC code format.'
                    ], 422);
                }

                // Get bank name from IFSC
                $bankName = UserBankAccount::getBankNameFromIfsc($ifscCode) ?: 'Unknown Bank';

                // Create bank account
                $bankAccount = UserBankAccount::create([
                    'user_id' => $user->id,
                    'account_holder_name' => $request->account_holder_name,
                    'account_number' => $request->account_number,
                    'ifsc_code' => $ifscCode,
                    'bank_name' => $bankName,
                    'account_type' => $request->account_type,
                    'payment_method_type' => 'bank',
                    'is_verified' => true, // Auto-verify for now
                    'is_primary' => UserBankAccount::where('user_id', $user->id)->count() === 0,
                ]);

                $message = 'Bank account added successfully.';

            } else { // G-Pay
                // Check if user already has a G-Pay account
                $existingGpayAccount = UserBankAccount::where('user_id', $user->id)
                                                     ->where('payment_method_type', 'gpay')
                                                     ->first();

                if ($existingGpayAccount) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You can only add one G-Pay account. Please delete the existing one to add a new one.'
                    ], 422);
                }

                $validator = Validator::make($request->all(), [
                    'gpay_name' => 'required|string|max:255',
                    'gpay_number' => 'required|string|size:10|regex:/^[0-9]{10}$/',
                    'gpay_upi' => 'required|string|max:255|regex:/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/',
                ]);

                if ($validator->fails()) {
                    Log::info('G-Pay account validation failed:', [
                        'errors' => $validator->errors(),
                        'request_data' => $request->all(),
                        'user_id' => Auth::id(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid input data.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                // Create G-Pay account
                $bankAccount = UserBankAccount::create([
                    'user_id' => $user->id,
                    'account_holder_name' => $request->gpay_name,
                    'gpay_number' => $request->gpay_number,
                    'gpay_upi' => $request->gpay_upi,
                    'payment_method_type' => 'gpay',
                    'account_type' => 'gpay',
                    'is_verified' => true, // G-Pay accounts are auto-verified
                    'is_primary' => UserBankAccount::where('user_id', $user->id)->count() === 0,
                ]);

                $message = 'G-Pay account added successfully.';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'bank_account' => [
                    'id' => $bankAccount->id,
                    'display_name' => $bankAccount->display_name,
                    'is_verified' => $bankAccount->is_verified,
                    'is_primary' => $bankAccount->is_primary,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error adding payment method:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding payment method.'
            ], 500);
        }
    }

    /**
     * Update bank account.
     */
    public function updateBankAccount(Request $request, $accountId): JsonResponse
    {
        try {
            $user = Auth::user();
            $bankAccount = UserBankAccount::where('id', $accountId)
                                         ->where('user_id', $user->id)
                                         ->first();

            if (!$bankAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found.'
                ], 404);
            }

            $paymentType = $request->input('payment_method_type');

            if ($paymentType === 'bank') {
                $validator = Validator::make($request->all(), [
                    'account_holder_name' => 'required|string|max:255',
                    'account_number' => 'required|string|min:9|max:20',
                    'confirm_account_number' => 'required|same:account_number',
                    'ifsc_code' => 'required|string|size:11',
                    'account_type' => 'required|in:savings,current',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid input data.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                // Validate IFSC code format
                $ifscCode = strtoupper($request->ifsc_code);
                if (!UserBankAccount::validateIfscCode($ifscCode)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid IFSC code format.'
                    ], 422);
                }

                // Get bank name from IFSC
                $bankName = UserBankAccount::getBankNameFromIfsc($ifscCode) ?: 'Unknown Bank';

                // Update bank account
                $bankAccount->update([
                    'account_holder_name' => $request->account_holder_name,
                    'account_number' => $request->account_number,
                    'ifsc_code' => $ifscCode,
                    'bank_name' => $bankName,
                    'account_type' => $request->account_type,
                ]);

                $message = 'Bank account updated successfully.';

            } else { // G-Pay
                $validator = Validator::make($request->all(), [
                    'gpay_name' => 'required|string|max:255',
                    'gpay_number' => 'required|string|size:10|regex:/^[0-9]{10}$/',
                    'gpay_upi' => 'required|string|max:255|regex:/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid input data.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                // Update G-Pay account
                $bankAccount->update([
                    'account_holder_name' => $request->gpay_name,
                    'gpay_number' => $request->gpay_number,
                    'gpay_upi' => $request->gpay_upi,
                ]);

                $message = 'G-Pay account updated successfully.';
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating payment method:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'account_id' => $accountId,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating payment method.'
            ], 500);
        }
    }

    /**
     * Delete bank account.
     */
    public function deleteBankAccount($accountId): JsonResponse
    {
        try {
            $user = Auth::user();
            $bankAccount = UserBankAccount::where('id', $accountId)
                                         ->where('user_id', $user->id)
                                         ->first();

            if (!$bankAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found.'
                ], 404);
            }

            // Check if there are pending withdrawals for this account
            $pendingWithdrawals = WithdrawalRequest::where('bank_account_id', $accountId)
                                                  ->whereIn('status', ['pending', 'processing'])
                                                  ->count();

            if ($pendingWithdrawals > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete payment method with pending withdrawals.'
                ], 422);
            }

            $bankAccount->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting payment method:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'account_id' => $accountId,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting payment method.'
            ], 500);
        }
    }

    /**
     * Request withdrawal.
     */
    public function requestWithdrawal(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $wallet = UserWallet::getOrCreate($user->id);

            // Get withdrawal settings
            $minWithdrawal = (float) Setting::get('minimum_withdrawal_amount', 100);
            $maxWithdrawal = (float) Setting::get('maximum_withdrawal_amount', 50000);
            $dailyLimit = (float) Setting::get('daily_withdrawal_limit', 100000);
            $processingFee = (float) Setting::get('withdrawal_processing_fee', 5);

            $validator = Validator::make($request->all(), [
                'amount' => "required|numeric|min:{$minWithdrawal}|max:{$maxWithdrawal}",
                'bank_account_id' => 'required|exists:user_bank_accounts,id',
                'notes' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid input data.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $amount = (float) $request->amount;

            // Check wallet balance
            if ($wallet->balance < $amount) {
                return response()->json([
                    'success' => false,
                    'message' => "Insufficient balance. Your current balance is ₹{$wallet->balance}."
                ], 422);
            }

            // Check daily limit
            $todayWithdrawal = WithdrawalRequest::getDailyWithdrawalTotal($user->id);
            if (($todayWithdrawal + $amount) > $dailyLimit) {
                $remaining = $dailyLimit - $todayWithdrawal;
                return response()->json([
                    'success' => false,
                    'message' => "Daily withdrawal limit exceeded. You can withdraw up to ₹{$remaining} more today."
                ], 422);
            }

            // Verify bank account belongs to user
            $bankAccount = UserBankAccount::where('id', $request->bank_account_id)
                                         ->where('user_id', $user->id)
                                         ->first();

            if (!$bankAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid bank account selected.'
                ], 422);
            }

            DB::beginTransaction();

            try {
                // Calculate net amount after processing fee
                $netAmount = $amount - $processingFee;

                // Create withdrawal request (DO NOT deduct money yet - wait for admin approval)
                $withdrawal = WithdrawalRequest::create([
                    'user_id' => $user->id,
                    'bank_account_id' => $bankAccount->id,
                    'amount' => $amount,
                    'processing_fee' => $processingFee,
                    'net_amount' => $netAmount,
                    'status' => 'pending',
                    'notes' => $request->notes,
                    'requested_at' => now(),
                ]);

                // Create notification for user only
                \App\Models\Notification::create([
                    'user_id' => $user->id,
                    'type' => 'withdrawal_requested',
                    'title' => 'Withdrawal Request Submitted',
                    'message' => "Your withdrawal request of ₹{$amount} has been submitted to admin.",
                    'body' => "Your withdrawal request of ₹{$amount} has been submitted to admin.",
                    'data' => [
                        'withdrawal_id' => $withdrawal->id,
                        'amount' => $amount,
                        'net_amount' => $netAmount,
                        'bank_account' => $bankAccount->display_name,
                    ],
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => "Withdrawal request submitted successfully. Amount will be deducted from your wallet once approved by admin. You will receive ₹{$netAmount} after processing fee.",
                    'withdrawal_id' => $withdrawal->id,
                    'processing_time' => $withdrawal->getProcessingTimeEstimate(),
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Error requesting withdrawal:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'amount' => $request->amount ?? null,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing withdrawal request.'
            ], 500);
        }
    }

    /**
     * Cancel withdrawal request.
     */
    public function cancelWithdrawal($withdrawalId): JsonResponse
    {
        try {
            $user = Auth::user();
            $withdrawal = WithdrawalRequest::where('id', $withdrawalId)
                                          ->where('user_id', $user->id)
                                          ->first();

            if (!$withdrawal) {
                return response()->json([
                    'success' => false,
                    'message' => 'Withdrawal request not found.'
                ], 404);
            }

            if (!$withdrawal->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This withdrawal request cannot be cancelled.'
                ], 422);
            }

            $withdrawal->cancel('Cancelled by user');

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal request cancelled successfully. Amount refunded to wallet.'
            ]);

        } catch (\Exception $e) {
            Log::error('Error cancelling withdrawal:', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'withdrawal_id' => $withdrawalId,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while cancelling withdrawal.'
            ], 500);
        }
    }

    /**
     * Get withdrawal settings for frontend.
     */
    public function getWithdrawalSettings(): JsonResponse
    {
        $user = Auth::user();
        $wallet = UserWallet::getOrCreate($user->id);

        $settings = [
            'minimum_withdrawal_amount' => (float) Setting::get('minimum_withdrawal_amount', 100),
            'maximum_withdrawal_amount' => (float) Setting::get('maximum_withdrawal_amount', 50000),
            'daily_withdrawal_limit' => (float) Setting::get('daily_withdrawal_limit', 100000),
            'processing_fee' => (float) Setting::get('withdrawal_processing_fee', 5),
            'processing_time' => Setting::get('withdrawal_processing_time', '1-3 business days'),
            'current_balance' => $wallet->balance,
            'today_withdrawal_total' => WithdrawalRequest::getDailyWithdrawalTotal($user->id),
        ];

        $settings['remaining_daily_limit'] = max(0, $settings['daily_withdrawal_limit'] - $settings['today_withdrawal_total']);
        $settings['can_withdraw'] = $wallet->balance >= $settings['minimum_withdrawal_amount'];

        return response()->json([
            'success' => true,
            'settings' => $settings
        ]);
    }
}
