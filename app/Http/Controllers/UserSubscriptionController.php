<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\UserWallet;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Razorpay\Api\Api;

class UserSubscriptionController extends Controller
{
    /**
     * Get available subscription plans.
     */
    public function getPlans(): JsonResponse
    {
        $plans = SubscriptionPlan::getActivePlans();
        
        return response()->json([
            'success' => true,
            'plans' => $plans
        ]);
    }

    /**
     * Get user's current subscription status.
     */
    public function getStatus(): JsonResponse
    {
        $user = Auth::user();
        $activeSubscription = UserSubscription::getActiveSubscription($user->id);
        
        return response()->json([
            'success' => true,
            'has_active_subscription' => $user->hasActiveTimeSpendingSubscription(),
            'subscription' => $activeSubscription ? [
                'id' => $activeSubscription->id,
                'plan_name' => $activeSubscription->subscriptionPlan->name,
                'expires_at' => $activeSubscription->formatted_expiry_date,
                'days_remaining' => $activeSubscription->days_remaining,
                'amount_paid' => $activeSubscription->amount_paid,
            ] : null
        ]);
    }

    /**
     * Purchase a subscription plan.
     */
    public function purchase(Request $request): JsonResponse
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::findOrFail($request->plan_id);

        if (!$plan->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'This subscription plan is not available.'
            ], 400);
        }

        // Check if user already has an active subscription
        $existingSubscription = UserSubscription::getActiveSubscription($user->id);
        if ($existingSubscription) {
            // Allow queuing a new subscription to start after current one expires
            $queuedSubscription = UserSubscription::getQueuedSubscription($user->id);
            if ($queuedSubscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a queued subscription. Please wait for it to activate or contact support.'
                ], 400);
            }

            // Create queued subscription instead of active one
            $isQueued = true;
        } else {
            $isQueued = false;
        }

        try {
            // Get user wallet
            $userWallet = UserWallet::getOrCreate($user->id);
            $walletBalance = $userWallet->balance;
            $totalAmount = $plan->effective_price; // Use effective price (discount price if available)

            // Calculate payment split
            $walletUsage = min($walletBalance, $totalAmount);
            $razorpayAmount = $totalAmount - $walletUsage;

            $razorpayKey = Setting::get('razorpay_key_id');
            $razorpaySecret = Setting::get('razorpay_key_secret');

            $razorpayOrder = null;

            // Create Razorpay order if needed
            if ($razorpayAmount > 0) {
                $api = new Api($razorpayKey, $razorpaySecret);

                $orderData = [
                    'receipt' => 'subscription_' . $plan->id . '_' . time(),
                    'amount' => $razorpayAmount * 100, // Amount in paise
                    'currency' => 'INR',
                    'notes' => [
                        'plan_id' => $plan->id,
                        'user_id' => $user->id,
                        'wallet_used' => $walletUsage,
                        'razorpay_amount' => $razorpayAmount,
                        'type' => 'subscription_purchase'
                    ]
                ];

                $razorpayOrder = $api->order->create($orderData);
            }

            return response()->json([
                'success' => true,
                'payment_required' => $razorpayAmount > 0,
                'wallet_usage' => $walletUsage,
                'razorpay_amount' => $razorpayAmount,
                'total_amount' => $totalAmount,
                'razorpay_key' => $razorpayKey,
                'razorpay_order_id' => $razorpayOrder ? $razorpayOrder['id'] : null,
                'is_queued' => $isQueued,
                'queue_message' => $isQueued ? 'This subscription will be queued and activate when your current subscription expires.' : null,
                'plan' => [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'duration_text' => $plan->duration_text,
                    'amount' => $plan->amount,
                    'formatted_amount' => $plan->formatted_amount,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Subscription purchase preparation failed:', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to prepare subscription purchase. Please try again.'
            ], 500);
        }
    }

    /**
     * Process subscription payment.
     */
    public function processPayment(Request $request): JsonResponse
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'razorpay_payment_id' => 'nullable|string',
            'razorpay_order_id' => 'nullable|string',
            'razorpay_signature' => 'nullable|string',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::findOrFail($request->plan_id);

        try {
            // Get user wallet
            $userWallet = UserWallet::getOrCreate($user->id);
            $walletBalance = $userWallet->balance;
            $totalAmount = $plan->effective_price; // Use effective price (discount price if available)

            // Calculate payment split
            $walletUsage = min($walletBalance, $totalAmount);
            $razorpayAmount = $totalAmount - $walletUsage;

            // Determine payment method
            $paymentMethod = 'wallet';
            if ($razorpayAmount > 0 && $walletUsage > 0) {
                $paymentMethod = 'mixed';
            } elseif ($razorpayAmount > 0) {
                $paymentMethod = 'razorpay';
            }

            // Deduct from wallet if needed
            if ($walletUsage > 0) {
                $walletTransaction = $userWallet->deductMoney(
                    $walletUsage,
                    "Time Spending Subscription - {$plan->name}",
                    null,
                    ['subscription_plan_id' => $plan->id, 'payment_method' => 'wallet']
                );

                if (!$walletTransaction) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Insufficient wallet balance.'
                    ], 400);
                }
            }

            // Check if this should be a queued subscription
            $existingSubscription = UserSubscription::getActiveSubscription($user->id);
            $isQueued = $existingSubscription !== null;

            // Create subscription
            $subscription = UserSubscription::createSubscription(
                $user->id,
                $plan->id,
                $totalAmount,
                $walletUsage,
                $razorpayAmount,
                $paymentMethod,
                [
                    'payment_id' => $request->razorpay_payment_id,
                    'order_id' => $request->razorpay_order_id,
                    'signature' => $request->razorpay_signature,
                ],
                $isQueued
            );

            // Create admin revenue record for subscription
            \App\Models\AdminRevenue::createFromSubscription($subscription);

            // Update user subscription status (only for active subscriptions)
            if (!$isQueued) {
                $user->updateSubscriptionStatus();
            }

            $message = $isQueued
                ? 'Subscription queued successfully! It will activate when your current subscription expires.'
                : 'Subscription activated successfully!';

            return response()->json([
                'success' => true,
                'message' => $message,
                'is_queued' => $isQueued,
                'subscription' => [
                    'id' => $subscription->id,
                    'plan_name' => $subscription->subscriptionPlan->name,
                    'status' => $subscription->status,
                    'starts_at' => $subscription->starts_at->format('M d, Y'),
                    'expires_at' => $subscription->formatted_expiry_date,
                    'days_remaining' => $subscription->days_remaining,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Subscription payment processing failed:', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed. Please try again.'
            ], 500);
        }
    }
}
