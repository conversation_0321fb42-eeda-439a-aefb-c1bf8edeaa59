<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Setting;
use Carbon\Carbon;

class SiteModeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip middleware for admin routes and admin users
        if ($request->is('admin/*') || (auth()->check() && auth()->user()->isAdmin())) {
            return $next($request);
        }

        // Skip middleware for maintenance and coming soon pages themselves
        if ($request->is('maintenance') || $request->is('coming-soon')) {
            return $next($request);
        }

        // Check if maintenance mode is enabled
        $maintenanceModeEnabled = Setting::get('maintenance_mode_enabled', '0') === '1';
        $comingSoonModeEnabled = Setting::get('coming_soon_mode_enabled', '0') === '1';

        // Auto-disable modes if time has passed
        $this->autoDisableModes();

        // Re-check after potential auto-disable
        $maintenanceModeEnabled = Setting::get('maintenance_mode_enabled', '0') === '1';
        $comingSoonModeEnabled = Setting::get('coming_soon_mode_enabled', '0') === '1';

        // Maintenance mode takes priority over coming soon mode
        if ($maintenanceModeEnabled) {
            return $this->redirectToMaintenance($request);
        }

        if ($comingSoonModeEnabled) {
            return $this->redirectToComingSoon($request);
        }

        return $next($request);
    }

    /**
     * Auto-disable modes if their scheduled time has passed
     */
    private function autoDisableModes(): void
    {
        // Check maintenance mode
        $maintenanceModeEnabled = Setting::get('maintenance_mode_enabled', '0') === '1';
        if ($maintenanceModeEnabled) {
            $endTime = Setting::get('maintenance_mode_end_time');
            if ($endTime && Carbon::parse($endTime)->isPast()) {
                Setting::set('maintenance_mode_enabled', '0');
            }
        }

        // Check coming soon mode
        $comingSoonModeEnabled = Setting::get('coming_soon_mode_enabled', '0') === '1';
        if ($comingSoonModeEnabled) {
            $launchTime = Setting::get('coming_soon_launch_time');
            if ($launchTime && Carbon::parse($launchTime)->isPast()) {
                Setting::set('coming_soon_mode_enabled', '0');
            }
        }
    }

    /**
     * Redirect to maintenance page
     */
    private function redirectToMaintenance(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Site is currently under maintenance.',
                'maintenance_mode' => true
            ], 503);
        }

        return response()->view('site-modes.maintenance', [], 503);
    }

    /**
     * Redirect to coming soon page
     */
    private function redirectToComingSoon(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Site is coming soon.',
                'coming_soon_mode' => true,
                'launch_time' => Setting::get('coming_soon_launch_time')
            ], 503);
        }

        return response()->view('site-modes.coming-soon', [], 503);
    }
}
