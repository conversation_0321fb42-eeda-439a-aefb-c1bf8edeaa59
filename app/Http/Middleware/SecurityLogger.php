<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class SecurityLogger
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log suspicious activities
        $this->logSuspiciousActivity($request);
        
        $response = $next($request);
        
        // Log security events
        $this->logSecurityEvents($request, $response);
        
        return $response;
    }

    /**
     * Log suspicious activities.
     */
    protected function logSuspiciousActivity(Request $request): void
    {
        // Only log critical security threats in production
        $criticalPatterns = [
            'sql injection' => ['union select', 'drop table', 'delete from'],
            'command injection' => ['rm -rf', 'wget', 'curl']
        ];

        $userInput = array_merge(
            $request->all(),
            [$request->getPathInfo()]
        );

        foreach ($criticalPatterns as $type => $patterns) {
            foreach ($patterns as $pattern) {
                foreach ($userInput as $input) {
                    if (is_string($input) && stripos($input, $pattern) !== false) {
                        Log::channel('security')->critical("Critical {$type} attempt detected", [
                            'ip' => $request->ip(),
                            'url' => $request->fullUrl(),
                            'user_id' => auth()->id(),
                        ]);
                        break 3;
                    }
                }
            }
        }
    }

    /**
     * Log security events.
     */
    protected function logSecurityEvents(Request $request, Response $response): void
    {
        // Only log critical security events in production

        // Log multiple failed authentication attempts (potential brute force)
        if ($response->getStatusCode() === 401) {
            $ip = $request->ip();
            $cacheKey = "failed_auth_{$ip}";
            $attempts = cache()->increment($cacheKey, 1);
            cache()->put($cacheKey, $attempts, now()->addMinutes(15));

            if ($attempts >= 5) {
                Log::channel('security')->warning('Multiple authentication failures', [
                    'ip' => $ip,
                    'attempts' => $attempts,
                ]);
            }
        }

        // Log admin access only for sensitive operations
        if ($request->is('admin/*') && auth()->check() && in_array($request->method(), ['POST', 'PUT', 'DELETE'])) {
            Log::channel('security')->info('Admin action', [
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'action' => $request->method() . ' ' . $request->getPathInfo(),
            ]);
        }
    }
}
