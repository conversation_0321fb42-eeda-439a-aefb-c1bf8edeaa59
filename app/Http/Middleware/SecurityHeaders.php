<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Content Security Policy - Updated for Tailwind CDN and Bootstrap CDN compatibility
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://code.jquery.com https://fonts.googleapis.com https://checkout.razorpay.com https://apis.google.com https://cdn.jsdelivr.net; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://unpkg.com https://cdn.jsdelivr.net; " .
               "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; " .
               "img-src 'self' data: https: blob:; " .
               "connect-src 'self' https://api.razorpay.com https://lumberjack.razorpay.com https://accounts.google.com https://nominatim.openstreetmap.org https://api.bigdatacloud.net; " .
               "frame-src 'self' https://checkout.razorpay.com https://api.razorpay.com https://accounts.google.com; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self';";

        // Only add upgrade-insecure-requests in production with HTTPS
        if (app()->environment('production') && $request->isSecure()) {
            $csp .= " upgrade-insecure-requests;";
        }

        $response->headers->set('Content-Security-Policy', $csp);
        
        // Security Headers
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(self), microphone=(), camera=(self), payment=(self "https://checkout.razorpay.com" "https://api.razorpay.com")');
        
        // HSTS (only in production with HTTPS)
        if (app()->environment('production') && $request->isSecure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
        
        // Remove server information
        $response->headers->remove('Server');
        $response->headers->remove('X-Powered-By');
        
        return $response;
    }
}
