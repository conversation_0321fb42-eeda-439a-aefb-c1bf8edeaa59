<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Setting;

class CustomAdminUrlMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $customAdminUrl = Setting::get('custom_admin_url', '');

            // If no custom admin URL is set, allow normal admin access
            if (empty($customAdminUrl)) {
                return $next($request);
            }

            // Clean and validate the custom admin URL
            $customAdminUrl = trim($customAdminUrl, '/');

            // Check if the request is for the default admin routes
            if ($request->is('admin') || $request->is('admin/*')) {
                // If custom admin URL is set and user is trying to access default admin routes,
                // redirect to home page for security
                return redirect()->route('home');
            }

            return $next($request);

        } catch (\Exception $e) {
            // If there's any error (like database not available), allow normal access
            return $next($request);
        }
    }
}
