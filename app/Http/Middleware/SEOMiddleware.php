<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class SEOMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Add canonical URL
        $canonicalUrl = $request->url();
        View::share('canonicalUrl', $canonicalUrl);

        // Add Open Graph and Twitter Card data
        $this->addSocialMetaTags($request);

        // Add structured data
        $this->addStructuredData($request);

        return $response;
    }

    /**
     * Add social media meta tags.
     */
    protected function addSocialMetaTags(Request $request): void
    {
        $defaultTitle = config('app.name', 'SettingWala') . ' - Find Your Perfect Match';
        $defaultDescription = 'Connect with like-minded individuals and discover meaningful relationships through verified profiles and local events.';
        $defaultImage = asset('images/og-image.png');

        View::share('ogData', [
            'title' => $defaultTitle,
            'description' => $defaultDescription,
            'image' => $defaultImage,
            'url' => $request->url(),
            'type' => 'website',
            'site_name' => config('app.name', 'SettingWala'),
        ]);

        View::share('twitterData', [
            'card' => 'summary_large_image',
            'title' => $defaultTitle,
            'description' => $defaultDescription,
            'image' => $defaultImage,
        ]);
    }

    /**
     * Add structured data.
     */
    protected function addStructuredData(Request $request): void
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'WebApplication',
            'name' => config('app.name', 'SettingWala'),
            'description' => 'Premium dating platform for meaningful connections through verified profiles and local events.',
            'url' => config('app.url'),
            'applicationCategory' => 'SocialNetworkingApplication',
            'operatingSystem' => 'Web, iOS, Android',
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'INR',
                'availability' => 'https://schema.org/InStock',
            ],
            'author' => [
                '@type' => 'Organization',
                'name' => config('app.name', 'SettingWala'),
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => config('app.name', 'SettingWala'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png'),
                ],
            ],
        ];

        View::share('structuredData', json_encode($structuredData, JSON_UNESCAPED_SLASHES));
    }
}
