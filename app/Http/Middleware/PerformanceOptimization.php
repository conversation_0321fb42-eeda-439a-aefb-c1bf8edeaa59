<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PerformanceOptimization
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Add performance headers
        $this->addPerformanceHeaders($response);
        
        // Enable compression
        $this->enableCompression($response);
        
        // Add caching headers
        $this->addCachingHeaders($request, $response);

        return $response;
    }

    /**
     * Add performance-related headers.
     */
    protected function addPerformanceHeaders(Response $response): void
    {
        // DNS prefetch for external resources
        $response->headers->set('X-DNS-Prefetch-Control', 'on');

        // Only add preload headers for HTML responses to avoid unnecessary preloading
        $contentType = $response->headers->get('Content-Type', '');
        if (!str_contains($contentType, 'text/html')) {
            return;
        }

        // Get actual asset paths from Vite manifest
        $preloadHeaders = [
            '<https://fonts.googleapis.com>; rel=preconnect',
            '<https://fonts.gstatic.com>; rel=preconnect; crossorigin',
        ];

        // Preload critical assets for shared hosting (CDN + custom files)
        $request = request();
        if ($request && $request->is('/') && !$request->is('api/*') && !$request->is('css/*') && !$request->is('js/*') && !$request->is('images/*') && !$request->is('admin/*')) {
            // Preload custom CSS and critical CDN assets
            $preloadHeaders[] = '</css/app.css>; rel=preload; as=style';
            $preloadHeaders[] = '</js/app.js>; rel=preload; as=script';
            $preloadHeaders[] = '<https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap>; rel=preload; as=style; crossorigin=anonymous';
            $preloadHeaders[] = '<https://cdn.tailwindcss.com>; rel=preload; as=script';
        }

        // Only set preload headers for main app, not admin panel
        if (count($preloadHeaders) > 2 && !$request->is('admin/*')) {
            $response->headers->set('Link', implode(', ', $preloadHeaders));
        }
    }

    /**
     * Enable compression.
     */
    protected function enableCompression(Response $response): void
    {
        if (!$response->headers->has('Content-Encoding')) {
            $response->headers->set('Vary', 'Accept-Encoding');
        }
    }

    /**
     * Add caching headers.
     */
    protected function addCachingHeaders(Request $request, Response $response): void
    {
        // Cache static assets
        if ($this->isStaticAsset($request)) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
            $response->headers->set('Expires', now()->addYear()->toRfc7231String());
        }
        
        // Cache images
        if ($this->isImage($request)) {
            $response->headers->set('Cache-Control', 'public, max-age=2592000'); // 30 days
        }
        
        // Cache API responses
        if ($request->is('api/*')) {
            $response->headers->set('Cache-Control', 'public, max-age=300'); // 5 minutes
        }
    }

    /**
     * Check if request is for static asset.
     */
    protected function isStaticAsset(Request $request): bool
    {
        return $request->is('css/*') ||
               $request->is('js/*') ||
               preg_match('/\.(css|js|woff|woff2|ttf|eot)$/', $request->path());
    }

    /**
     * Check if request is for image.
     */
    protected function isImage(Request $request): bool
    {
        return $request->is('images/*') || 
               $request->is('storage/*') ||
               preg_match('/\.(jpg|jpeg|png|gif|webp|svg|ico)$/', $request->path());
    }
}
