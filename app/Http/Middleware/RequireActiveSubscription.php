<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RequireActiveSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // Skip check if user is not authenticated
        if (!$user) {
            return $next($request);
        }

        // Skip check if user doesn't have Time Spending enabled
        if (!$user->is_time_spending_enabled) {
            return $next($request);
        }

        // Check if user has active subscription
        if (!$user->hasActiveTimeSpendingSubscription()) {
            // Check for AJAX/JSON requests more explicitly
            $isAjax = $request->expectsJson() ||
                     $request->header('X-Requested-With') === 'XMLHttpRequest' ||
                     $request->header('Accept') === 'application/json' ||
                     $request->wantsJson();

            // Debug logging (remove in production)
            \Log::info('Subscription middleware triggered', [
                'user_id' => $user->id,
                'is_time_spending_enabled' => $user->is_time_spending_enabled,
                'has_active_subscription' => $user->hasActiveTimeSpendingSubscription(),
                'is_ajax' => $isAjax,
                'expects_json' => $request->expectsJson(),
                'x_requested_with' => $request->header('X-Requested-With'),
                'accept_header' => $request->header('Accept'),
                'wants_json' => $request->wantsJson(),
                'url' => $request->url()
            ]);

            // For AJAX requests, return JSON response
            if ($isAjax) {
                return response()->json([
                    'success' => false,
                    'message' => 'Active subscription required to access Time Spending features.',
                    'redirect' => route('profile.edit', ['tab' => 'time-spending'])
                ], 403);
            }

            // For regular requests, redirect to profile with subscription message
            return redirect()->route('profile.edit', ['tab' => 'time-spending'])
                ->with('subscription_required', 'You need an active subscription to access Time Spending features.');
        }

        return $next($request);
    }
}
