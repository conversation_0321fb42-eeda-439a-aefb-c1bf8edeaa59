<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'contact_number' => ['required', 'string', 'max:20'],
            'gender' => ['required', 'string', Rule::in(['male', 'female'])],
            'date_of_birth' => ['required', 'date'],
            'interests' => ['nullable', 'string', 'max:1000'], // max length example
            'expectation' => ['nullable', 'string', 'max:1000'], // User's expectations from potential partners
            'interested_in_sugar_partner' => ['nullable', 'boolean'],
            'sugar_partner_types' => ['nullable', 'array'],
            'sugar_partner_types.*' => [
                'string',
                Rule::in(['sugar_daddy', 'sugar_mommy', 'sugar_companion_female', 'sugar_companion_male'])
            ],
            'profile_picture' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'], // 2MB max
        ];
    }
}
