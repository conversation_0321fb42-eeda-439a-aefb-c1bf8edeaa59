<?php

namespace Tests\Feature;

use App\Models\Feature;
use App\Models\SugarPartnerExchange;
use App\Models\SugarPartnerExchangePayment;
use App\Models\SugarPartnerHardReject;
use App\Models\SugarPartnerRejection;
use App\Models\User;
use App\Models\UserWallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SugarPartnerExchangeTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create Sugar Partner feature
        Feature::create([
            'name' => 'sugar_partner',
            'label' => 'Sugar Partner',
            'description' => 'Sugar Partner profile exchange system',
            'is_enabled' => true,
            'options' => [
                'default_exchange_price' => 100.00,
                'currency' => 'INR',
                'enable_profile_exchange' => true,
                'require_payment_for_exchange' => true
            ]
        ]);
    }

    /** @test */
    public function admin_can_initiate_profile_exchange()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $user1 = User::factory()->create([
            'interested_in_sugar_partner' => true,
            'sugar_partner_types' => ['sugar_daddy']
        ]);
        $user2 = User::factory()->create([
            'interested_in_sugar_partner' => true,
            'sugar_partner_types' => ['sugar_companion_female']
        ]);

        $this->actingAs($admin);

        $response = $this->post(route('admin.sugar-partners.initiate-exchange'), [
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
            'admin_notes' => 'Test exchange'
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('sugar_partner_exchanges', [
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
            'initiated_by_admin_id' => $admin->id,
            'exchange_price' => 100.00,
            'status' => 'pending_payment'
        ]);
    }

    /** @test */
    public function cannot_initiate_exchange_between_hard_rejected_users()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $user1 = User::factory()->create(['interested_in_sugar_partner' => true]);
        $user2 = User::factory()->create(['interested_in_sugar_partner' => true]);

        // Create hard reject between users
        SugarPartnerHardReject::create([
            'user1_id' => min($user1->id, $user2->id),
            'user2_id' => max($user1->id, $user2->id),
            'rejector_id' => $user1->id,
            'original_exchange_id' => 1,
            'rejection_reason' => 'Test hard reject'
        ]);

        $this->actingAs($admin);

        $response = $this->post(route('admin.sugar-partners.initiate-exchange'), [
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseMissing('sugar_partner_exchanges', [
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
        ]);
    }

    /** @test */
    public function user_can_pay_for_exchange_with_wallet()
    {
        $user = User::factory()->create(['interested_in_sugar_partner' => true]);
        $otherUser = User::factory()->create(['interested_in_sugar_partner' => true]);
        
        // Create wallet with sufficient balance
        $wallet = UserWallet::create([
            'user_id' => $user->id,
            'balance' => 200.00
        ]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user->id,
            'user2_id' => $otherUser->id,
            'initiated_by_admin_id' => 1,
            'exchange_price' => 100.00,
            'currency' => 'INR'
        ]);

        $this->actingAs($user);

        $response = $this->post(route('sugar-partner.exchange.process-payment', $exchange), [
            'payment_method' => 'wallet'
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('sugar_partner_exchange_payments', [
            'exchange_id' => $exchange->id,
            'user_id' => $user->id,
            'amount' => 100.00,
            'payment_method' => 'wallet',
            'status' => 'completed'
        ]);

        $wallet->refresh();
        $this->assertEquals(100.00, $wallet->balance);
    }

    /** @test */
    public function user_cannot_pay_with_insufficient_wallet_balance()
    {
        $user = User::factory()->create(['interested_in_sugar_partner' => true]);
        $otherUser = User::factory()->create(['interested_in_sugar_partner' => true]);
        
        // Create wallet with insufficient balance
        $wallet = UserWallet::create([
            'user_id' => $user->id,
            'balance' => 50.00
        ]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user->id,
            'user2_id' => $otherUser->id,
            'initiated_by_admin_id' => 1,
            'exchange_price' => 100.00,
            'currency' => 'INR'
        ]);

        $this->actingAs($user);

        $response = $this->post(route('sugar-partner.exchange.process-payment', $exchange), [
            'payment_method' => 'wallet'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseMissing('sugar_partner_exchange_payments', [
            'exchange_id' => $exchange->id,
            'user_id' => $user->id,
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function exchange_status_updates_when_both_users_pay()
    {
        $user1 = User::factory()->create(['interested_in_sugar_partner' => true]);
        $user2 = User::factory()->create(['interested_in_sugar_partner' => true]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
            'initiated_by_admin_id' => 1,
            'exchange_price' => 100.00,
            'currency' => 'INR'
        ]);

        // Create payments for both users
        SugarPartnerExchangePayment::create([
            'exchange_id' => $exchange->id,
            'user_id' => $user1->id,
            'amount' => 100.00,
            'payment_method' => 'razorpay',
            'status' => 'completed',
            'paid_at' => now()
        ]);

        SugarPartnerExchangePayment::create([
            'exchange_id' => $exchange->id,
            'user_id' => $user2->id,
            'amount' => 100.00,
            'payment_method' => 'razorpay',
            'status' => 'completed',
            'paid_at' => now()
        ]);

        $exchange->markPaymentCompleted();
        $exchange->refresh();

        $this->assertEquals('payment_completed', $exchange->status);
        $this->assertNotNull($exchange->payment_completed_at);
    }

    /** @test */
    public function user_can_submit_acceptance_response()
    {
        $user = User::factory()->create(['interested_in_sugar_partner' => true]);
        $otherUser = User::factory()->create(['interested_in_sugar_partner' => true]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user->id,
            'user2_id' => $otherUser->id,
            'initiated_by_admin_id' => 1,
            'exchange_price' => 100.00,
            'currency' => 'INR',
            'status' => 'payment_completed'
        ]);

        $this->actingAs($user);

        $response = $this->post(route('sugar-partner.exchange.submit-response', $exchange), [
            'rejection_type' => 'accept',
            'rejection_reason' => 'Great profile!',
            'admin_note' => 'Good match'
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('sugar_partner_rejections', [
            'exchange_id' => $exchange->id,
            'rejector_id' => $user->id,
            'rejected_user_id' => $otherUser->id,
            'rejection_type' => 'accept',
            'rejection_reason' => 'Great profile!'
        ]);
    }

    /** @test */
    public function hard_reject_creates_permanent_block()
    {
        $user = User::factory()->create(['interested_in_sugar_partner' => true]);
        $otherUser = User::factory()->create(['interested_in_sugar_partner' => true]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user->id,
            'user2_id' => $otherUser->id,
            'initiated_by_admin_id' => 1,
            'exchange_price' => 100.00,
            'currency' => 'INR',
            'status' => 'payment_completed'
        ]);

        $this->actingAs($user);

        $response = $this->post(route('sugar-partner.exchange.submit-response', $exchange), [
            'rejection_type' => 'hard_reject',
            'rejection_reason' => 'Not compatible',
            'admin_note' => 'Permanent rejection'
        ]);

        $response->assertRedirect();
        
        // Check hard reject record is created
        $this->assertDatabaseHas('sugar_partner_hard_rejects', [
            'user1_id' => min($user->id, $otherUser->id),
            'user2_id' => max($user->id, $otherUser->id),
            'rejector_id' => $user->id,
            'rejection_reason' => 'Not compatible'
        ]);

        // Verify future exchanges are blocked
        $canInitiate = SugarPartnerExchange::canInitiateExchange($user->id, $otherUser->id);
        $this->assertFalse($canInitiate['can_initiate']);
        $this->assertEquals('hard_reject', $canInitiate['reason']);
    }

    /** @test */
    public function rejection_sends_notification()
    {
        $user = User::factory()->create(['interested_in_sugar_partner' => true]);
        $otherUser = User::factory()->create(['interested_in_sugar_partner' => true]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user->id,
            'user2_id' => $otherUser->id,
            'initiated_by_admin_id' => 1,
            'exchange_price' => 100.00,
            'currency' => 'INR',
            'status' => 'payment_completed'
        ]);

        $this->actingAs($user);

        $response = $this->post(route('sugar-partner.exchange.submit-response', $exchange), [
            'rejection_type' => 'soft_reject',
            'rejection_reason' => 'Not the right time'
        ]);

        $response->assertRedirect();
        
        // Check notification is created
        $this->assertDatabaseHas('notifications', [
            'user_id' => $otherUser->id,
            'type' => 'sugar_partner_rejection',
            'title' => 'Sugar Partner Response: Soft Reject'
        ]);

        // Check rejection record shows notification sent
        $this->assertDatabaseHas('sugar_partner_rejections', [
            'exchange_id' => $exchange->id,
            'rejector_id' => $user->id,
            'notification_sent' => true
        ]);
    }

    /** @test */
    public function admin_can_cancel_exchange_with_refunds()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $user1 = User::factory()->create(['interested_in_sugar_partner' => true]);
        $user2 = User::factory()->create(['interested_in_sugar_partner' => true]);

        $exchange = SugarPartnerExchange::create([
            'user1_id' => $user1->id,
            'user2_id' => $user2->id,
            'initiated_by_admin_id' => $admin->id,
            'exchange_price' => 100.00,
            'currency' => 'INR',
            'status' => 'payment_completed'
        ]);

        // Create completed payment
        $payment = SugarPartnerExchangePayment::create([
            'exchange_id' => $exchange->id,
            'user_id' => $user1->id,
            'amount' => 100.00,
            'payment_method' => 'razorpay',
            'status' => 'completed',
            'paid_at' => now()
        ]);

        $this->actingAs($admin);

        $response = $this->post(route('admin.sugar-partners.exchanges.cancel', $exchange), [
            'cancellation_reason' => 'Admin decision to cancel'
        ]);

        $response->assertRedirect();
        
        $exchange->refresh();
        $this->assertEquals('cancelled', $exchange->status);
        
        $payment->refresh();
        $this->assertEquals('refunded', $payment->status);
    }
}
