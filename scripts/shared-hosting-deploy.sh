#!/bin/bash

# SettingWala Dating App - Shared Hosting Deployment Script
# This script prepares the Laravel app for deployment on shared hosting without Node.js

echo "🚀 SettingWala Dating App - Shared Hosting Deployment"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: Please run this script from the Laravel project root directory"
    exit 1
fi

echo "✅ Laravel project detected"

# Clear all caches
echo "🧹 Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
echo "⚡ Optimizing for production..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Ensure storage link exists
echo "🔗 Creating storage link..."
php artisan storage:link

# Set proper file permissions
echo "🔒 Setting file permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public

# Check if custom CSS and JS files exist
echo "📁 Checking custom assets..."
if [ -f "public/css/app.css" ]; then
    echo "✅ Custom CSS file found: public/css/app.css"
else
    echo "❌ Error: Custom CSS file not found. Please ensure public/css/app.css exists."
    exit 1
fi

if [ -f "public/js/app.js" ]; then
    echo "✅ Custom JS file found: public/js/app.js"
else
    echo "❌ Error: Custom JS file not found. Please ensure public/js/app.js exists."
    exit 1
fi

# Remove Node.js build directories and files (optional cleanup)
echo "🗑️  Cleaning up Node.js build artifacts..."
if [ -d "public/build" ]; then
    rm -rf public/build
    echo "✅ Removed public/build directory"
fi

if [ -d "node_modules" ]; then
    echo "⚠️  node_modules directory found. You can safely remove it for shared hosting deployment."
    echo "   Run: rm -rf node_modules"
fi

# Check environment configuration
echo "🔍 Checking environment configuration..."
if [ -f ".env" ]; then
    # Check if debug is disabled
    DEBUG_VALUE=$(grep "^APP_DEBUG=" .env | cut -d'=' -f2)
    if [ "$DEBUG_VALUE" = "true" ]; then
        echo "⚠️  Warning: APP_DEBUG is set to true. Consider setting it to false for production."
    else
        echo "✅ APP_DEBUG is properly configured for production"
    fi
    
    # Check if app is in production mode
    ENV_VALUE=$(grep "^APP_ENV=" .env | cut -d'=' -f2)
    if [ "$ENV_VALUE" != "production" ]; then
        echo "⚠️  Warning: APP_ENV is not set to 'production'. Consider updating it."
    else
        echo "✅ APP_ENV is properly set to production"
    fi
else
    echo "❌ Error: .env file not found. Please create one from .env.example"
    exit 1
fi

# Optimize autoloader
echo "🔄 Optimizing autoloader..."
composer dump-autoload --optimize

# Final verification
echo "🔍 Running final verification..."

# Check if the app can boot
php artisan --version > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Laravel application boots successfully"
else
    echo "❌ Error: Laravel application failed to boot"
    exit 1
fi

# Check database connection (optional)
echo "🗄️  Testing database connection..."
php artisan migrate:status > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
else
    echo "⚠️  Warning: Database connection failed. Please check your database configuration."
fi

echo ""
echo "🎉 Shared Hosting Deployment Preparation Complete!"
echo "=================================================="
echo ""
echo "📋 Deployment Checklist:"
echo "✅ Node.js dependencies removed"
echo "✅ Custom CSS/JS files created"
echo "✅ CDN links configured in layouts"
echo "✅ Laravel optimizations applied"
echo "✅ File permissions set"
echo ""
echo "📤 Files to upload to shared hosting:"
echo "   • All PHP files (app/, bootstrap/, config/, database/, resources/, routes/, storage/, vendor/)"
echo "   • public/ directory (including custom CSS/JS)"
echo "   • .env file (with production settings)"
echo "   • composer.json and composer.lock"
echo ""
echo "🚫 Files NOT to upload:"
echo "   • node_modules/ directory"
echo "   • public/build/ directory"
echo "   • package.json, package-lock.json"
echo "   • vite.config.js, tailwind.config.js, postcss.config.js"
echo "   • .git/ directory"
echo ""
echo "🌐 Your app is now ready for shared hosting deployment!"
echo "   All frontend assets are served via CDN or direct files."
echo "   No Node.js runtime required on the server."
echo ""
echo "💡 Next steps:"
echo "   1. Upload files to your shared hosting"
echo "   2. Point domain to public/ directory"
echo "   3. Import database if needed"
echo "   4. Test the application"
echo ""
