<x-app-layout>
    @section('title', 'Chat')
    @section('description', 'Chat with your booking partners')

    <style>
        /* Custom scrollbar for chat messages */
        #messages-container::-webkit-scrollbar {
            width: 6px;
        }

        #messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        #messages-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #messages-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Ensure proper word wrapping */
        .word-wrap {
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        /* Smooth scrolling for the entire container */
        .scroll-smooth {
            scroll-behavior: smooth;
        }

        /* Clean chat item hover effect */
        .chat-item:hover {
            background: linear-gradient(to right, rgba(99, 102, 241, 0.05), rgba(147, 51, 234, 0.05));
            border-left-color: #6366f1;
        }

        /* Active chat item styling */
        .chat-item.active {
            background: linear-gradient(to right, rgba(99, 102, 241, 0.1), rgba(147, 51, 234, 0.1));
            border-left-color: #6366f1;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }
    </style>

    <!-- Real-time chat without WebSocket -->
    <div class="min-h-screen bg-gray-50 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded relative mb-3" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded relative mb-3" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            @if (isset($error) && $error)
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded relative mb-3" role="alert">
                    <span class="block sm:inline">{{ $error }}</span>
                </div>
            @endif

            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 h-[650px]">
                    <!-- Chat List Sidebar -->
                    <div class="lg:col-span-1 border-r border-gray-200 bg-white">
                        <!-- Header -->
                        <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 21l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-lg font-semibold text-gray-900">Messages</h2>
                                    <p class="text-xs text-gray-500">Active conversations</p>
                                </div>
                            </div>
                        </div>

                        <!-- Chat List -->
                        <div class="overflow-y-auto h-full">

                            @if(count($chats) > 0)
                                <div class="p-3 bg-indigo-50 border-b border-gray-200">
                                    <p class="text-xs text-indigo-800">{{ count($chats) }} active chat(s)</p>
                                </div>
                                <div class="divide-y divide-gray-200">
                                    @foreach($chats as $chat)
                                        <div class="p-3 cursor-pointer chat-item transition-all duration-200 border-l-4 border-transparent"
                                             data-booking-id="{{ $chat['booking_id'] }}"
                                             data-user-id="{{ $chat['other_user']->id }}"
                                             onclick="openChat({{ $chat['booking_id'] }})">
                                        <div class="flex items-center space-x-3">
                                                <!-- Profile Image -->
                                                <div class="flex-shrink-0 relative">
                                                    @if($chat['other_user']->profile_picture)
                                                        <img class="w-10 h-10 rounded-full object-cover border-2 border-indigo-200 shadow-sm"
                                                             src="{{ asset('storage/' . $chat['other_user']->profile_picture) }}"
                                                             alt="{{ $chat['other_user']->name }}">
                                                    @else
                                                        <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                            </svg>
                                                        </div>
                                                    @endif
                                                    <!-- Online indicator -->
                                                    <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                                                </div>

                                                <!-- Chat Info -->
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center justify-between mb-1">
                                                        <p class="text-sm font-semibold text-gray-900 truncate hover:text-indigo-600 cursor-pointer"
                                                           onclick="event.stopPropagation(); openUserProfile({{ $chat['other_user']->id }})">
                                                            {{ $chat['other_user']->name }}
                                                        </p>
                                                        @if($chat['unread_count'] > 0)
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-sm">
                                                                {{ $chat['unread_count'] }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                    <div class="flex items-center space-x-1 mb-1">
                                                        <svg class="w-3 h-3 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <p class="text-xs text-gray-600">
                                                            @php
                                                                $startTime = \Carbon\Carbon::parse($chat['booking_date']);
                                                                $endTime = $startTime->copy()->addHours((float) $chat['duration_hours']);
                                                                $formattedTime = $startTime->format('M j, g:i A') . ' - ' . $endTime->format('g:i A');
                                                            @endphp
                                                            {{ $formattedTime }}
                                                        </p>
                                                    </div>
                                                    @if($chat['last_message'])
                                                        <p class="text-xs text-gray-600 truncate">
                                                            {{ Str::limit($chat['last_message']->message, 35) }}
                                                        </p>
                                                    @else
                                                        <p class="text-xs text-gray-400 italic">Start chatting!</p>
                                                    @endif
                                                </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="p-12 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">No active chats</h3>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Accept a booking to start chatting with clients.
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Chat Window -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-lg border border-gray-100 h-[650px] flex flex-col" id="chat-window">
                            <div class="flex items-center justify-center h-full text-gray-500">
                                <div class="text-center p-8">
                                    <div class="bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
                                        <svg class="h-12 w-12 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Welcome to Chat</h3>
                                    <p class="text-gray-600">Select a conversation from the left to start messaging</p>
                                    <p class="text-sm text-gray-400 mt-2">Your messages are secure and private</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <script>
        let currentBookingId = null;
        let messagePolling = null;
        let isTyping = false;
        let typingTimeout = null;
        let lastMessageId = 0;
        let isLoadingMessages = false;

        // Real-time chat initialized without WebSocket

        // Format booking time display to match PHP exactly
        function formatBookingTime(bookingDate, durationHours) {
            console.log('formatBookingTime called with:', bookingDate, durationHours);

            const startTime = new Date(bookingDate);
            const endTime = new Date(startTime.getTime() + (durationHours * 60 * 60 * 1000));

            // Format exactly like PHP: "M j, Y, g:i A - g:i A"
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const month = monthNames[startTime.getMonth()];
            const day = startTime.getDate(); // No leading zero
            const year = startTime.getFullYear();

            // Format start time
            let startHour = startTime.getHours();
            const startMinute = startTime.getMinutes().toString().padStart(2, '0');
            const startAmPm = startHour >= 12 ? 'PM' : 'AM';
            if (startHour > 12) startHour -= 12;
            if (startHour === 0) startHour = 12;

            // Format end time
            let endHour = endTime.getHours();
            const endMinute = endTime.getMinutes().toString().padStart(2, '0');
            const endAmPm = endHour >= 12 ? 'PM' : 'AM';
            if (endHour > 12) endHour -= 12;
            if (endHour === 0) endHour = 12;

            const formattedTime = `${month} ${day}, ${year}, ${startHour}:${startMinute} ${startAmPm} - ${endHour}:${endMinute} ${endAmPm}`;
            console.log('Formatted time:', formattedTime);

            return formattedTime;
        }

        // Check if booking has ended and remove from chat list
        function checkBookingExpiry() {
            const chatItems = document.querySelectorAll('.chat-item');
            let removedAny = false;

            chatItems.forEach(item => {
                const bookingId = item.getAttribute('data-booking-id');

                // Get booking data from the item (you can store this as data attributes)
                // For now, we'll refresh the page to get updated chat list
                // This ensures server-side filtering is applied
            });

            // Refresh chat list every 5 minutes to remove expired bookings
            setTimeout(() => {
                window.location.reload();
            }, 5 * 60 * 1000); // 5 minutes
        }

        // Start expiry check
        checkBookingExpiry();

        // Function to open user profile
        function openUserProfile(userId) {
            window.open(`/profile/${userId}`, '_blank');
        }

        function openChat(bookingId) {
            console.log('openChat function called with booking ID:', bookingId);
            currentBookingId = bookingId;
            lastMessageId = 0; // Reset message tracking

            // Update active chat item
            document.querySelectorAll('.chat-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = document.querySelector(`[data-booking-id="${bookingId}"]`);

            if (activeItem) {
                activeItem.classList.add('active');
            } else {
                return;
            }

            // Load initial messages
            loadMessages(bookingId, true);

            // Start real-time polling for new messages
            if (messagePolling) {
                clearInterval(messagePolling);
            }

            // Fast polling for real-time experience (every 1.5 seconds for better responsiveness)
            messagePolling = setInterval(() => {
                if (!isTyping && !isLoadingMessages) {
                    loadNewMessages(bookingId);
                }
            }, 1500);

            console.log('Chat opened for booking:', bookingId, 'Last message ID:', lastMessageId);
        }

        // Load all messages (initial load)
        function loadMessages(bookingId, forceUpdate = false) {
            if (isLoadingMessages && !forceUpdate) return;

            isLoadingMessages = true;

            fetch(`/chat/${bookingId}/messages`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        displayChat(data.booking, data.messages);
                        // Update last message ID for tracking
                        if (data.messages.length > 0) {
                            lastMessageId = Math.max(...data.messages.map(m => m.id));
                        }
                    } else {
                        console.error('Failed to load messages:', data.message);
                        if (forceUpdate) {
                            alert('Failed to load messages: ' + data.message);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                    if (forceUpdate) {
                        alert('Error loading messages: ' + error.message);
                    }
                })
                .finally(() => {
                    isLoadingMessages = false;
                });
        }

        // Load only new messages (for real-time updates)
        function loadNewMessages(bookingId) {
            if (isLoadingMessages) return;

            isLoadingMessages = true;

            fetch(`/chat/${bookingId}/messages?after=${lastMessageId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        if (data.messages.length > 0) {
                            console.log(`Received ${data.messages.length} new message(s) for booking ${bookingId}`);
                            // Add new messages to existing chat
                            data.messages.forEach(message => {
                                addMessageToChat(message);
                                lastMessageId = Math.max(lastMessageId, message.id);
                            });
                        }
                    } else {
                        console.error('Failed to load new messages:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading new messages:', error);
                })
                .finally(() => {
                    isLoadingMessages = false;
                });
        }

        // Add single message to chat
        function addMessageToChat(messageData) {
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) {
                return;
            }

            // Fix: Look for the correct class name (space-y-2, not space-y-3)
            let spaceDiv = messagesContainer.querySelector('.space-y-2');
            if (!spaceDiv) {
                // If no space div found (e.g., when there were no initial messages), create one
                spaceDiv = document.createElement('div');
                spaceDiv.className = 'space-y-2';
                messagesContainer.innerHTML = '';
                messagesContainer.appendChild(spaceDiv);
            }

            // Check if message already exists
            const existingMessage = spaceDiv.querySelector(`[data-message-id="${messageData.id}"]`);
            if (existingMessage) {
                console.log('Message already exists, skipping:', messageData.id);
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `flex ${messageData.sender_id === {{ $user->id }} ? 'justify-end' : 'justify-start'} mb-2`;
            messageDiv.setAttribute('data-message-id', messageData.id);

            messageDiv.innerHTML = `
                <div class="relative max-w-[75%] min-w-[80px] px-3 py-2 rounded-xl shadow-sm break-words ${messageData.sender_id === {{ $user->id }} ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-br-md' : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md'}">
                    <p class="text-sm leading-snug whitespace-pre-wrap word-wrap break-words">${messageData.message}</p>
                    <div class="flex ${messageData.sender_id === {{ $user->id }} ? 'justify-end' : 'justify-start'} mt-1">
                        <p class="text-xs ${messageData.sender_id === {{ $user->id }} ? 'text-indigo-100' : 'text-gray-400'}">${new Date(messageData.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                    </div>
                </div>
            `;

            spaceDiv.appendChild(messageDiv);
            console.log('Message added to chat:', messageData.id);

            // Scroll to bottom smoothly
            setTimeout(() => {
                messagesContainer.scrollTo({
                    top: messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }, 100);
        }

        function displayChat(booking, messages) {
            const chatWindow = document.getElementById('chat-window');
            const otherUser = booking.client_id === {{ $user->id }} ? booking.provider : booking.client;

            // Save current input value before updating
            const currentInput = document.getElementById('message-input');
            const savedInputValue = currentInput ? currentInput.value : '';

            chatWindow.innerHTML = `
                <!-- Chat Header -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                    <div class="flex items-center space-x-4">
                        ${otherUser.profile_picture ?
                            `<img class="w-12 h-12 rounded-full object-cover border-2 border-indigo-200 shadow-sm" src="/storage/${otherUser.profile_picture}" alt="${otherUser.name}">` :
                            `<div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>`
                        }
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-indigo-600 cursor-pointer" onclick="openUserProfile(${otherUser.id})">${otherUser.name}</h3>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                                <p class="text-sm text-gray-600">${formatBookingTime(booking.booking_date, booking.duration_hours)}</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Active
                        </span>
                    </div>
                </div>

                <!-- Messages -->
                <div class="flex-1 overflow-y-auto p-3 bg-gray-50 scroll-smooth" style="height: 530px; max-height: 530px;" id="messages-container">
                    <div class="space-y-2">
                        ${messages.length > 0 ?
                            messages.map(message => `
                                <div class="flex ${message.sender_id === {{ $user->id }} ? 'justify-end' : 'justify-start'} mb-2" data-message-id="${message.id}">
                                    <div class="relative max-w-[75%] min-w-[80px] px-3 py-2 rounded-xl shadow-sm break-words ${message.sender_id === {{ $user->id }} ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-br-md' : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md'}">
                                        <p class="text-sm leading-snug whitespace-pre-wrap word-wrap break-words">${message.message}</p>
                                        <div class="flex ${message.sender_id === {{ $user->id }} ? 'justify-end' : 'justify-start'} mt-1">
                                            <p class="text-xs ${message.sender_id === {{ $user->id }} ? 'text-indigo-100' : 'text-gray-400'}">${new Date(message.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                                        </div>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="flex justify-center items-center h-full"><div class="text-center text-gray-500 py-8"><div class="bg-white rounded-lg p-6 shadow-sm max-w-sm mx-auto"><svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path></svg><p class="text-gray-600 font-medium">No messages yet</p><p class="text-gray-400 text-sm mt-1">Start the conversation!</p></div></div></div>'
                        }
                    </div>
                </div>

                <!-- Message Input -->
                <div class="border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-3">
                    <form onsubmit="sendMessage(event)" class="flex items-center space-x-3">
                        <input type="text" id="message-input" placeholder="Type your message..."
                               class="flex-1 border border-gray-300 rounded-full px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 text-gray-900 bg-white shadow-sm"
                               autocomplete="off" required>
                        <button type="submit" id="send-button" class="flex-shrink-0 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-5 py-2.5 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl font-medium">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                            <span class="ml-2 hidden sm:inline font-semibold">Send</span>
                        </button>
                    </form>
                </div>
            `;

            // Restore input value, focus, and scroll to bottom
            const newInput = document.getElementById('message-input');
            if (newInput) {
                if (savedInputValue) {
                    newInput.value = savedInputValue;
                }
                // Always focus on input when chat opens
                setTimeout(() => {
                    newInput.focus();
                }, 100);
            }

            // Add typing detection
            if (newInput) {
                newInput.addEventListener('input', function() {
                    isTyping = true;
                    clearTimeout(typingTimeout);
                    typingTimeout = setTimeout(() => {
                        isTyping = false;
                    }, 2000); // Stop considering as typing after 2 seconds
                });

                newInput.addEventListener('blur', function() {
                    isTyping = false;
                });
            }

            const messagesContainer = document.getElementById('messages-container');
            if (messagesContainer) {
                // Smooth scroll to bottom
                setTimeout(() => {
                    messagesContainer.scrollTo({
                        top: messagesContainer.scrollHeight,
                        behavior: 'smooth'
                    });
                }, 100);
            }
        }

        function sendMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();

            if (!message || !currentBookingId) {
                alert('Please enter a message');
                return;
            }

            // Disable input while sending
            messageInput.disabled = true;

            fetch(`/chat/${currentBookingId}/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    isTyping = false; // Reset typing state

                    // Add message immediately to UI (optimistic update)
                    addMessageToChat({
                        id: data.message.id,
                        message: data.message.message,
                        sender_id: data.message.sender_id,
                        receiver_id: data.message.receiver_id,
                        booking_id: data.message.booking_id,
                        created_at: data.sent_at,
                        sender: data.message.sender
                    });

                    // Update last message ID
                    lastMessageId = Math.max(lastMessageId, data.message.id);


                } else {
                    alert('Failed to send message: ' + data.message);
                }
            })
            .catch(error => {
                alert('Failed to send message. Please try again.');
            })
            .finally(() => {
                // Re-enable input
                messageInput.disabled = false;
                messageInput.focus();
            });
        }

        // Add visual feedback for message loading
        function showMessageLoadingIndicator() {
            const messagesContainer = document.getElementById('messages-container');
            if (messagesContainer && !document.getElementById('loading-indicator')) {
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-indicator';
                loadingDiv.className = 'flex justify-center py-2';
                loadingDiv.innerHTML = `
                    <div class="flex items-center space-x-2 text-gray-500 text-xs">
                        <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-500"></div>
                        <span>Loading new messages...</span>
                    </div>
                `;
                messagesContainer.appendChild(loadingDiv);
            }
        }

        function hideMessageLoadingIndicator() {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Chat page loaded - Real-time polling system ready');

            // Check for URL parameter first
            const urlParams = new URLSearchParams(window.location.search);
            const openBookingParam = urlParams.get('open_booking');
            const bookingIdParam = urlParams.get('booking_id');

            let autoOpenBookingId = null;

            // Priority 1: URL parameter (open_booking)
            if (openBookingParam) {
                autoOpenBookingId = parseInt(openBookingParam);
                console.log('Auto-opening chat from URL parameter (open_booking):', autoOpenBookingId);
            }
            // Priority 2: URL parameter (booking_id)
            else if (bookingIdParam) {
                autoOpenBookingId = parseInt(bookingIdParam);
                console.log('Auto-opening chat from URL parameter (booking_id):', autoOpenBookingId);
            }
            // Priority 3: Controller selected booking
            @if(isset($selectedBookingId) && $selectedBookingId)
            else {
                autoOpenBookingId = {{ $selectedBookingId }};
                console.log('Auto-opening chat from controller selection:', autoOpenBookingId);
            }
            @endif

            if (autoOpenBookingId) {
                // Check if the booking exists in the chat list
                const targetChatItem = document.querySelector(`[data-booking-id="${autoOpenBookingId}"]`);

                if (targetChatItem) {
                    setTimeout(() => {
                        openChat(autoOpenBookingId);

                        // Clean URL after opening chat
                        if (openBookingParam || bookingIdParam) {
                            const newUrl = window.location.pathname;
                            window.history.replaceState({}, document.title, newUrl);
                        }
                    }, 1500); // Increased delay to ensure everything is loaded
                } else {
                    // Show alert to user
                    setTimeout(() => {
                        alert('This chat is no longer available. The booking may have ended or been cancelled.');
                    }, 1000);
                }
            }
        });

        // Clean up when leaving page
        window.addEventListener('beforeunload', function() {
            if (messagePolling) {
                clearInterval(messagePolling);
            }
        });
    </script>
</x-app-layout>
