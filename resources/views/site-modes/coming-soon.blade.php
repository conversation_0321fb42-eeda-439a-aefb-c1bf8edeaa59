<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coming Soon - {{ config('app.name', 'SettingWala') }}</title>
    
    <!-- Favicon -->
    @if(App\Models\Setting::get('favicon'))
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . App\Models\Setting::get('favicon')) }}">
    @else
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(-45deg, #ff6b6b, #ee5a24, #ff9ff3, #f093fb, #f5576c, #4facfe, #00f2fe);
            background-size: 600% 600%;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            animation: gradientShift 35s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            12.5% {
                background-position: 25% 25%;
            }
            25% {
                background-position: 100% 50%;
            }
            37.5% {
                background-position: 75% 75%;
            }
            50% {
                background-position: 100% 100%;
            }
            62.5% {
                background-position: 25% 75%;
            }
            75% {
                background-position: 0% 100%;
            }
            87.5% {
                background-position: 25% 25%;
            }
            100% {
                background-position: 0% 50%;
            }
        }
        
        .coming-soon-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            max-width: 700px;
            width: 100%;
        }
        
        .logo {
            max-height: 70px;
            margin-bottom: 2rem;
        }
        
        .coming-soon-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .coming-soon-message {
            font-size: 1.2rem;
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 3rem;
        }
        
        .countdown-container {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .countdown-item {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px;
            padding: 1.5rem 1rem;
            min-width: 100px;
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }
        
        .countdown-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
            line-height: 1;
        }
        
        .countdown-label {
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 0.5rem;
        }
        
        .launch-info {
            background: #f7fafc;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }
        
        .launch-info h6 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .launch-date {
            font-size: 1.1rem;
            color: #ff6b6b;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .coming-soon-container {
                padding: 2rem 1.5rem;
            }
            
            .coming-soon-title {
                font-size: 2.2rem;
            }
            
            .countdown-item {
                min-width: 80px;
                padding: 1rem 0.5rem;
            }
            
            .countdown-number {
                font-size: 2rem;
            }
        }
        
        .expired-message {
            display: none;
            color: #28a745;
            font-size: 1.5rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="coming-soon-container">
        <!-- Logo -->
        @if(App\Models\Setting::get('header_logo'))
            <img src="{{ asset('storage/' . App\Models\Setting::get('header_logo')) }}" 
                 alt="{{ config('app.name', 'SettingWala') }}" class="logo">
        @endif
        
        <!-- Title -->
        <h1 class="coming-soon-title">
            {{ App\Models\Setting::get('coming_soon_title', 'Something Amazing is Coming Soon!') }}
        </h1>
        
        <!-- Message -->
        <div class="coming-soon-message">
            {!! nl2br(e(App\Models\Setting::get('coming_soon_message', 'We are working hard to bring you an amazing dating experience. Stay tuned for the launch!'))) !!}
        </div>
        
        <!-- Countdown Timer -->
        <div id="countdown-container" class="countdown-container">
            <div class="countdown-item">
                <span id="days" class="countdown-number">00</span>
                <span class="countdown-label">Days</span>
            </div>
            <div class="countdown-item">
                <span id="hours" class="countdown-number">00</span>
                <span class="countdown-label">Hours</span>
            </div>
            <div class="countdown-item">
                <span id="minutes" class="countdown-number">00</span>
                <span class="countdown-label">Minutes</span>
            </div>
            <div class="countdown-item">
                <span id="seconds" class="countdown-number">00</span>
                <span class="countdown-label">Seconds</span>
            </div>
        </div>
        
        <!-- Expired Message -->
        <div id="expired-message" class="expired-message">
            <i class="bi bi-check-circle me-2"></i>We're Live! Refreshing the page...
        </div>
        
        <!-- Launch Information -->
        @php
            $launchTime = App\Models\Setting::get('coming_soon_launch_time');
        @endphp
        @if($launchTime)
            <div class="launch-info">
                <h6><i class="bi bi-calendar-event me-2"></i>Launch Date</h6>
                <div class="launch-date">
                    {{ \Carbon\Carbon::parse($launchTime)->format('F j, Y \a\t g:i A T') }}
                </div>
            </div>
        @endif
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Countdown Timer
        @if($launchTime)
            const launchTime = new Date("{{ \Carbon\Carbon::parse($launchTime)->toISOString() }}").getTime();
            
            function updateCountdown() {
                const now = new Date().getTime();
                const distance = launchTime - now;
                
                if (distance < 0) {
                    // Time expired - launch time reached
                    document.getElementById('countdown-container').style.display = 'none';
                    document.getElementById('expired-message').style.display = 'block';

                    // Immediately redirect to home page to show live site
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);

                    return;
                }
                
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                document.getElementById('days').textContent = days.toString().padStart(2, '0');
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            }
            
            // Update countdown immediately and then every second
            updateCountdown();
            setInterval(updateCountdown, 1000);
        @else
            // No launch time set, hide countdown
            document.getElementById('countdown-container').style.display = 'none';
        @endif
    </script>
</body>
</html>
