<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Under Maintenance - {{ config('app.name', 'SettingWala') }}</title>
    
    <!-- Favicon -->
    @if(App\Models\Setting::get('favicon'))
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . App\Models\Setting::get('favicon')) }}">
    @else
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(-45deg, #e3f2fd, #bbdefb, #90caf9, #64b5f6, #42a5f5, #2196f3, #1e88e5);
            background-size: 600% 600%;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            animation: gradientShift 35s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            12.5% {
                background-position: 25% 25%;
            }
            25% {
                background-position: 100% 50%;
            }
            37.5% {
                background-position: 75% 75%;
            }
            50% {
                background-position: 100% 100%;
            }
            62.5% {
                background-position: 25% 75%;
            }
            75% {
                background-position: 0% 100%;
            }
            87.5% {
                background-position: 25% 25%;
            }
            100% {
                background-position: 0% 50%;
            }
        }
        
        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .maintenance-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
        }
        
        .maintenance-message {
            font-size: 1.1rem;
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .logo {
            max-height: 60px;
            margin-bottom: 2rem;
        }
        
        .contact-info {
            background: #f7fafc;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .contact-info h6 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .contact-info p {
            color: #718096;
            margin-bottom: 0;
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 2rem 1.5rem;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <!-- Logo -->
        @if(App\Models\Setting::get('header_logo'))
            <img src="{{ asset('storage/' . App\Models\Setting::get('header_logo')) }}" 
                 alt="{{ config('app.name', 'SettingWala') }}" class="logo">
        @endif
        
        <!-- Maintenance Icon -->
        <div class="maintenance-icon">
            <i class="bi bi-tools"></i>
        </div>
        
        <!-- Title -->
        <h1 class="maintenance-title">Under Maintenance</h1>
        
        <!-- Message -->
        <div class="maintenance-message">
            @php
                $message = App\Models\Setting::get('maintenance_mode_message', 'We are currently under maintenance. The website will be back online approximately at {end_time}.');
                $endTime = App\Models\Setting::get('maintenance_mode_end_time');
                
                if ($endTime) {
                    $formattedEndTime = \Carbon\Carbon::parse($endTime)->format('M j, Y \a\t g:i A');
                    $message = str_replace('{end_time}', $formattedEndTime, $message);
                } else {
                    $message = str_replace('{end_time}', 'soon', $message);
                }
            @endphp
            {!! nl2br(e($message)) !!}
        </div>
        
        <!-- Contact Information -->
        <div class="contact-info">
            <h6><i class="bi bi-info-circle me-2"></i>Need Help?</h6>
            <p>If you have any urgent inquiries, please contact our support team. We apologize for any inconvenience caused.</p>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-refresh when maintenance mode expires
        @php
            $endTime = App\Models\Setting::get('maintenance_mode_end_time');
        @endphp
        @if($endTime)
            const maintenanceEndTime = new Date("{{ \Carbon\Carbon::parse($endTime)->toISOString() }}").getTime();

            function checkMaintenanceExpiry() {
                const now = new Date().getTime();
                if (now >= maintenanceEndTime) {
                    // Maintenance period has ended, refresh to show live site
                    window.location.reload();
                }
            }

            // Check every 30 seconds
            setInterval(checkMaintenanceExpiry, 30000);

            // Also check immediately
            checkMaintenanceExpiry();
        @else
            // If no end time is set, check every 2 minutes if maintenance mode is still active
            function checkMaintenanceStatus() {
                fetch('/maintenance', { method: 'HEAD' })
                    .then(response => {
                        if (response.status !== 503) {
                            // Maintenance mode is no longer active
                            window.location.href = '/';
                        }
                    })
                    .catch(() => {
                        // If fetch fails, try to reload anyway
                        window.location.reload();
                    });
            }

            // Check every 2 minutes
            setInterval(checkMaintenanceStatus, 120000);
        @endif
    </script>
</body>
</html>
