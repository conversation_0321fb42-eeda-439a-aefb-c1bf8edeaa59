@section('title', 'Booking Calendar')
@section('description', 'Manage your time spending bookings with an intuitive calendar interface.')

<x-app-layout>
    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Calendar Header -->
            <div class="card mb-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">{{ $currentDate->format('F Y') }}</h2>
                        <p class="text-gray-600 mt-1">
                            @auth
                                Click on dates with bookings to view and manage them
                            @else
                                Please <a href="{{ route('login') }}" class="text-blue-600 hover:underline">log in</a> to view your bookings
                            @endauth
                        </p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- Previous Month -->
                        <a href="{{ route('calendar.index', ['month' => $currentDate->copy()->subMonth()->month, 'year' => $currentDate->copy()->subMonth()->year]) }}"
                           class="btn-secondary p-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>

                        <!-- Today Button -->
                        <a href="{{ route('calendar.index') }}" class="btn-secondary px-4 py-2">
                            Today
                        </a>

                        <!-- Next Month -->
                        <a href="{{ route('calendar.index', ['month' => $currentDate->copy()->addMonth()->month, 'year' => $currentDate->copy()->addMonth()->year]) }}"
                           class="btn-secondary p-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div class="bg-white rounded-2xl overflow-hidden border border-gray-200">
                    <!-- Day Headers -->
                    <div class="grid grid-cols-7 bg-gradient-to-r from-indigo-500 to-purple-600">
                        <div class="p-4 text-center font-semibold text-white text-sm">Sun</div>
                        <div class="p-4 text-center font-semibold text-white text-sm">Mon</div>
                        <div class="p-4 text-center font-semibold text-white text-sm">Tue</div>
                        <div class="p-4 text-center font-semibold text-white text-sm">Wed</div>
                        <div class="p-4 text-center font-semibold text-white text-sm">Thu</div>
                        <div class="p-4 text-center font-semibold text-white text-sm">Fri</div>
                        <div class="p-4 text-center font-semibold text-white text-sm">Sat</div>
                    </div>

                    <!-- Calendar Days -->
                    <div class="grid grid-cols-7">
                        @php
                            $startOfMonth = $currentDate->copy()->startOfMonth();
                            $endOfMonth = $currentDate->copy()->endOfMonth();
                            $startOfCalendar = $startOfMonth->copy()->startOfWeek();
                            $endOfCalendar = $endOfMonth->copy()->endOfWeek();
                            $today = now();
                        @endphp

                        @for ($date = $startOfCalendar->copy(); $date->lte($endOfCalendar); $date->addDay())
                            @php
                                $dateString = $date->format('Y-m-d');
                                $bookingCount = $bookingCounts[$dateString] ?? 0;
                                $isCurrentMonth = $date->month === $currentDate->month;
                                $isToday = $date->isSameDay($today);
                                $isPast = $date->lt($today->startOfDay());
                            @endphp

                            <div class="relative min-h-[80px] p-3 border-r border-b border-gray-200 transition-all duration-200 bg-white
                                {{ !$isCurrentMonth ? 'bg-gray-50 text-gray-400' : '' }}
                                {{ $isToday ? 'bg-gradient-to-br from-indigo-50 to-purple-50 border-2 border-indigo-500' : '' }}
                                {{ $isPast ? 'bg-gray-100 text-gray-500' : '' }}
                                {{ $bookingCount > 0 && $isCurrentMonth ? 'cursor-pointer hover:bg-gray-50 hover:scale-105 hover:z-10 hover:shadow-lg' : 'cursor-default' }}"
                                 data-date="{{ $dateString }}"
                                 @if($bookingCount > 0 && $isCurrentMonth) onclick="openBookingModal('{{ $dateString }}')" @endif>
                                <div class="font-semibold text-base">{{ $date->day }}</div>
                                @if($bookingCount > 0 && $isCurrentMonth)
                                    <div class="absolute top-2 right-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold shadow-md">{{ $bookingCount }}</div>
                                @endif
                            </div>
                        @endfor
                    </div>
                </div>
            </div>

            <!-- Legend and Info -->
            <div class="card">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Legend</h3>
                        <div class="flex flex-wrap gap-6">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-primary-500 rounded mr-2"></div>
                                <span class="text-sm text-gray-600">Today</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                                <span class="text-sm text-gray-600">Has Bookings (Clickable)</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-gray-300 rounded mr-2"></div>
                                <span class="text-sm text-gray-600">Past Date</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-gray-100 border border-gray-300 rounded mr-2"></div>
                                <span class="text-sm text-gray-600">No Bookings (Non-clickable)</span>
                            </div>
                        </div>
                    </div>

                    @if(array_sum($bookingCounts) === 0)
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 lg:max-w-md">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h4 class="text-sm font-semibold text-blue-800 mb-1">No Bookings This Month</h4>
                                    <p class="text-sm text-blue-700">
                                        You don't have any bookings for {{ $currentDate->format('F Y') }}.
                                        Bookings will appear on the calendar once clients make requests.
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Details Modal -->
    <div id="bookingModal" class="fixed inset-0 z-50 hidden" style="background-color: rgba(0, 0, 0, 0.8);">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900" id="modalTitle">Bookings for Date</h3>
                        <p class="text-gray-600 mt-1" id="modalSubtitle">Manage your booking requests</p>
                    </div>
                    <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Tab Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="switchTab('pending')"
                                class="tab-button active-tab py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                                id="tab-pending">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Requests
                                <span class="tab-count ml-2 px-2 py-1 text-xs rounded-full" id="count-pending">0</span>
                            </span>
                        </button>
                        <button onclick="switchTab('accepted')"
                                class="tab-button py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                                id="tab-accepted">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Approved
                                <span class="tab-count ml-2 px-2 py-1 text-xs rounded-full" id="count-accepted">0</span>
                            </span>
                        </button>
                        <button onclick="switchTab('rejected')"
                                class="tab-button py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                                id="tab-rejected">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Rejected
                                <span class="tab-count ml-2 px-2 py-1 text-xs rounded-full" id="count-rejected">0</span>
                            </span>
                        </button>
                    </nav>
                </div>

                <!-- Modal Content -->
                <div class="overflow-y-auto max-h-[60vh]" id="modalContent">
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                        <p class="ml-3 text-gray-600">Loading your bookings...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectModal" class="fixed inset-0 z-50 hidden" style="background-color: rgba(0, 0, 0, 0.8);">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Reject Booking</h3>
                    <form id="rejectForm">
                        <div class="mb-4">
                            <label for="rejectionReason" class="block text-sm font-medium text-gray-700 mb-2">
                                Reason for rejection
                            </label>
                            <textarea id="rejectionReason"
                                      name="reason"
                                      rows="3"
                                      class="input-field"
                                      placeholder="Please provide a reason for rejecting this booking..."
                                      required></textarea>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeRejectModal()" class="btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn-danger">
                                Reject Booking
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>









    @push('scripts')
    <script>
        let currentBookingId = null;
        let currentBookingsData = null;
        let currentActiveTab = 'pending';

        // Open booking modal for a specific date
        function openBookingModal(date) {
            const modal = document.getElementById('bookingModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalContent = document.getElementById('modalContent');

            // Show modal
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Reset to default tab
            currentActiveTab = 'pending';
            setActiveTab('pending');

            // Show loading state
            modalContent.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                    <p class="ml-3 text-gray-600">Loading your bookings...</p>
                </div>
            `;

            // Fetch bookings for the date
            fetch(`/calendar/bookings/${date}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('Please log in to view your bookings.');
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Bookings data received:', data);
                if (data.success) {
                    modalTitle.textContent = `Bookings for ${data.date}`;
                    currentBookingsData = data;
                    updateTabCounts(data.counts);

                    // Check if there are any bookings at all
                    const totalBookings = data.total_count || 0;
                    console.log('Total bookings found:', totalBookings);
                    console.log('Bookings data:', data.bookings);
                    console.log('Bookings by status:', data.bookings_by_status);

                    if (totalBookings === 0) {
                        console.log('No bookings found, showing no bookings message');
                        showNoBookingsMessage(data.date);
                    } else {
                        console.log('Bookings found, displaying tab content');
                        displayTabContent(currentActiveTab);
                    }
                } else {
                    showError(data.message || 'Failed to load bookings. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error loading bookings:', error);
                if (error.message.includes('log in')) {
                    showError('Please log in to view your bookings. <a href="/login" class="text-blue-600 hover:underline">Click here to log in</a>');
                } else {
                    showError('An error occurred while loading bookings. Please try again.');
                }
            });
        }

        // Close booking modal
        function closeBookingModal() {
            const modal = document.getElementById('bookingModal');
            modal.classList.add('hidden');
            document.body.style.overflow = '';
            currentBookingsData = null;
            currentActiveTab = 'pending';
        }

        // Switch tab function
        function switchTab(tabName) {
            currentActiveTab = tabName;
            setActiveTab(tabName);
            displayTabContent(tabName);
        }

        // Set active tab styling
        function setActiveTab(tabName) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(tab => {
                tab.classList.remove('active-tab');
            });

            // Add active class to selected tab
            document.getElementById(`tab-${tabName}`).classList.add('active-tab');
        }

        // Update tab counts
        function updateTabCounts(counts) {
            document.getElementById('count-pending').textContent = counts.pending;
            document.getElementById('count-accepted').textContent = counts.accepted;
            document.getElementById('count-rejected').textContent = counts.rejected;
        }

        // Show no bookings message when there are no bookings at all
        function showNoBookingsMessage(date) {
            const modalContent = document.getElementById('modalContent');
            modalContent.innerHTML = `
                <div class="flex flex-col items-center justify-center py-16 px-6">
                    <svg class="w-20 h-20 text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-xl font-medium text-gray-900 mb-3">No Bookings Found</h3>
                    <p class="text-gray-600 text-center mb-6">
                        You don't have any bookings for <strong>${date}</strong>.<br>
                        Bookings will appear here once clients make requests.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button onclick="closeBookingModal()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            Close
                        </button>
                        <a href="/find-person" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                            Browse Profiles
                        </a>
                    </div>
                </div>
            `;
        }

        // Display content for specific tab
        function displayTabContent(tabName) {
            if (!currentBookingsData) return;

            const modalContent = document.getElementById('modalContent');
            const bookings = currentBookingsData.bookings_by_status[tabName];

            let html = '<div class="p-6">';

            // Add informational message for requests tab
            if (tabName === 'pending' && bookings.length > 0) {
                html += createInfoMessage();
            }

            if (bookings.length === 0) {
                html += createEmptyState(tabName);
            } else {
                bookings.forEach(booking => {
                    html += createBookingCard(booking);
                });
            }

            html += '</div>';
            modalContent.innerHTML = html;
        }

        // Create informational message for requests tab
        function createInfoMessage() {
            return `
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-semibold text-indigo-800 mb-1">Booking Conflict Resolution</h4>
                            <p class="text-sm text-indigo-800">
                                You can only accept <strong>one booking per time slot</strong>. When you approve a booking for a specific time,
                                all other overlapping bookings for that same time slot will be <strong>automatically declined</strong>
                                to prevent scheduling conflicts.
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }

        // Create empty state for tabs
        function createEmptyState(tabName) {
            const messages = {
                'pending': {
                    title: 'No pending requests',
                    description: 'There are no booking requests waiting for your response.'
                },
                'accepted': {
                    title: 'No approved bookings',
                    description: 'You haven\'t approved any bookings for this date yet.'
                },
                'rejected': {
                    title: 'No rejected bookings',
                    description: 'You haven\'t rejected any bookings for this date.'
                }
            };

            const message = messages[tabName];

            return `
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">${message.title}</h3>
                    <p class="text-gray-500">${message.description}</p>
                </div>
            `;
        }

        // Create booking card HTML
        function createBookingCard(booking) {
            const statusClass = `status-${booking.provider_status}`;
            const statusText = booking.provider_status.charAt(0).toUpperCase() + booking.provider_status.slice(1);

            let actionButtons = '';
            if (booking.can_accept || booking.can_reject || booking.can_block) {
                actionButtons = `
                    <div class="flex flex-wrap gap-2 mt-4">
                        ${booking.can_accept ? `<button onclick="acceptBooking(${booking.id})" class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 border-none cursor-pointer bg-green-500 text-white hover:bg-green-600 hover:-translate-y-0.5">Accept</button>` : ''}
                        ${booking.can_reject ? `<button onclick="openRejectModal(${booking.id})" class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 border-none cursor-pointer bg-red-500 text-white hover:bg-red-600 hover:-translate-y-0.5">Reject</button>` : ''}
                        ${booking.can_block ? `<button onclick="blockClient(${booking.id})" class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 border-none cursor-pointer bg-gray-600 text-white hover:bg-gray-700 hover:-translate-y-0.5">Block Client</button>` : ''}
                    </div>
                `;
            }

            return `
                <div class="bg-white border border-gray-200 rounded-xl p-6 mb-4 transition-all duration-200 hover:shadow-lg hover:-translate-y-1" id="booking-${booking.id}">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            ${booking.client_profile_picture && booking.client_profile_picture !== '/images/default-avatar.png' ?
                                `<img src="${booking.client_profile_picture}" alt="${booking.client_name}" class="w-12 h-12 rounded-full object-cover mr-4 border-2 border-white shadow-md" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold mr-4 border-2 border-white shadow-md" style="display: none;">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>` :
                                `<div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold mr-4 border-2 border-white shadow-md">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>`
                            }
                            <div>
                                <h4 class="font-semibold text-gray-900">
                                    <a href="/find-person/${booking.client_id}/from-hire-request" class="hover:text-indigo-600 transition-colors cursor-pointer font-semibold rounded px-1 py-0.5 -mx-1 -my-0.5 hover:bg-indigo-50 hover:underline hover:translate-x-1" target="_blank">
                                        ${booking.client_name}
                                    </a>
                                </h4>
                                <p class="text-sm text-gray-600">${booking.time_range}</p>
                            </div>
                        </div>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide
                            ${booking.provider_status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ''}
                            ${booking.provider_status === 'accepted' ? 'bg-green-100 text-green-800' : ''}
                            ${booking.provider_status === 'rejected' ? 'bg-red-100 text-red-800' : ''}">${statusText}</span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Duration:</span>
                            <span class="text-gray-600">${booking.duration} hour${booking.duration !== 1 ? 's' : ''}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Amount:</span>
                            <span class="text-gray-600">₹${booking.total_amount}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Location:</span>
                            <span class="text-gray-600">${booking.location}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Requested:</span>
                            <span class="text-gray-600">${booking.created_at}</span>
                        </div>
                    </div>

                    ${booking.notes ? `
                        <div class="mt-4">
                            <span class="font-medium text-gray-700">Notes:</span>
                            <p class="text-gray-600 mt-1">${booking.notes}</p>
                        </div>
                    ` : ''}

                    ${actionButtons}
                </div>
            `;
        }

        // Accept booking function
        function acceptBooking(bookingId) {
            setLoadingState(bookingId, true);

            fetch(`/provider/booking/${bookingId}/accept`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('Booking accepted successfully!');
                    refreshCurrentTab();
                    updateCalendarBadge();
                } else {
                    showError(data.message || 'Failed to accept booking.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred. Please try again.');
            })
            .finally(() => {
                setLoadingState(bookingId, false);
            });
        }

        // Open reject modal
        function openRejectModal(bookingId) {
            currentBookingId = bookingId;
            const modal = document.getElementById('rejectModal');
            modal.classList.remove('hidden');
            document.getElementById('rejectionReason').value = '';
            document.getElementById('rejectionReason').focus();
        }

        // Close reject modal
        function closeRejectModal() {
            const modal = document.getElementById('rejectModal');
            modal.classList.add('hidden');
            currentBookingId = null;
        }

        // Block client function
        function blockClient(bookingId) {
            setLoadingState(bookingId, true);

            fetch(`/provider/booking/${bookingId}/block`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess('Client blocked successfully.');
                    refreshCurrentTab();
                    updateCalendarBadge();
                } else {
                    showError(data.message || 'Failed to block client.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred. Please try again.');
            })
            .finally(() => {
                setLoadingState(bookingId, false);
            });
        }

        // Handle reject form submission
        document.getElementById('rejectForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const reason = document.getElementById('rejectionReason').value.trim();
            if (!reason) {
                showError('Please provide a reason for rejection.');
                return;
            }

            setLoadingState(currentBookingId, true);

            fetch(`/provider/booking/${currentBookingId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ reason: reason })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeRejectModal();
                    showSuccess('Booking rejected successfully.');
                    refreshCurrentTab();
                    updateCalendarBadge();
                } else {
                    showError(data.message || 'Failed to reject booking.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred. Please try again.');
            })
            .finally(() => {
                setLoadingState(currentBookingId, false);
            });
        });

        // Refresh current tab data
        function refreshCurrentTab() {
            if (!currentBookingsData) return;

            // Re-fetch the data for the current date
            const currentDate = currentBookingsData.date;
            const dateString = new Date(currentDate).toISOString().split('T')[0];

            fetch(`/calendar/bookings/${dateString}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentBookingsData = data;
                    updateTabCounts(data.counts);
                    displayTabContent(currentActiveTab);
                }
            })
            .catch(error => {
                console.error('Error refreshing tab:', error);
            });
        }

        // Set loading state for booking actions
        function setLoadingState(bookingId, isLoading) {
            const bookingCard = document.getElementById(`booking-${bookingId}`);
            if (bookingCard) {
                const buttons = bookingCard.querySelectorAll('.btn-action');
                buttons.forEach(button => {
                    button.disabled = isLoading;
                    if (isLoading) {
                        button.style.opacity = '0.6';
                        button.style.cursor = 'not-allowed';
                    } else {
                        button.style.opacity = '1';
                        button.style.cursor = 'pointer';
                    }
                });
            }
        }

        // Update calendar badge after booking action
        function updateCalendarBadge() {
            // Refresh the page to update booking counts
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        }

        // Show success message
        function showSuccess(message) {
            // Create and show a temporary success message
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 z-50 bg-green-50 border border-green-200 text-green-800 px-6 py-4 rounded-2xl shadow-lg';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-medium">${message}</span>
                </div>
            `;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Show error message
        function showError(message) {
            const modalContent = document.getElementById('modalContent');

            if (modalContent) {
                // Check if this is a login error
                if (message.includes('log in')) {
                    modalContent.innerHTML = `
                        <div class="flex flex-col items-center justify-center py-12 px-6">
                            <svg class="w-16 h-16 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Authentication Required</h3>
                            <p class="text-gray-600 text-center mb-4">Please log in to view your bookings.</p>
                            <a href="/login" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                Log In
                            </a>
                        </div>
                    `;
                } else {
                    // Regular error display
                    modalContent.innerHTML = `
                        <div class="flex flex-col items-center justify-center py-12 px-6">
                            <svg class="w-16 h-16 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Unable to Load Bookings</h3>
                            <p class="text-gray-600 text-center mb-4">${message}</p>
                            <button onclick="location.reload()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                Try Again
                            </button>
                        </div>
                    `;
                }
            } else {
                // Fallback to alert if modal content not found
                alert(message);
            }
        }

        // Close modals when clicking outside
        document.getElementById('bookingModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBookingModal();
            }
        });

        document.getElementById('rejectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRejectModal();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeBookingModal();
                closeRejectModal();
            }
        });
    </script>
    @endpush
</x-app-layout>
