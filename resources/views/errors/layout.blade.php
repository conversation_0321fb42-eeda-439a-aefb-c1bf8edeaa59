@extends('layouts.app')

@section('title', $__env->yieldContent('code', 'Error') . ' - ' . $__env->yieldContent('message', 'Something went wrong'))

@section('content')
    <!-- Generic Error Page -->
    <div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 flex items-center justify-center px-4">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-20" style="background-image: radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);"></div>
        
        <div class="relative z-10 max-w-2xl mx-auto text-center">
            <!-- Error Card -->
            <div class="bg-white/80 backdrop-blur-lg rounded-3xl p-8 md:p-12 border border-white/50 shadow-2xl">
                <!-- Error Code -->
                <div class="mb-6">
                    <h1 class="text-8xl md:text-9xl font-bold bg-gradient-to-r from-gray-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        @yield('code', 'Error')
                    </h1>
                </div>

                <!-- Error Icon -->
                <div class="mb-6">
                    <div class="w-24 h-24 mx-auto bg-gradient-to-r from-gray-100 to-blue-100 rounded-full flex items-center justify-center">
                        @yield('icon')
                    </div>
                </div>

                <!-- Error Message -->
                <div class="mb-8">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                        @yield('title', 'Something went wrong')
                    </h2>
                    <p class="text-gray-600 text-lg leading-relaxed">
                        @yield('description', 'We encountered an unexpected error. Please try again or contact support if the problem persists.')
                    </p>
                </div>

                <!-- Additional Content -->
                @yield('additional_content')

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    @yield('primary_action')
                    
                    <a href="{{ route('home') }}" class="inline-flex items-center justify-center px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 font-semibold rounded-full border border-gray-300 transition-all duration-200 shadow-md hover:shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        Go Home
                    </a>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    @yield('footer', '')
                    @if(!$__env->yieldContent('footer'))
                        <p class="text-sm text-gray-500">
                            Need help? 
                            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">Contact our support team</a>
                        </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
