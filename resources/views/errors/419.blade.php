@extends('layouts.app')

@section('title', '419 - Page Expired')

@section('content')
    <!-- 419 Error Page -->
    <div class="min-h-screen bg-gradient-to-br from-teal-50 via-cyan-50 to-blue-50 flex items-center justify-center px-4">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-20" style="background-image: radial-gradient(circle at 25% 25%, rgba(20, 184, 166, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);"></div>
        
        <div class="relative z-10 max-w-2xl mx-auto text-center">
            <!-- Error Card -->
            <div class="bg-white/80 backdrop-blur-lg rounded-3xl p-8 md:p-12 border border-white/50 shadow-2xl">
                <!-- Error Code -->
                <div class="mb-6">
                    <h1 class="text-8xl md:text-9xl font-bold bg-gradient-to-r from-teal-600 via-cyan-600 to-blue-600 bg-clip-text text-transparent">
                        419
                    </h1>
                </div>

                <!-- Error Icon -->
                <div class="mb-6">
                    <div class="w-24 h-24 mx-auto bg-gradient-to-r from-teal-100 to-cyan-100 rounded-full flex items-center justify-center">
                        <svg class="w-12 h-12 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Error Message -->
                <div class="mb-8">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                        Page Expired
                    </h2>
                    <p class="text-gray-600 text-lg leading-relaxed">
                        Your session has expired for security reasons. This happens when you've been inactive for a while or when your security token needs to be refreshed.
                    </p>
                </div>

                <!-- What This Means -->
                <div class="mb-8">
                    <div class="bg-teal-50 rounded-2xl p-6 border border-teal-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">What this means:</h3>
                        <ul class="text-left text-gray-700 space-y-2">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-teal-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Your security token has expired (this is normal)
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-teal-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                We protect your account with automatic timeouts
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-teal-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Simply refresh the page to continue safely
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="location.reload()" class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-teal-600 to-cyan-600 hover:from-teal-700 hover:to-cyan-700 text-white font-semibold rounded-full transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh Page
                    </button>
                    
                    <a href="{{ route('home') }}" class="inline-flex items-center justify-center px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 font-semibold rounded-full border border-gray-300 transition-all duration-200 shadow-md hover:shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        Go Home
                    </a>
                </div>

                <!-- Security Note -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="flex items-center justify-center text-sm text-gray-500">
                        <svg class="w-4 h-4 text-teal-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                        This security feature helps protect your account and personal information
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
