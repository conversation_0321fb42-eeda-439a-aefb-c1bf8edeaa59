@extends('layouts.app')

@section('title', '429 - Too Many Requests')

@section('content')
    <!-- 429 Error Page -->
    <div class="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center px-4">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-20" style="background-image: radial-gradient(circle at 25% 25%, rgba(244, 63, 94, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);"></div>
        
        <div class="relative z-10 max-w-2xl mx-auto text-center">
            <!-- Error Card -->
            <div class="bg-white/80 backdrop-blur-lg rounded-3xl p-8 md:p-12 border border-white/50 shadow-2xl">
                <!-- Error Code -->
                <div class="mb-6">
                    <h1 class="text-8xl md:text-9xl font-bold bg-gradient-to-r from-rose-600 via-pink-600 to-purple-600 bg-clip-text text-transparent">
                        429
                    </h1>
                </div>

                <!-- Error Icon -->
                <div class="mb-6">
                    <div class="w-24 h-24 mx-auto bg-gradient-to-r from-rose-100 to-pink-100 rounded-full flex items-center justify-center">
                        <svg class="w-12 h-12 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                        </svg>
                    </div>
                </div>

                <!-- Error Message -->
                <div class="mb-8">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                        Slow Down There!
                    </h2>
                    <p class="text-gray-600 text-lg leading-relaxed">
                        You're making requests too quickly. We've temporarily limited your access to protect our servers and ensure a smooth experience for everyone.
                    </p>
                </div>

                <!-- Rate Limit Info -->
                <div class="mb-8">
                    <div class="bg-rose-50 rounded-2xl p-6 border border-rose-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Why this happened:</h3>
                        <ul class="text-left text-gray-700 space-y-2">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-rose-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                You've exceeded our rate limit for requests
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-rose-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                This helps us maintain quality service for all users
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-rose-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Please wait a moment before trying again
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Countdown Timer -->
                <div class="mb-8">
                    <div class="bg-gradient-to-r from-rose-100 to-pink-100 rounded-2xl p-6 border border-rose-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Please wait:</h3>
                        <div class="text-3xl font-bold text-rose-600" id="countdown">60</div>
                        <p class="text-sm text-gray-600 mt-2">seconds before you can try again</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="location.reload()" id="retryBtn" disabled class="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-rose-400 to-pink-400 text-white font-semibold rounded-full transition-all duration-200 shadow-lg opacity-50 cursor-not-allowed">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Try Again
                    </button>
                    
                    <a href="{{ route('home') }}" class="inline-flex items-center justify-center px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 font-semibold rounded-full border border-gray-300 transition-all duration-200 shadow-md hover:shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        Go Home
                    </a>
                </div>

                <!-- Tips -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <p class="text-sm text-gray-500">
                        💡 <strong>Tip:</strong> Take your time browsing profiles and connecting with people. Quality connections are better than quantity!
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Countdown Script -->
    <script>
        let timeLeft = 60;
        const countdownElement = document.getElementById('countdown');
        const retryBtn = document.getElementById('retryBtn');
        
        const timer = setInterval(() => {
            timeLeft--;
            countdownElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                retryBtn.disabled = false;
                retryBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'from-rose-400', 'to-pink-400');
                retryBtn.classList.add('from-rose-600', 'to-pink-600', 'hover:from-rose-700', 'hover:to-pink-700');
                countdownElement.textContent = '0';
                countdownElement.parentElement.innerHTML = '<div class="text-green-600 font-semibold">✓ You can try again now!</div>';
            }
        }, 1000);
    </script>
@endsection
