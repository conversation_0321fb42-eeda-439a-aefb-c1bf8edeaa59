<form id="send-verification" method="post" action="{{ route('verification.send') }}">
    @csrf
</form>

<!-- Compact Form Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Personal Information</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Complete your profile to connect with amazing people</p>
</div>

    <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
        <form method="post" action="{{ route('profile.update') }}" class="space-y-0" enctype="multipart/form-data">
        @csrf
        @method('patch')
        <input type="hidden" name="current_tab" value="personal">

        <!-- 1. Profile Picture Section -->
        <div class="bg-white">
            <div class="p-4">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-base font-semibold text-gray-900">Profile Picture</h3>
                        <p class="text-xs text-gray-500">Upload a photo to personalize your profile</p>
                    </div>
                </div>

                <div class="flex flex-col lg:flex-row lg:items-center lg:space-x-6 space-y-4 lg:space-y-0">
                    <!-- Current Profile Picture -->
                    <div class="flex-shrink-0">
                        <div class="relative group">
                            @if($user->profile_picture)
                                <img id="profile-preview"
                                     src="{{ $user->profile_picture_url }}"
                                     alt="Profile Picture"
                                     class="h-24 w-24 rounded-xl object-cover border-2 border-white shadow-md ring-2 ring-gray-200 group-hover:ring-purple-300 transition-all duration-300">
                            @else
                                <div id="profile-preview-placeholder" class="h-24 w-24 rounded-xl border-2 border-white shadow-md bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center ring-2 ring-gray-200 group-hover:ring-purple-300 transition-all duration-300">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <img id="profile-preview" src="" alt="Profile Picture" class="h-24 w-24 rounded-xl object-cover border-2 border-white shadow-md ring-2 ring-gray-200 hidden">
                            @endif
                            <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Section -->
                    <div class="flex-1 space-y-3">
                        <div>
                            <label for="profile_picture" class="block text-sm font-medium text-gray-700 mb-1">
                                Upload New Picture
                            </label>
                            <input type="file"
                                   id="profile_picture"
                                   name="profile_picture"
                                   accept="image/*"
                                   class="block w-full text-sm text-gray-500 file:mr-3 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-gradient-to-r file:from-purple-500 file:to-pink-500 file:text-white hover:file:from-purple-600 hover:file:to-pink-600 file:cursor-pointer file:transition-all file:duration-200"
                                   onchange="previewImage(this)">
                        </div>

                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-xs text-gray-600 mb-1 font-medium">Photo Guidelines:</p>
                            <p class="text-xs text-gray-500">PNG, JPG, GIF • Max 2MB • Recommended: 400x400px</p>
                        </div>

                        <x-input-error class="mt-2" :messages="$errors->get('profile_picture')" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. Personal Details Section (Full Name, DOB, Gender) -->
        <div class="bg-white">
            <div class="p-3 border-t border-gray-100">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-base font-semibold text-gray-900">Personal Details</h3>
                        <p class="text-xs text-gray-500">Your basic information</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <!-- Full Name -->
                    <div class="space-y-1">
                        <label for="name" class="block text-sm font-medium text-gray-700">
                            {{ __('Full Name') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input id="name"
                                   name="name"
                                   type="text"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white placeholder-gray-400 text-sm"
                                   value="{{ old('name', $user->name) }}"
                                   required
                                   autofocus
                                   autocomplete="name"
                                   placeholder="Enter your full name">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <x-input-error class="mt-1" :messages="$errors->get('name')" />
                    </div>

                    <!-- Date of Birth -->
                    <div class="space-y-1">
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700">
                            {{ __('Date of Birth') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input id="date_of_birth"
                                   name="date_of_birth"
                                   type="date"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white text-gray-900 text-sm"
                                   value="{{ old('date_of_birth', $user->date_of_birth?->format('Y-m-d')) }}"

                                   required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <x-input-error class="mt-1" :messages="$errors->get('date_of_birth')" />
                    </div>

                    <!-- Gender -->
                    <div class="space-y-1">
                        <label for="gender" class="block text-sm font-medium text-gray-700">
                            {{ __('Gender') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <select id="gender"
                                    name="gender"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white appearance-none cursor-pointer text-sm"
                                    required>
                                <option value="">{{ __('Select gender') }}</option>
                                <option value="male" @selected(old('gender', $user->gender) == 'male')>{{ __('Male') }}</option>
                                <option value="female" @selected(old('gender', $user->gender) == 'female')>{{ __('Female') }}</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <x-input-error class="mt-1" :messages="$errors->get('gender')" />
                    </div>
                </div>


            </div>
        </div>

        <!-- 3. Contact Information Section (Email, Contact) -->
        <div class="bg-white">
            <div class="p-3 border-t border-gray-100">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-base font-semibold text-gray-900">Contact Information</h3>
                        <p class="text-xs text-gray-500">Your email and contact details</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- Email Address (Read-only) -->
                    <div class="space-y-1">
                        <div class="flex items-center justify-between">
                            <label for="email" class="block text-sm font-medium text-gray-700">
                                {{ __('Email Address') }}
                            </label>
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Verified
                            </span>
                        </div>
                        <div class="relative">
                            <input id="email"
                                   name="email"
                                   type="email"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed text-gray-600 text-sm"
                                   value="{{ old('email', $user->email) }}"
                                   readonly
                                   autocomplete="username">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500">Managed through your Google account</p>
                        <x-input-error class="mt-1" :messages="$errors->get('email')" />
                    </div>

                    <!-- Contact Number -->
                    <div class="space-y-1">
                        <label for="contact_number" class="block text-sm font-medium text-gray-700">
                            {{ __('Contact Number') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input id="contact_number"
                                   name="contact_number"
                                   type="tel"
                                   class="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white placeholder-gray-400 text-sm"
                                   value="{{ old('contact_number', $user->contact_number) }}"
                                   required
                                   autocomplete="tel"
                                   placeholder="98765 43210">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-xs">+91</span>
                            </div>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                        </div>
                        <x-input-error class="mt-1" :messages="$errors->get('contact_number')" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. Interests Section -->
        <div class="bg-white">
            <div class="p-3 border-t border-gray-100">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-amber-500 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-base font-semibold text-gray-900">Interests & Hobbies</h3>
                        <p class="text-xs text-gray-500">Share what you love to do in your free time</p>
                    </div>
                </div>

                <!-- Display current interests as tags -->
                <div id="interests-display" class="mb-3">
                    @if($user->interests)
                        <div class="flex flex-wrap gap-2">
                            @foreach($user->interests_array as $interest)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 border border-orange-200 shadow-sm hover:shadow-md transition-all duration-200">
                                    <svg class="w-3 h-3 mr-1 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $interest }}
                                    <button type="button" onclick="removeInterest('{{ $interest }}')" class="ml-1 text-orange-600 hover:text-orange-800 transition-colors hover:bg-orange-200 rounded-full p-0.5">
                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </span>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                            <svg class="w-6 h-6 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <p class="text-gray-600 text-sm font-medium">No interests added yet</p>
                            <p class="text-gray-500 text-xs">Add some to help others find you!</p>
                        </div>
                    @endif
                </div>

                <!-- Input for adding new interests -->
                <div class="space-y-3">
                    <div class="flex space-x-2">
                        <div class="flex-1 relative">
                            <input type="text"
                                   id="interest-input"
                                   placeholder="Type an interest (e.g., Photography, Hiking...)"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-white placeholder-gray-400 text-sm"
                                   onkeypress="handleInterestInput(event)"
                                   onkeyup="handleCommaInput(event)">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                        </div>
                        <button type="button"
                                onclick="addInterest()"
                                class="px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md text-sm">
                            Add
                        </button>
                    </div>


                </div>

                <!-- Hidden input to store all interests -->
                <textarea id="interests" name="interests" class="hidden">{{ old('interests', $user->interests) }}</textarea>
                <x-input-error class="mt-2" :messages="$errors->get('interests')" />
            </div>
        </div>

        <!-- 5. Expectations Section -->
        <div class="bg-white">
            <div class="p-3 border-t border-gray-100">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-rose-500 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-base font-semibold text-gray-900">Partner Expectations</h3>
                        <p class="text-xs text-gray-500">Describe what you're looking for in a relationship</p>
                    </div>
                </div>

                <div class="space-y-2">
                    <div>
                        <label for="expectation" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ __('What are you looking for in a partner?') }}
                        </label>
                        <div class="relative">
                            <textarea id="expectation"
                                      name="expectation"
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-all duration-200 bg-white resize-none placeholder-gray-400 text-sm"
                                      placeholder="Describe what you're looking for in a partner... (e.g., shared values, interests, lifestyle preferences)"
                                      maxlength="1000">{{ old('expectation', $user->expectation) }}</textarea>
                            <div class="absolute bottom-2 right-2 text-xs text-gray-400" id="expectation-counter">
                                <span id="expectation-count">{{ strlen(old('expectation', $user->expectation ?? '')) }}</span>/1000
                            </div>
                        </div>
                    </div>



                    <x-input-error class="mt-1" :messages="$errors->get('expectation')" />
                </div>
            </div>
        </div>

        <!-- 6. Save Button Section -->
        <div class="bg-white">
            <div class="p-3 border-t border-gray-100">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-3">
                        <button type="submit" class="inline-flex items-center justify-center px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md text-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('Save Changes') }}
                        </button>

                        @if (session('status') === 'profile-updated')
                            <div id="profile-success-message" class="flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-lg border border-green-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="font-medium text-sm">{{ __('Profile updated successfully!') }}</span>
                            </div>
                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    const successMessage = document.getElementById('profile-success-message');
                                    if (successMessage) {
                                        setTimeout(() => {
                                            successMessage.style.transition = 'opacity 0.5s ease';
                                            successMessage.style.opacity = '0';
                                            setTimeout(() => {
                                                if (successMessage.parentNode) {
                                                    successMessage.parentNode.removeChild(successMessage);
                                                }
                                            }, 500);
                                        }, 3000);
                                    }
                                });
                            </script>
                        @endif
                    </div>

                    <div class="text-center sm:text-right">
                        <div class="flex items-center justify-center sm:justify-end text-xs text-gray-500">
                            <svg class="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                            Secure & Private
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>

    <script>
        // Profile picture preview
        function previewImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('profile-preview');
                    const placeholder = document.getElementById('profile-preview-placeholder');

                    // Hide placeholder and show image
                    if (placeholder) {
                        placeholder.style.display = 'none';
                    }

                    preview.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Interests management
        let interests = [];

        // Initialize interests from existing data
        document.addEventListener('DOMContentLoaded', function() {
            const existingInterests = document.getElementById('interests').value;
            if (existingInterests) {
                interests = existingInterests.split(',').map(i => i.trim()).filter(i => i);
            }
        });

        function addInterest() {
            const input = document.getElementById('interest-input');
            const interest = input.value.trim();

            if (interest && !interests.includes(interest)) {
                interests.push(interest);
                updateInterestsDisplay();
                updateHiddenInput();
                input.value = '';
            }
        }

        function removeInterest(interest) {
            interests = interests.filter(i => i !== interest);
            updateInterestsDisplay();
            updateHiddenInput();
        }

        function handleInterestInput(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                addInterest();
            }
        }

        function handleCommaInput(event) {
            const input = event.target;
            if (input.value.includes(',')) {
                const parts = input.value.split(',');
                const newInterest = parts[0].trim();

                if (newInterest && !interests.includes(newInterest)) {
                    interests.push(newInterest);
                    updateInterestsDisplay();
                    updateHiddenInput();
                }

                input.value = parts.slice(1).join(',').trim();
            }
        }

        function updateInterestsDisplay() {
            const display = document.getElementById('interests-display');
            if (interests.length === 0) {
                display.innerHTML = `
                    <div class="text-center py-8 border-2 border-dashed border-orange-200 rounded-xl bg-orange-50/50">
                        <svg class="w-12 h-12 text-orange-300 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        <p class="text-orange-600 text-sm font-medium">No interests added yet</p>
                        <p class="text-orange-500 text-xs">Add some to help others find you!</p>
                    </div>
                `;
            } else {
                display.innerHTML = interests.map(interest => `
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 mr-2 mb-2 border border-orange-200 shadow-sm">
                        ${interest}
                        <button type="button" onclick="removeInterest('${interest}')" class="ml-2 text-orange-600 hover:text-orange-800 transition-colors">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </span>
                `).join('');
            }
        }

        function updateHiddenInput() {
            document.getElementById('interests').value = interests.join(', ');
        }

        // Expectation character counter
        document.addEventListener('DOMContentLoaded', function() {
            const expectationTextarea = document.getElementById('expectation');
            const expectationCount = document.getElementById('expectation-count');

            if (expectationTextarea && expectationCount) {
                expectationTextarea.addEventListener('input', function() {
                    const currentLength = this.value.length;
                    expectationCount.textContent = currentLength;

                    // Change color based on character count
                    const counter = document.getElementById('expectation-counter');
                    if (currentLength > 900) {
                        counter.classList.add('text-red-500');
                        counter.classList.remove('text-gray-400');
                    } else if (currentLength > 800) {
                        counter.classList.add('text-yellow-500');
                        counter.classList.remove('text-gray-400', 'text-red-500');
                    } else {
                        counter.classList.add('text-gray-400');
                        counter.classList.remove('text-red-500', 'text-yellow-500');
                    }
                });
            }
        });


    </script>
