@php
    $averageRating = $user->getAverageRating();
    $totalReviews = $user->getTotalReviewsCount();
    $ratingDistribution = $user->getRatingDistribution();
@endphp

<div class="space-y-6">
    <!-- Reviews Header -->
    <div class="text-center">
        <h3 class="text-2xl font-bold text-gray-900 mb-2">Reviews & Ratings</h3>
        <p class="text-gray-600">See what others are saying about your services</p>
    </div>

    @if($totalReviews > 0)
        <!-- Rating Summary -->
        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
            <div class="text-center">
                <div class="text-4xl font-bold text-gray-900 mb-2">{{ number_format($averageRating, 1) }}</div>
                <div class="flex justify-center mb-2">
                    {!! $user->getStarRatingHtml() !!}
                </div>
                <p class="text-gray-600">Based on {{ $totalReviews }} review{{ $totalReviews > 1 ? 's' : '' }}</p>
            </div>

            <!-- Rating Distribution -->
            <div class="mt-6 space-y-2">
                @for($i = 5; $i >= 1; $i--)
                    @php
                        $count = $ratingDistribution[$i] ?? 0;
                        $percentage = $totalReviews > 0 ? ($count / $totalReviews) * 100 : 0;
                    @endphp
                    <div class="flex items-center space-x-3">
                        <span class="text-sm font-medium text-gray-700 w-8">{{ $i }} ★</span>
                        <div class="flex-1 bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-400 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                        </div>
                        <span class="text-sm text-gray-600 w-8">{{ $count }}</span>
                    </div>
                @endfor
            </div>
        </div>

        <!-- Reviews List -->
        <div class="space-y-4" id="reviewsList">
            <div class="flex justify-between items-center">
                <h4 class="text-lg font-semibold text-gray-900">Recent Reviews</h4>
                <button onclick="loadAllReviews()" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    View All Reviews
                </button>
            </div>
            
            <div id="reviewsContainer" class="space-y-4">
                <!-- Reviews will be loaded here via JavaScript -->
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="text-gray-600 mt-2">Loading reviews...</p>
                </div>
            </div>
        </div>
    @else
        <!-- No Reviews State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Reviews Yet</h3>
            <p class="text-gray-600 mb-4">You haven't received any reviews yet. Complete some bookings to start receiving feedback!</p>
            
            <!-- Pending Reviews Section -->
            <div class="mt-8">
                <h4 class="text-md font-semibold text-gray-900 mb-4">Pending Reviews</h4>
                <p class="text-sm text-gray-600 mb-4">You can review these completed meetings:</p>
                <div id="pendingReviewsContainer">
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                        <p class="text-gray-600 mt-2 text-sm">Loading pending reviews...</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($totalReviews > 0)
        loadReviews();
    @else
        loadPendingReviews();
    @endif
});

async function loadReviews() {
    try {
        const response = await fetch(`/reviews/user/{{ $user->id }}`);
        const data = await response.json();
        
        if (data.success && data.reviews.data.length > 0) {
            displayReviews(data.reviews.data);
        } else {
            document.getElementById('reviewsContainer').innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <p>No reviews to display.</p>
                </div>
            `;
        }
    } catch (error) {
        document.getElementById('reviewsContainer').innerHTML = `
            <div class="text-center py-8 text-red-500">
                <p>Failed to load reviews. Please try again.</p>
            </div>
        `;
    }
}

async function loadPendingReviews() {
    try {
        const response = await fetch('/reviews/pending');
        const data = await response.json();
        
        if (data.success && data.pending_reviews.length > 0) {
            displayPendingReviews(data.pending_reviews);
        } else {
            document.getElementById('pendingReviewsContainer').innerHTML = `
                <div class="text-center py-4 text-gray-500">
                    <p class="text-sm">No pending reviews available.</p>
                </div>
            `;
        }
    } catch (error) {
        document.getElementById('pendingReviewsContainer').innerHTML = `
            <div class="text-center py-4 text-red-500">
                <p class="text-sm">Failed to load pending reviews.</p>
            </div>
        `;
    }
}

function displayReviews(reviews) {
    const container = document.getElementById('reviewsContainer');
    
    if (reviews.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <p>No reviews to display.</p>
            </div>
        `;
        return;
    }
    
    const reviewsHtml = reviews.map(review => `
        <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div class="flex items-start space-x-3">
                <img src="${review.is_anonymous ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMCIgeT0iMTAiPgo8cGF0aCBkPSJNMTMgNkEzIDMgMCAxIDEgNyA2QTMgMyAwIDAgMSAxMyA2Wk0xMCAxMUE1IDUgMCAwIDAgNSAxNkgxNUE1IDUgMCAwIDAgMTAgMTFaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K' : (review.reviewer.profile_picture_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMCIgeT0iMTAiPgo8cGF0aCBkPSJNMTMgNkEzIDMgMCAxIDEgNyA2QTMgMyAwIDAgMSAxMyA2Wk0xMCAxMUE1IDUgMCAwIDAgNSAxNkgxNUE1IDUgMCAwIDAgMTAgMTFaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K')}"
                     alt="${review.is_anonymous ? 'Anonymous' : review.reviewer.name}"
                     class="w-10 h-10 rounded-full">
                <div class="flex-1">
                    <div class="flex items-center justify-between mb-2">
                        <div>
                            <h5 class="font-semibold text-gray-900">
                                ${review.is_anonymous ? 'Anonymous User' : review.reviewer.name}
                            </h5>
                            <div class="flex items-center space-x-2">
                                <div class="flex">
                                    ${generateStarRating(review.rating)}
                                </div>
                                <span class="text-sm text-gray-600">${review.rating}/5</span>
                            </div>
                        </div>
                        <span class="text-xs text-gray-500">${formatDate(review.created_at)}</span>
                    </div>
                    ${review.review_text ? `<p class="text-gray-700 text-sm">${review.review_text}</p>` : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = reviewsHtml;
}

function displayPendingReviews(pendingReviews) {
    const container = document.getElementById('pendingReviewsContainer');
    
    if (pendingReviews.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4 text-gray-500">
                <p class="text-sm">No pending reviews available.</p>
            </div>
        `;
        return;
    }
    
    const pendingHtml = pendingReviews.map(pending => `
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="${pending.other_user.profile_picture_url}" 
                         alt="${pending.other_user.name}" 
                         class="w-8 h-8 rounded-full">
                    <div>
                        <p class="font-medium text-gray-900 text-sm">${pending.other_user.name}</p>
                        <p class="text-xs text-gray-600">${pending.booking_date} at ${pending.booking_time}</p>
                    </div>
                </div>
                <a href="/rating/${pending.booking_id}" 
                   class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors">
                    Rate & Review
                </a>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = pendingHtml;
}

function generateStarRating(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
        } else {
            stars += '<svg class="w-4 h-4 text-gray-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
        }
    }
    return stars;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}

function loadAllReviews() {
    // This could open a modal or navigate to a dedicated reviews page
    alert('Load all reviews functionality can be implemented here');
}
</script>
