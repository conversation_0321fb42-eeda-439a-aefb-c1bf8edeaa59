<!-- Compact Couple Activity Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-pink-600 to-rose-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Couple Activity</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Connect with partners for couple activities and experiences</p>
</div>

<div class="bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="space-y-4 p-4">
        <!-- Information Notice -->
        @if(!$user->is_couple_activity_enabled)
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h3 class="text-base font-semibold text-blue-900 mb-2">Enable Couple Activity Service</h3>
                        <p class="text-sm text-blue-800 mb-2">
                            To activate your Couple Activity service, please enable "Couple Activity" in the settings below.
                        </p>
                        <p class="text-xs text-blue-700">
                            Once enabled, you can search for partners and receive activity requests from other users.
                        </p>
                    </div>
                </div>
            </div>
        @else
            <!-- Current Status Card -->
            <div class="bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg p-4 border border-pink-100">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-base font-semibold text-gray-900 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Service Status
                    </h3>
                    <div class="flex items-center">
                        @if($user->is_couple_activity_enabled)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Inactive
                            </span>
                        @endif
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div class="bg-white rounded-lg p-3 border border-gray-100">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-pink-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-600">Partner Status</p>
                                <p class="text-sm font-semibold text-gray-900">
                                    @if($user->hasOwnPartner())
                                        Has Partner
                                    @else
                                        No Partner
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-3 border border-gray-100">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-pink-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-600">Service Status</p>
                                <p class="text-sm font-semibold text-gray-900">
                                    {{ $user->is_couple_activity_enabled ? 'Available' : 'Not Available' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Your Partner Section -->
            @php
                $ownPartner = $user->getOwnPartner();
            @endphp
            @if($ownPartner)
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        Your Partner
                    </h4>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm mr-3">
                                {{ substr($ownPartner->name, 0, 1) }}
                            </div>
                            <div>
                                <h5 class="font-semibold text-gray-900 text-sm">{{ $ownPartner->name }}</h5>
                                <p class="text-xs text-gray-600">{{ ucfirst($ownPartner->gender) }}</p>
                            </div>
                        </div>
                        <form action="{{ route('couple-activity.end-partnership') }}" method="POST" class="inline">
                            @csrf
                            <button type="submit"
                                    class="px-3 py-1.5 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-xs"
                                    onclick="return confirm('Are you sure you want to end this partnership?')">
                                💔 End Partnership
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Couple Activity Partners Section -->
                @php
                    $coupleActivityPartners = $user->getCoupleActivityPartners();
                @endphp
                @if(count($coupleActivityPartners) > 0)
                    <div class="bg-white rounded-lg p-4 border border-gray-200">
                        <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Activity Partners
                            <span class="ml-2 px-2 py-0.5 bg-pink-100 text-pink-700 text-xs font-medium rounded-full">
                                {{ count($coupleActivityPartners) }}
                            </span>
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            @foreach($coupleActivityPartners as $activityPartner)
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-100">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-400 to-pink-500 flex items-center justify-center text-white font-semibold text-xs mr-3">
                                        {{ substr($activityPartner->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h6 class="font-semibold text-gray-900 text-sm">{{ $activityPartner->name }}</h6>
                                        <p class="text-xs text-gray-600">{{ ucfirst($activityPartner->gender) }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
        @endif

            <!-- Pending Requests Section -->
            @php
                $pendingOwnPartnerRequests = $user->getPendingOwnPartnerRequests();
                $pendingCoupleSwapRequests = $user->getPendingCoupleSwapRequests();
            @endphp

            @if($pendingOwnPartnerRequests->count() > 0)
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Pending Partner Requests
                        <span class="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-700 text-xs font-medium rounded-full">
                            {{ $pendingOwnPartnerRequests->count() }}
                        </span>
                    </h4>
                    <div class="space-y-3">
                        @foreach($pendingOwnPartnerRequests as $request)
                            <div class="p-3 bg-gray-50 rounded-lg border border-gray-100">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center text-white font-semibold text-xs mr-3">
                                        {{ substr($request->requester->name, 0, 1) }}
                                    </div>
                                    <div class="flex-1">
                                        <h6 class="font-semibold text-gray-900 text-sm">{{ $request->requester->name }}</h6>
                                        <p class="text-xs text-gray-600">{{ ucfirst($request->requester->gender) }} • Wants to be your partner</p>
                                        @if($request->message)
                                            <p class="text-xs text-gray-500 mt-1">"{{ $request->message }}"</p>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <form action="{{ route('couple-activity.approve', $request) }}" method="POST" class="flex-1 min-w-0">
                                        @csrf
                                        <button type="submit" class="w-full px-2 py-1.5 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 transition-colors text-xs">
                                            ✓ Accept
                                        </button>
                                    </form>
                                    <form action="{{ route('couple-activity.reject', $request) }}" method="POST" class="flex-1 min-w-0">
                                        @csrf
                                        <button type="submit" class="w-full px-2 py-1.5 bg-yellow-500 text-white font-medium rounded-md hover:bg-yellow-600 transition-colors text-xs">
                                            ⚠ Decline
                                        </button>
                                    </form>
                                    <form action="{{ route('couple-activity.block', $request) }}" method="POST" class="flex-1 min-w-0">
                                        @csrf
                                        <button type="submit"
                                                class="w-full px-2 py-1.5 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 transition-colors text-xs"
                                                onclick="return confirm('Are you sure you want to block this user? They will not be able to send you requests again.')">
                                            🚫 Block
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            @if($pendingCoupleSwapRequests->count() > 0)
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Pending Activity Requests
                        <span class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                            {{ $pendingCoupleSwapRequests->count() }}
                        </span>
                    </h4>
                    <div class="space-y-3">
                        @foreach($pendingCoupleSwapRequests as $request)
                            <div class="p-3 bg-gray-50 rounded-lg border border-gray-100">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-400 to-pink-500 flex items-center justify-center text-white font-semibold text-xs mr-3">
                                        {{ substr($request->requester->name, 0, 1) }}
                                    </div>
                                    <div class="flex-1">
                                        <h6 class="font-semibold text-gray-900 text-sm">{{ $request->requester->name }}</h6>
                                        <p class="text-xs text-gray-600">{{ ucfirst($request->requester->gender) }} • Couple activity request</p>
                                        @if($request->message)
                                            <p class="text-xs text-gray-500 mt-1">"{{ $request->message }}"</p>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    <form action="{{ route('couple-activity.approve', $request) }}" method="POST" class="flex-1 min-w-0">
                                        @csrf
                                        <button type="submit" class="w-full px-2 py-1.5 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 transition-colors text-xs">
                                            ✓ Accept
                                        </button>
                                    </form>
                                    <form action="{{ route('couple-activity.reject', $request) }}" method="POST" class="flex-1 min-w-0">
                                        @csrf
                                        <button type="submit" class="w-full px-2 py-1.5 bg-yellow-500 text-white font-medium rounded-md hover:bg-yellow-600 transition-colors text-xs">
                                            ⚠ Decline
                                        </button>
                                    </form>
                                    <form action="{{ route('couple-activity.block', $request) }}" method="POST" class="flex-1 min-w-0">
                                        @csrf
                                        <button type="submit"
                                                class="w-full px-2 py-1.5 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 transition-colors text-xs"
                                                onclick="return confirm('Are you sure you want to block this user? They will not be able to send you requests again.')">
                                            🚫 Block
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Blocked Users Section -->
            @php
                $blockedUsers = $user->getBlockedUsers();
            @endphp
            @if($blockedUsers->count() > 0)
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                        Blocked Users
                        <span class="ml-2 px-2 py-0.5 bg-red-100 text-red-700 text-xs font-medium rounded-full">
                            {{ $blockedUsers->count() }}
                        </span>
                    </h4>
                    <p class="text-gray-600 mb-3 text-xs">Users you have blocked from sending partner requests. You can unblock them to allow future requests.</p>
                    <div class="space-y-2">
                        @foreach($blockedUsers as $blockedUser)
                            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-red-400 to-pink-500 flex items-center justify-center text-white font-semibold text-xs mr-3">
                                        {{ substr($blockedUser->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h6 class="font-semibold text-gray-900 text-sm">{{ $blockedUser->name }}</h6>
                                        <p class="text-xs text-gray-600">{{ ucfirst($blockedUser->gender) }} • Blocked from sending requests</p>
                                    </div>
                                </div>
                                <form action="{{ route('couple-activity.unblock') }}" method="POST" class="inline">
                                    @csrf
                                    <input type="hidden" name="blocked_user_id" value="{{ $blockedUser->id }}">
                                    <button type="submit"
                                            class="px-3 py-1.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-xs"
                                            onclick="return confirm('Are you sure you want to unblock {{ $blockedUser->name }}? They will be able to send you partner requests again.')">
                                        🔓 Unblock
                                    </button>
                                </form>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Add Partner Section -->
            @if(!$ownPartner)
                @php
                    $sentRequest = $user->sentCoupleActivityRequests()->where('type', 'own_partner')->where('status', 'pending')->first();
                @endphp

                @if(!$sentRequest)
                    <div class="bg-white rounded-lg p-4 border border-gray-200">
                        <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Your Partner
                        </h4>
                        <p class="text-gray-600 mb-3 text-xs">Search and add someone as your partner for couple activities.</p>

                        <div class="space-y-3">
                            <div>
                                <label for="partner_search" class="block text-sm font-medium text-gray-700 mb-2">
                                    Search for a user
                                </label>
                                <div class="relative">
                                    <input type="text"
                                           id="partner_search"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-sm"
                                           placeholder="Type name or email to search..."
                                           autocomplete="off">
                                    <div id="search_results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 hidden max-h-60 overflow-y-auto"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-white rounded-lg p-4 border border-gray-200">
                        <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Partner Request Sent
                        </h4>
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm mr-3">
                                    {{ substr($sentRequest->partner->name, 0, 1) }}
                                </div>
                                <div>
                                    <h5 class="font-semibold text-gray-900 text-sm">{{ $sentRequest->partner->name }}</h5>
                                    <p class="text-xs text-gray-600">{{ ucfirst($sentRequest->partner->gender) }} • Request pending</p>
                                    @if($sentRequest->message)
                                        <p class="text-xs text-gray-500 mt-1">"{{ $sentRequest->message }}"</p>
                                    @endif
                                </div>
                            </div>
                            <form action="{{ route('couple-activity.cancel-request', $sentRequest) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="px-3 py-1.5 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-xs"
                                        onclick="return confirm('Are you sure you want to cancel this request?')">
                                    🗑️ Cancel Request
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            @endif

    @endif

    <!-- Settings Section -->
    <div class="bg-white rounded-lg p-4 border border-gray-200">
        <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
            <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Activity Settings
        </h4>
        <form action="{{ route('profile.couple-activity.update') }}" method="POST" class="space-y-3">
            @csrf
            @method('PATCH')
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <h5 class="font-semibold text-gray-900 text-sm">Enable Couple Activity Service</h5>
                    <p class="text-xs text-gray-600">Allow others to find you and send partner requests</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox"
                           name="is_couple_activity_enabled"
                           value="1"
                           class="sr-only peer"
                           {{ $user->is_couple_activity_enabled ? 'checked' : '' }}>
                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-pink-600"></div>
                </label>
            </div>

            <div class="flex justify-end">
                <button type="submit"
                        class="px-4 py-2 bg-pink-600 text-white font-medium rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-sm">
                    Save Settings
                </button>
            </div>
        </form>
    </div>

        <!-- Success/Error Messages -->
        @if (session('success'))
            <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <p class="text-xs text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        @endif

        @if (session('error'))
            <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    <p class="text-xs text-red-800">{{ session('error') }}</p>
                </div>
            </div>
        @endif
    </div>

    <!-- AJAX Search Script -->
    <script>
        let searchTimeout;
        const searchInput = document.getElementById('partner_search');
        const searchResults = document.getElementById('search_results');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    searchResults.classList.add('hidden');
                    return;
                }

                searchTimeout = setTimeout(() => {
                    fetch(`/couple-activity/search-users?q=${encodeURIComponent(query)}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            console.error('Search error:', data.error);
                            searchResults.innerHTML = '<div class="p-3 text-red-500 text-sm">' + data.error + '</div>';
                            searchResults.classList.remove('hidden');
                        } else {
                            displaySearchResults(data.users);
                        }
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                    });
                }, 300);
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                    searchResults.classList.add('hidden');
                }
            });
        }

        function displaySearchResults(users) {
            if (users.length === 0) {
                searchResults.innerHTML = '<div class="p-3 text-gray-500 text-sm">No users found</div>';
            } else {
                searchResults.innerHTML = users.map(user => `
                    <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                         onclick="selectUser(${user.id}, '${user.name}', '${user.gender}')">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm mr-3">
                                ${user.name.charAt(0)}
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">${user.name}</div>
                                <div class="text-sm text-gray-500">${user.email} • ${user.gender}</div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            searchResults.classList.remove('hidden');
        }

        function selectUser(userId, userName, userGender) {
            // Show confirmation modal
            if (confirm(`Send partner request to ${userName}?`)) {
                // Send the request
                fetch('/couple-activity/send-request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        partner_id: userId,
                        type: 'own_partner',
                        message: ''
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        // Reload the page to show the updated state
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showToast(data.message || 'Error sending request', 'error');
                    }
                })
                .catch(error => {
                    showToast('Error sending request', 'error');
                });
            }

            // Hide search results
            searchResults.classList.add('hidden');
            searchInput.value = '';
        }
    </script>
</div>
