<section class="space-y-6">
    <div class="mb-6">
        <p class="text-gray-600">
            {{ __('Once your account is deleted, all of its resources and data will be permanently deleted. This action cannot be undone.') }}
        </p>
    </div>

    <!-- Warning Box -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-semibold text-red-800 mb-1">This action is irreversible</h3>
                <p class="text-red-700 text-sm">All your profile information and data will be permanently deleted.</p>
            </div>
        </div>
    </div>

    <button x-data=""
            x-on:click.prevent="$dispatch('open-modal', 'confirm-user-deletion')"
            class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200">
        {{ __('Delete My Account') }}
    </button>

    <x-modal name="confirm-user-deletion" :show="$errors->userDeletion->isNotEmpty()" focusable>
        <form method="post" action="{{ route('profile.destroy') }}" class="p-6">
            @csrf
            @method('delete')

            <div class="text-center mb-6">
                <h2 class="text-lg font-bold text-gray-900 mb-2">
                    {{ __('Delete Account?') }}
                </h2>
                <p class="text-gray-600">
                    {{ __('This will permanently delete your account and all associated data. This action cannot be undone.') }}
                </p>
            </div>

            <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-6">
                <p class="text-red-800 text-sm text-center">
                    {{ __('Since you use Google authentication, no password is required for account deletion.') }}
                </p>
            </div>

            <div class="flex justify-center space-x-3">
                <button type="button"
                        x-on:click="$dispatch('close')"
                        class="px-4 py-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 font-medium rounded-lg transition-all duration-200">
                    {{ __('Cancel') }}
                </button>

                <button type="submit"
                        class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200">
                    {{ __('Yes, Delete Account') }}
                </button>
            </div>
        </form>
    </x-modal>
</section>
