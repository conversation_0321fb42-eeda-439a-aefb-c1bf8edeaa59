<div class="space-y-6">
    <!-- Hide these sections - they should not be visible in Time Spending tab -->
    <div style="display: none;">
        <!-- Current Subscription Status - HIDDEN -->
        <!-- Subscription History - HIDDEN -->
        <!-- Available Plans - HIDDEN -->
    </div>

    <!-- Inline Subscription Plans Container (Hidden by default) -->
    <div id="inline-subscription-plans-section" class="bg-white rounded-lg border border-gray-200 shadow-sm" style="display: none;">
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-lg font-semibold text-gray-900">Choose Your Subscription Plan</h4>
                    <p class="text-sm text-gray-500 mt-1">Select the perfect plan for your needs</p>
                </div>
                <button onclick="hideInlineSubscriptionPlans()" class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
            </div>
        </div>

        <div class="p-4">
            <div class="flex justify-center items-center space-x-4 mb-4">
                <div class="flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs font-medium">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span>Secure Payment</span>
                </div>
                <div class="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs font-medium">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Instant Activation</span>
                </div>
                <div class="flex items-center space-x-1 px-2 py-1 bg-orange-100 text-orange-800 rounded-md text-xs font-medium">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75c0-1.856-.5-3.596-1.372-5.096L12 2.25z"></path>
                    </svg>
                    <span>24/7 Support</span>
                </div>
            </div>

            <div id="inline-subscription-plans-container">
                <div class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
                    <h6 class="text-gray-600 font-medium">Loading subscription plans...</h6>
                    <p class="text-gray-500 text-sm">Please wait while we fetch the latest plans for you</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
@endpush

<script>
function showSubscriptionPlans() {
    // Ensure modal is properly initialized
    const modalElement = document.getElementById('subscriptionPlansModal');
    if (!modalElement) {
        console.error('Subscription modal not found');
        return;
    }

    const modal = new bootstrap.Modal(modalElement, {
        backdrop: false, // Disable backdrop to prevent overlay issues
        keyboard: true,
        focus: true
    });

    // Show modal
    modal.show();

    // Load plans after modal is shown
    setTimeout(function() {
        loadSubscriptionPlansInModal();
    }, 300);
}

function showInlineSubscriptionPlans() {
    console.log('Showing inline subscription plans...');

    // Show the main subscription plans section that was hidden on page load
    const subscriptionSection = document.getElementById('subscription-plans-section');
    if (subscriptionSection) {
        subscriptionSection.style.display = 'block';
        console.log('Shown main subscription plans section');
    }

    // Hide ONLY Time Spending Settings form (the actual form with availability settings, rates, etc.)
    const timeSpendingForm = document.querySelector('#time-spending-content form');
    if (timeSpendingForm) {
        timeSpendingForm.style.display = 'none';
        console.log('Hidden Time Spending Settings form');
    }

    // Show inline plans section
    const inlinePlansSection = document.getElementById('inline-subscription-plans-section');
    if (inlinePlansSection) {
        inlinePlansSection.style.display = 'block';
        console.log('Shown inline subscription plans section');

        // Smooth scroll to the plans section
        inlinePlansSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Load plans
        setTimeout(function() {
            loadInlineSubscriptionPlans();
        }, 300);
    }
}

function hideInlineSubscriptionPlans() {
    console.log('Hiding inline subscription plans...');

    // Hide the main subscription plans section
    const subscriptionSection = document.getElementById('subscription-plans-section');
    if (subscriptionSection) {
        subscriptionSection.style.display = 'none';
        console.log('Hidden main subscription plans section');
    }

    // Show ONLY Time Spending Settings form (restore the form with availability settings, rates, etc.)
    const timeSpendingForm = document.querySelector('#time-spending-content form');
    if (timeSpendingForm) {
        timeSpendingForm.style.display = 'block';
        console.log('Restored Time Spending Settings form');
    }

    // Hide inline plans section
    const inlinePlansSection = document.getElementById('inline-subscription-plans-section');
    if (inlinePlansSection) {
        inlinePlansSection.style.display = 'none';
        console.log('Hidden inline subscription plans section');
    }
}

function loadSubscriptionPlansInModal() {
    console.log('Loading subscription plans in modal...');

    // Ensure jQuery is available
    if (typeof $ === 'undefined' || typeof $.get !== 'function') {
        console.error('jQuery is not available for modal loading.');
        return;
    }

    $.get('/subscription/plans')
        .done(function(response) {
            console.log('Plans loaded:', response);
            if (response.success && response.plans && response.plans.length > 0) {
                renderSubscriptionPlans(response.plans, '#modal-subscription-plans-container');
            } else {
                $('#modal-subscription-plans-container').html(`
                    <div class="text-center py-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No subscription plans available at the moment.
                        </div>
                    </div>
                `);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load plans:', xhr);
            $('#modal-subscription-plans-container').html(`
                <div class="text-center py-4">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load plans. Please try again.
                        <br>
                        <button onclick="loadSubscriptionPlansInModal()" class="btn btn-outline-danger btn-sm mt-2">
                            <i class="fas fa-redo me-1"></i>
                            Retry
                        </button>
                    </div>
                </div>
            `);
        });
}

function loadInlineSubscriptionPlans() {
    console.log('Loading inline subscription plans...');

    // Check if jQuery is available, if not use fetch
    if (typeof $ === 'undefined' || typeof $.get !== 'function') {
        console.log('jQuery is not available. Loading plans with fetch instead.');
        loadInlineSubscriptionPlansWithFetch();
        return;
    }

    $.get('/subscription/plans')
        .done(function(response) {
            console.log('Inline plans loaded:', response);
            if (response.success && response.plans && response.plans.length > 0) {
                renderSubscriptionPlans(response.plans, '#inline-subscription-plans-container');
            } else {
                $('#inline-subscription-plans-container').html(`
                    <div class="text-center py-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No subscription plans available at the moment.
                        </div>
                    </div>
                `);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load inline plans:', xhr);
            $('#inline-subscription-plans-container').html(`
                <div class="text-center py-4">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load plans. Please try again.
                        <br>
                        <button onclick="loadInlineSubscriptionPlans()" class="btn btn-outline-danger btn-sm mt-2">
                            <i class="fas fa-redo me-1"></i>
                            Retry
                        </button>
                    </div>
                </div>
            `);
        });
}

function loadInlineSubscriptionPlansWithFetch() {
    console.log('Loading inline subscription plans with fetch...');

    fetch('/subscription/plans')
        .then(response => response.json())
        .then(response => {
            console.log('Inline plans loaded with fetch:', response);
            if (response.success && response.plans && response.plans.length > 0) {
                renderSubscriptionPlans(response.plans, '#inline-subscription-plans-container');
            } else {
                document.getElementById('inline-subscription-plans-container').innerHTML = `
                    <div class="text-center py-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No subscription plans available at the moment.
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading inline subscription plans with fetch:', error);
            document.getElementById('inline-subscription-plans-container').innerHTML = `
                <div class="text-center py-4">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading subscription plans. Please refresh the page.
                    </div>
                </div>
            `;
        });
}

function renderSubscriptionPlans(plans, container) {
    try {
        // Get container element once at the beginning
        const containerElement = document.querySelector(container);
        if (!containerElement) {
            console.error('Container not found:', container);
            return;
        }

        // Ensure plans is an array and container exists
        if (!Array.isArray(plans) || plans.length === 0) {
            containerElement.innerHTML = `
                <div class="text-center py-8">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <svg class="w-8 h-8 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-blue-800 font-medium">No subscription plans available at the moment.</p>
                    </div>
                </div>
            `;
            return;
        }

    // Determine grid layout based on number of plans
    const planCount = plans.length;
    let gridCols = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'; // Default: 3 per row

    if (planCount === 4) {
        gridCols = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'; // 4 per row for exactly 4 plans
    } else if (planCount <= 3) {
        gridCols = `grid-cols-1 md:grid-cols-${Math.min(planCount, 2)} lg:grid-cols-${planCount}`; // Responsive for 1-3 plans
    }

        let plansHtml = `<div class="grid ${gridCols} gap-4">`;

        // Use for loop instead of forEach to avoid iterator issues
        for (let i = 0; i < plans.length; i++) {
            const plan = plans[i];
            if (!plan || typeof plan !== 'object') {
                console.warn('Invalid plan data at index', i, plan);
                continue;
            }

            // Safe data extraction with defaults
            const originalPrice = parseFloat(plan.original_price) || parseFloat(plan.amount) || 0;
            const discountPrice = parseFloat(plan.discount_price) || null;
            const hasDiscount = discountPrice && discountPrice < originalPrice;
            const effectivePrice = hasDiscount ? discountPrice : originalPrice;
            const discountPercentage = hasDiscount ? Math.round(((originalPrice - discountPrice) / originalPrice) * 100) : 0;
            const savingsAmount = hasDiscount ? (originalPrice - discountPrice) : 0;
            const durationMonths = parseInt(plan.duration_months) || 1;
            const monthlyRate = (effectivePrice / durationMonths).toFixed(2);

            plansHtml += `
                <div class="bg-gradient-to-br from-purple-50 to-indigo-100 border border-purple-200 rounded p-4"
                     data-plan-id="${plan.id || ''}">

                    <div class="text-center">
                        <!-- Plan Info -->
                        <div class="mb-3">
                            <h5 class="text-base font-medium text-gray-900">${plan.name || 'Unnamed Plan'}</h5>
                            <p class="text-sm text-gray-600">${durationMonths} Month${durationMonths > 1 ? 's' : ''} Plan</p>
                        </div>

                        <!-- Pricing -->
                        <div class="mb-3">
                            ${hasDiscount ? `
                                <div class="flex items-center justify-center gap-2 mb-2">
                                    <div class="text-2xl font-bold text-gray-900">₹${effectivePrice.toLocaleString('en-IN')}</div>
                                    <div class="text-lg text-gray-500 line-through">₹${originalPrice.toLocaleString('en-IN')}</div>
                                </div>
                                <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-2">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    ${discountPercentage}% OFF - Save ₹${savingsAmount.toLocaleString('en-IN')}
                                </div>
                            ` : `
                                <div class="text-2xl font-bold text-gray-900">₹${effectivePrice.toLocaleString('en-IN')}</div>
                            `}
                            <div class="text-sm text-gray-600">₹${monthlyRate}/month</div>
                        </div>

                    <!-- Features -->
                    <div class="mb-4">
                        ${plan.description ? `
                            <div class="text-sm text-gray-700">
                                ${plan.description.replace(/\n/g, '<br>')}
                            </div>
                        ` : `
                            <div class="text-sm text-gray-700">
                                <div>✓ Time Spending Service</div>
                                <div>✓ Enhanced Profile Visibility</div>
                                <div>✓ Priority Support</div>
                            </div>
                        `}
                    </div>

                        <!-- Action Button -->
                        <div>
                            <button type="button"
                                    class="w-full px-4 py-2 bg-white text-gray-800 border-2 border-gray-400 rounded-md hover:bg-gray-100 hover:border-gray-500 font-medium select-plan-btn"
                                    data-plan-id="${plan.id || ''}"
                                    data-plan-name="${(plan.name || '').replace(/'/g, "\\'")}"
                                    data-plan-amount="${effectivePrice}"
                                    onclick="selectSubscriptionPlan(${plan.id || 0}, '${(plan.name || '').replace(/'/g, "\\'")}', ${effectivePrice}); return false;">
                                Activate Plan
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        plansHtml += '</div>';

        // Use native DOM manipulation completely to avoid jQuery/Alpine conflicts
        // Clear existing content first
        containerElement.innerHTML = '';
        // Add new content
        containerElement.innerHTML = plansHtml;

        // Add enhanced hover effects for plan cards with native DOM
        try {
            const planCards = containerElement.querySelectorAll('[data-plan-id]');
            planCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('shadow-lg', 'transform', 'scale-105');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('shadow-lg', 'transform', 'scale-105');
                });
            });
        } catch (e) {
            console.warn('Error adding hover effects:', e);
        }

        // Add click event delegation for plan selection buttons with native DOM
        try {
            const selectButtons = containerElement.querySelectorAll('.select-plan-btn');
            selectButtons.forEach(button => {
                // Remove existing listeners
                button.removeEventListener('click', handlePlanSelection);
                // Add new listener
                button.addEventListener('click', handlePlanSelection);
            });
        } catch (error) {
            console.error('Error adding plan selection handlers:', error);
        }

        // Plan selection handler function
        function handlePlanSelection(e) {
            try {
                e.preventDefault();
                e.stopPropagation();

                const planId = this.getAttribute('data-plan-id');
                const planName = this.getAttribute('data-plan-name');
                const planAmount = this.getAttribute('data-plan-amount');

                if (planId) {
                    console.log('Plan button clicked:', planId, planName, planAmount);
                    selectSubscriptionPlan(planId, planName, planAmount);
                } else {
                    console.error('No plan ID found for button');
                }
            } catch (error) {
                console.error('Error in plan selection:', error);
            }
        }
    } catch (error) {
        // Use the containerElement that was already declared at the beginning
        if (containerElement) {
            containerElement.innerHTML = `
                <div class="text-center py-8">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <svg class="w-8 h-8 text-red-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-red-800 font-medium">Error loading subscription plans. Please refresh the page.</p>
                    </div>
                </div>
            `;
        }
    }
}

function selectSubscriptionPlan(planId, planName, amount) {
    // Prevent event bubbling
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    // Show loading state
    const button = event ? event.target : document.querySelector(`button[data-plan-id="${planId}"]`);
    if (!button) {
        console.error('Button not found for plan:', planId);
        return;
    }

    const originalText = button.innerHTML;
    const originalClass = button.className;

    // Use native DOM instead of jQuery for better compatibility
    // No need to check for jQuery availability

    // Disable all plan buttons
    const allPlanButtons = document.querySelectorAll('.select-plan-btn');
    allPlanButtons.forEach(btn => {
        btn.disabled = true;
        btn.classList.add('opacity-50');
    });

    // Show loading on selected button
    button.disabled = true;
    button.innerHTML = `
        <div class="d-flex align-items-center justify-content-center">
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span>Processing Payment...</span>
        </div>
    `;
    button.className = 'btn btn-warning btn-lg w-100 fw-bold';

    console.log('Selecting plan:', planId, planName, amount);

    // Check CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    console.log('CSRF Token:', csrfToken);

    // Purchase subscription using fetch
    fetch('/subscription/purchase', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            plan_id: planId,
            _token: csrfToken
        })
    })
    .then(response => response.json())
    .then(response => {
        if (response.success) {
            // Show queue message if applicable
            if (response.is_queued && response.queue_message) {
                const queueHtml = `
                    <div class="alert alert-info alert-dismissible fade show mt-3" role="alert">
                        <i class="fas fa-clock me-2"></i>
                        <strong>Queue Notice:</strong> ${response.queue_message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Add queue message to inline container
                const container = document.getElementById('inline-subscription-plans-container');
                if (container) {
                    container.insertAdjacentHTML('afterbegin', queueHtml);
                }
            }

            if (response.payment_required && response.razorpay_amount > 0) {
                // Process Razorpay payment
                processRazorpayPayment(response, planId);
            } else {
                // Wallet-only payment
                processWalletPayment(planId);
            }
        } else {
            alert(response.message || 'Failed to initiate subscription purchase.');
        }
    })
    .catch(error => {
        console.error('Subscription purchase failed:', error);

        // Restore all buttons
        const allPlanButtons = document.querySelectorAll('.select-plan-btn');
        allPlanButtons.forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('opacity-50');
        });
        button.innerHTML = originalText;
        button.className = originalClass;

        // Show error message
        let errorMessage = 'Failed to process subscription. Please try again.';
        if (error.message) {
            errorMessage = error.message;
        }

        // Show error in a better way
        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Activation Failed!</strong> ${errorMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Add error message to inline container
        const container = document.getElementById('inline-subscription-plans-container');
        if (container && !container.querySelector('.alert')) {
            container.insertAdjacentHTML('afterbegin', errorHtml);
        } else {
            const modalContainer = document.getElementById('modal-subscription-plans-container');
            if (modalContainer && !modalContainer.querySelector('.alert')) {
                modalContainer.insertAdjacentHTML('afterbegin', errorHtml);
            }
        }

        // Auto-remove error after 7 seconds
        setTimeout(function() {
            const alertElements = document.querySelectorAll('.alert-danger');
            alertElements.forEach(alert => {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 7000);

        // Final cleanup if needed
        console.log('Subscription purchase request completed');
    });
}

function processRazorpayPayment(paymentData, planId) {
    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not available for Razorpay payment processing.');
        alert('Error: Required libraries not loaded. Please refresh the page.');
        return;
    }

    const options = {
        key: paymentData.razorpay_key,
        amount: paymentData.razorpay_amount * 100,
        currency: 'INR',
        name: 'Time Spending Subscription',
        description: `${paymentData.plan.name}${paymentData.is_queued ? ' (Queued)' : ''}`,
        order_id: paymentData.razorpay_order_id,
        handler: function(response) {
            // Process payment completion
            $.post('/subscription/process-payment', {
                plan_id: planId,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature,
                _token: $('meta[name="csrf-token"]').attr('content')
            })
            .done(function(result) {
                if (result.success) {
                    $('#subscriptionPlansModal').modal('hide');
                    hideInlineSubscriptionPlans(); // Hide inline plans

                    // Show success message with better styling
                    const successMessage = result.is_queued
                        ? 'Subscription queued successfully! It will activate when your current subscription expires.'
                        : 'Subscription activated successfully!';

                    const successHtml = `
                        <div class="alert alert-success alert-dismissible fade show position-fixed"
                             style="top: 20px; right: 20px; z-index: 99999; min-width: 350px;" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Success!</strong> ${successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                    $('body').append(successHtml);

                    // Auto-remove success message and reload
                    setTimeout(function() {
                        $('.alert-success').fadeOut(function() {
                            location.reload(); // Refresh to show updated subscription status
                        });
                    }, 3000);
                } else {
                    alert(result.message || 'Payment processing failed.');
                }
            })
            .fail(function() {
                alert('Payment processing failed. Please contact support.');
            });
        },
        prefill: {
            name: '{{ auth()->user()->name ?? "" }}',
            email: '{{ auth()->user()->email ?? "" }}'
        },
        theme: {
            color: '#3B82F6'
        },
        modal: {
            ondismiss: function() {
                console.log('Payment cancelled by user');
            }
        }
    };

    const rzp = new Razorpay(options);
    rzp.open();
}

function processWalletPayment(planId) {
    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not available for wallet payment processing.');
        alert('Error: Required libraries not loaded. Please refresh the page.');
        return;
    }

    $.post('/subscription/process-payment', {
        plan_id: planId,
        _token: $('meta[name="csrf-token"]').attr('content')
    })
    .done(function(result) {
        if (result.success) {
            $('#subscriptionPlansModal').modal('hide');
            hideInlineSubscriptionPlans(); // Hide inline plans

            // Show success message with better styling
            const successMessage = result.is_queued
                ? 'Subscription queued successfully via wallet! It will activate when your current subscription expires.'
                : 'Subscription activated successfully via wallet!';

            const successHtml = `
                <div class="alert alert-success alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 99999; min-width: 350px;" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Success!</strong> ${successMessage}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('body').append(successHtml);

            // Auto-remove success message and reload
            setTimeout(function() {
                $('.alert-success').fadeOut(function() {
                    location.reload(); // Refresh to show updated subscription status
                });
            }, 3000);
        } else {
            alert(result.message || 'Payment processing failed.');
        }
    })
    .fail(function() {
        alert('Payment processing failed. Please contact support.');
    });
}

// Load plans on page load - wait for jQuery if available
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for jQuery to load if it's being loaded
    setTimeout(function() {
    // Ensure no modal is shown automatically with better error handling
    try {
        const modalElement = document.getElementById('subscriptionPlansModal');
        if (modalElement) {
            // Use native Bootstrap modal methods if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }
        }
    } catch (e) {
        // Silently handle modal errors as they're not critical
    }

    // Remove any existing modal backdrops and overlays with native JavaScript
    const modalBackdrops = document.querySelectorAll('.modal-backdrop');
    modalBackdrops.forEach(backdrop => backdrop.remove());

    const modalOpenElements = document.querySelectorAll('.modal-open');
    modalOpenElements.forEach(element => element.classList.remove('modal-open'));

    document.body.classList.remove('modal-open');

    // Remove any overlay styles that might be blocking interaction
    document.body.style.overflow = 'auto';
    document.body.style.paddingRight = '0px';

        // Don't auto-load plans on page load - only load when user clicks "Update Plan"
        // This maintains the original hide/show behavior
    }, 1000); // Wait 1 second for jQuery to load
});
</script>

<style>
/* Clean subscription plan styles */
[data-plan-id] {
    transition: all 0.3s ease;
}

[data-plan-id]:hover {
    transform: translateY(-2px);
}

.select-plan-btn {
    transition: all 0.2s ease;
}

.select-plan-btn:hover {
    transform: translateY(-1px);
}

.subscription-plan-card.border-primary {
    border-color: #007bff !important;
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

.subscription-plan-card .btn {
    position: relative;
    z-index: 10;
    pointer-events: auto;
    border-radius: 10px;
    font-weight: 600;
    padding: 14px 28px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.subscription-plan-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.subscription-plan-card .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.subscription-plan-card .btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

/* New Plan Button Styles */
.btn-plan-select {
    width: 100%;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-plan-select:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.btn-plan-select.popular {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-plan-select.popular:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.5);
}

/* Horizontal Layout Styles */
.btn-plan-select-horizontal {
    padding: 10px 20px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    white-space: nowrap;
}

.btn-plan-select-horizontal:hover {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.btn-plan-select-horizontal.popular {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 3px 12px rgba(245, 158, 11, 0.3);
}

.btn-plan-select-horizontal.popular:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.5);
}

.pricing-section-horizontal {
    padding: 12px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 12px;
    border: 1px solid #cbd5e1;
}

.features-section-horizontal {
    padding: 8px 0;
}

.badge.bg-success-subtle {
    background-color: #dcfce7 !important;
    color: #166534 !important;
    font-size: 11px;
    padding: 4px 8px;
}

.pricing-section {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 24px;
    margin: 20px 0;
    border: 1px solid #cbd5e1;
}

.discount-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.original-price-strikethrough {
    text-decoration: line-through;
    color: #9ca3af;
    font-size: 0.9em;
}

.effective-price {
    color: #059669;
    font-weight: 700;
}

.price-main {
    margin-bottom: 8px;
}

.price-main .display-6 {
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.price-sub {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.price-main {
    line-height: 1;
}

.features-section {
    text-align: left;
}

.features-section ul li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.features-section ul li:last-child {
    border-bottom: none;
}

/* Modal specific styles */
#subscriptionPlansModal {
    z-index: 9999 !important;
}

.modal-backdrop {
    z-index: 9998 !important;
    display: none !important;
    visibility: hidden !important;
}

#subscriptionPlansModal .modal-dialog {
    z-index: 10000 !important;
    margin: 1.75rem auto;
}

/* Fix for modal backdrop blocking clicks */
.modal.show {
    pointer-events: auto !important;
}

.modal-backdrop {
    pointer-events: none !important;
    display: none !important;
}

.modal-backdrop.show {
    pointer-events: none !important;
    display: none !important;
}

#subscriptionPlansModal .modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 25px 80px rgba(0,0,0,0.15);
    overflow: hidden;
}

#subscriptionPlansModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    padding: 25px 30px;
}

#subscriptionPlansModal .modal-body {
    padding: 0;
    background: #f8f9fa;
}

#subscriptionPlansModal .modal-footer {
    background: #f8f9fa;
    border-radius: 0 0 20px 20px;
    padding: 20px 30px;
}

#subscriptionPlansModal .modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

#subscriptionPlansModal .modal-header .btn-close:hover {
    opacity: 1;
}

/* Modal dialog sizing */
@media (min-width: 1200px) {
    #subscriptionPlansModal .modal-xl {
        max-width: 1000px;
    }
}

@media (max-width: 768px) {
    #subscriptionPlansModal .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .subscription-plan-card {
        margin-bottom: 20px;
    }
}

/* Ensure buttons are clickable */
.subscription-plan-card button {
    pointer-events: auto !important;
    position: relative !important;
    z-index: 999 !important;
}

/* Remove any overlay that might be blocking clicks */
.modal-body * {
    pointer-events: auto !important;
}

/* Loading spinner styles */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Prevent any overlay issues */
body {
    overflow: auto !important;
    padding-right: 0 !important;
}

body.modal-open {
    overflow: auto !important;
    padding-right: 0 !important;
}

/* Hide any unwanted overlays */
.modal-backdrop,
.modal-backdrop.fade,
.modal-backdrop.show {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}
</style>
