<!-- Compact Sugar Partner Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-rose-600 to-pink-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Sugar Partner</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Configure your preferences for meaningful connections and arrangements</p>
</div>

<div class="bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="space-y-4 p-4">
        <form method="post" action="{{ route('profile.sugar-partner.update') }}" class="space-y-4">
            @csrf
            @method('patch')
            <input type="hidden" name="current_tab" value="sugar-partner">

            <!-- Partner Preferences Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                @php
                    // Get user info first
                    $userAge = $user->getAge();
                    $userGender = $user->gender;

                    $whatIAmOptions = $user->getWhatIAmOptions();
                    $whatIWantOptions = $user->getWhatIWantOptions();
                    $selectedTypes = old('sugar_partner_types', $user->sugar_partner_types ?? []);

                    // Get smart defaults based on age and existing selections
                    $smartDefaults = $user->getSmartDefaults();
                    $currentWhatIAm = old('what_i_am', $smartDefaults['what_i_am']);
                    $currentWhatIWant = old('what_i_want', $smartDefaults['what_i_want']);

                    // Ensure $errors is available (for testing compatibility)
                    if (!isset($errors)) {
                        $errors = new \Illuminate\Support\MessageBag();
                    }
                @endphp

                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Relationship Preferences
                </h4>
                <p class="text-xs text-gray-600 mb-4">
                    Define your role and preferences in sugar partner relationships:
                    @if($userAge)
                        <span class="inline-block ml-2 px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                            Age: {{ $userAge }} years
                            @if($userAge < 30)
                                (Young - Seeking support recommended)
                            @else
                                (Experienced - Providing support recommended)
                            @endif
                        </span>
                    @endif
                </p>

                <!-- Section 1: What I Am (Dropdown) -->
                <div class="mb-6">
                    <h5 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        What I Am
                        <span class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-600 text-xs font-medium rounded-full">Single Selection</span>
                    </h5>
                    <p class="text-xs text-gray-600 mb-3">
                        Select your role as a provider (optional):
                        @if($userAge && $userAge >= 30)
                            <span class="text-blue-600 font-medium">Suggested for 30+ users</span>
                        @endif
                    </p>

                    <select name="what_i_am" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500 text-sm">
                        <option value="">Select your provider role (optional)</option>
                        @foreach($whatIAmOptions as $value => $option)
                            <option value="{{ $value }}" {{ $currentWhatIAm === $value ? 'selected' : '' }}>
                                {{ $option['label'] }}
                            </option>
                        @endforeach
                    </select>
                    <x-input-error class="mt-1" :messages="$errors->get('what_i_am')" />
                </div>

                <!-- Section 2: What I Want (Checkboxes) -->
                <div class="mb-4">
                    <h5 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        What I Want
                        <span class="ml-2 px-2 py-0.5 bg-pink-100 text-pink-600 text-xs font-medium rounded-full">Multiple Selection</span>
                    </h5>
                    <p class="text-xs text-gray-600 mb-3">
                        Select what you're seeking (you can choose multiple):
                        @if($userAge && $userAge < 30)
                            <span class="text-pink-600 font-medium">Suggested for under 30 users</span>
                        @endif
                    </p>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3" id="what-i-want-options">
                        @foreach($whatIWantOptions as $value => $option)
                            <label class="relative flex items-start p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-rose-50 hover:border-rose-300 transition-all duration-200 {{ in_array($value, $currentWhatIWant) ? 'bg-rose-50 border-rose-300' : '' }}" data-option-value="{{ $value }}">
                                <input type="checkbox"
                                       name="what_i_want[]"
                                       value="{{ $value }}"
                                       {{ in_array($value, $currentWhatIWant) ? 'checked' : '' }}
                                       class="w-4 h-4 text-rose-600 bg-white border-gray-300 rounded focus:ring-rose-500 focus:ring-2 mt-0.5">
                                <div class="ml-3 flex-1">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 rounded-full bg-{{ $option['color'] }}-500 mr-2"></div>
                                        <span class="text-sm font-medium text-gray-900">{{ $option['label'] }}</span>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">{{ $option['description'] }}</p>
                                </div>
                            </label>
                        @endforeach
                    </div>
                    <x-input-error class="mt-2" :messages="$errors->get('what_i_want')" />
                    <x-input-error class="mt-2" :messages="$errors->get('what_i_want.*')" />
                </div>

                <!-- Hidden field to maintain backward compatibility -->
                <input type="hidden" name="sugar_partner_types_combined" id="sugar_partner_types_combined" value="">

                <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_types')" />
                <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_types.*')" />
            </div>

            <!-- Bio Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Profile Bio
                    <span class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">Optional</span>
                </h4>
                <div>
                    <textarea id="sugar_partner_bio"
                              name="sugar_partner_bio"
                              rows="3"
                              placeholder="Describe what you're looking for in a sugar partner relationship..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500 text-sm">{{ old('sugar_partner_bio', $user->sugar_partner_bio ?? '') }}</textarea>
                    <p class="text-xs text-gray-500 mt-1">This will be displayed on your profile to potential sugar partners.</p>
                    <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_bio')" />
                </div>
            </div>

            <!-- Expectations Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                    Expectations & Boundaries
                    <span class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">Optional</span>
                </h4>
                <div>
                    <textarea id="sugar_partner_expectations"
                              name="sugar_partner_expectations"
                              rows="3"
                              placeholder="Describe your expectations, boundaries, and what you can offer..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500 text-sm">{{ old('sugar_partner_expectations', $user->sugar_partner_expectations ?? '') }}</textarea>
                    <p class="text-xs text-gray-500 mt-1">Be clear about your expectations to find compatible matches.</p>
                    <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_expectations')" />
                </div>
            </div>

            

            <!-- Save Button -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <div class="flex items-center justify-end">
                    <button type="submit" class="px-4 py-2 bg-rose-600 text-white font-medium rounded-lg hover:bg-rose-700 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-sm">
                        Save Sugar Partner Settings
                    </button>
                </div>
            </div>

            <!-- Success Message -->
            @if (session('status') === 'sugar-partner-updated')
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p class="text-xs text-green-800">Sugar partner settings updated successfully!</p>
                    </div>
                </div>
            @endif
        </div>
    </form>
</div>

<style>
/* Sugar Partner specific styles */
.sugar-partner-option {
    transition: all 0.2s ease;
}

.sugar-partner-option:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 63, 94, 0.15);
}

.sugar-partner-option input[type="checkbox"]:checked + div {
    color: #e11d48;
}

.sugar-partner-option input[type="checkbox"]:checked {
    background-color: #e11d48;
    border-color: #e11d48;
}

/* Enhanced focus states */
textarea:focus {
    box-shadow: 0 0 0 3px rgba(244, 63, 94, 0.1);
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .sugar-partner-grid {
        grid-template-columns: 1fr;
    }

    .sugar-partner-option {
        padding: 0.75rem;
    }
}

/* Animation for success message */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-message {
    animation: slideIn 0.3s ease-out;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add enhanced styling classes for checkboxes
    const options = document.querySelectorAll('label[class*="relative flex items-start"]');
    options.forEach(option => {
        option.classList.add('sugar-partner-option');
    });

    // Add grid class for responsive design
    const grid = document.querySelector('.grid.grid-cols-1.sm\\:grid-cols-2');
    if (grid) {
        grid.classList.add('sugar-partner-grid');
    }

    // Add success message animation
    const successMessage = document.querySelector('.bg-green-50');
    if (successMessage) {
        successMessage.classList.add('success-message');
    }

    // Enhanced checkbox interaction with rose color scheme for "What I Want" section
    const whatIWantCheckboxes = document.querySelectorAll('input[name="what_i_want[]"]');
    whatIWantCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const label = this.closest('label');
            if (this.checked) {
                label.style.borderColor = '#e11d48';
                label.style.backgroundColor = '#fdf2f8';
            } else {
                label.style.borderColor = '#e5e7eb';
                label.style.backgroundColor = '';
            }
            updateCombinedField();
        });

        // Set initial state
        if (checkbox.checked) {
            const label = checkbox.closest('label');
            label.style.borderColor = '#e11d48';
            label.style.backgroundColor = '#fdf2f8';
        }
    });

    // Handle dropdown change for "What I Am" section
    const whatIAmSelect = document.querySelector('select[name="what_i_am"]');
    if (whatIAmSelect) {
        whatIAmSelect.addEventListener('change', function() {
            updateCombinedField();
            updateOptionVisibility();
            showSmartSuggestions();
        });
    }

    // Add change listeners to checkboxes for dynamic filtering
    whatIWantCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateOptionVisibility();
            showSmartSuggestions();
        });
    });

    // Initialize filtering on page load
    updateOptionVisibility();

    // Function to update option visibility based on selections
    function updateOptionVisibility() {
        const whatIAmValue = whatIAmSelect ? whatIAmSelect.value : '';

        // Filter "What I Want" options to exclude the selected "What I Am" option
        const whatIWantContainer = document.getElementById('what-i-want-options');
        if (whatIWantContainer) {
            const optionLabels = whatIWantContainer.querySelectorAll('label[data-option-value]');

            optionLabels.forEach(label => {
                const optionValue = label.getAttribute('data-option-value');
                const checkbox = label.querySelector('input[type="checkbox"]');

                if (optionValue === whatIAmValue) {
                    // Hide the option that matches "What I Am" selection
                    label.style.display = 'none';
                    // Uncheck if it was previously checked
                    if (checkbox && checkbox.checked) {
                        checkbox.checked = false;
                    }
                } else {
                    // Show the option
                    label.style.display = 'flex';
                }
            });
        }

        // Update the combined field for form submission
        updateCombinedField();
    }

    // Function to highlight recommended options
    function highlightRecommendedOptions(recommendedTypes) {
        // Clear previous recommendations
        clearRecommendations();

        // Highlight recommended "What I Am" options
        if (whatIAmSelect) {
            const options = whatIAmSelect.querySelectorAll('option');
            options.forEach(option => {
                if (recommendedTypes.includes(option.value)) {
                    option.style.backgroundColor = '#dbeafe';
                    option.style.fontWeight = 'bold';
                }
            });
        }

        // Highlight recommended "What I Want" options
        whatIWantCheckboxes.forEach(checkbox => {
            if (recommendedTypes.includes(checkbox.value)) {
                const label = checkbox.closest('label');
                if (label && !checkbox.checked) {
                    label.style.boxShadow = '0 0 0 2px #3b82f6';
                    label.style.borderColor = '#3b82f6';
                }
            }
        });
    }

    // Function to clear recommendations
    function clearRecommendations() {
        // Clear dropdown highlights
        if (whatIAmSelect) {
            const options = whatIAmSelect.querySelectorAll('option');
            options.forEach(option => {
                option.style.backgroundColor = '';
                option.style.fontWeight = '';
            });
        }

        // Clear checkbox highlights
        whatIWantCheckboxes.forEach(checkbox => {
            const label = checkbox.closest('label');
            if (label && !checkbox.checked) {
                label.style.boxShadow = '';
                label.style.borderColor = '#e5e7eb';
            }
        });
    }

    // Function to show smart suggestions based on age and gender
    function showSmartSuggestions() {
        const userAge = {{ $userAge ?? 'null' }};
        const userGender = '{{ $userGender ?? '' }}';

        // Only show suggestions for new users (no existing selections)
        const hasExistingSelections = whatIAmSelect.value ||
            Array.from(whatIWantCheckboxes).some(cb => cb.checked);

        if (!hasExistingSelections && userAge !== null) {
            let suggestionText = '';

            if (userAge < 30) {
                if (userGender === 'male') {
                    suggestionText = '💡 Suggestion: As a young man, you might want to select "I want Sugar Boy" to connect with supportive partners.';
                } else if (userGender === 'female') {
                    suggestionText = '💡 Suggestion: As a young woman, you might want to select "I want Sugar Babe" to connect with supportive partners.';
                }
            } else {
                if (userGender === 'male') {
                    suggestionText = '💡 Suggestion: As an experienced man, you might want to select "I am Sugar Daddy" to provide support to others.';
                } else if (userGender === 'female') {
                    suggestionText = '💡 Suggestion: As an experienced woman, you might want to select "I am Sugar Mommy" to provide support to others.';
                }
            }

            if (suggestionText) {
                showSuggestionToast(suggestionText);
            }
        }
    }

    // Function to show suggestion toast
    function showSuggestionToast(message) {
        // Remove existing toast if any
        const existingToast = document.querySelector('.suggestion-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'suggestion-toast fixed top-4 right-4 bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-lg shadow-lg max-w-sm z-50';
        toast.innerHTML = `
            <div class="flex items-start">
                <div class="flex-1 text-sm">${message}</div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-blue-600 hover:text-blue-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        document.body.appendChild(toast);

        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 8000);
    }

    // Initialize smart suggestions on page load
    setTimeout(() => {
        showSmartSuggestions();
    }, 1000);

    // Function to combine values for backward compatibility
    function updateCombinedField() {
        const combinedValues = [];

        // Add "What I Am" selection
        const whatIAmValue = whatIAmSelect ? whatIAmSelect.value : '';
        if (whatIAmValue) {
            combinedValues.push(whatIAmValue);
        }

        // Add "What I Want" selections
        whatIWantCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                combinedValues.push(checkbox.value);
            }
        });

        // Update hidden field
        const combinedField = document.getElementById('sugar_partner_types_combined');
        if (combinedField) {
            combinedField.value = JSON.stringify(combinedValues);
        }
    }

    // Form submission handler to ensure backward compatibility
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            updateCombinedField();

            // Create hidden inputs for the original field name
            const existingHiddenInputs = form.querySelectorAll('input[name="sugar_partner_types[]"]');
            existingHiddenInputs.forEach(input => input.remove());

            const combinedField = document.getElementById('sugar_partner_types_combined');
            if (combinedField && combinedField.value) {
                const values = JSON.parse(combinedField.value);
                values.forEach(value => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'sugar_partner_types[]';
                    hiddenInput.value = value;
                    form.appendChild(hiddenInput);
                });
            }
        });
    }

    // Initialize combined field on page load
    updateCombinedField();

    // Character counter for textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const maxLength = 500; // Set reasonable limit

        // Create character counter
        const counter = document.createElement('div');
        counter.className = 'text-xs text-gray-400 mt-1 text-right';
        counter.textContent = `${textarea.value.length}/${maxLength}`;

        // Insert after the existing help text
        const helpText = textarea.parentNode.querySelector('.text-xs.text-gray-500');
        if (helpText) {
            helpText.parentNode.insertBefore(counter, helpText.nextSibling);
        } else {
            textarea.parentNode.appendChild(counter);
        }

        // Update counter on input
        textarea.addEventListener('input', function() {
            const length = this.value.length;
            counter.textContent = `${length}/${maxLength}`;

            if (length > maxLength * 0.9) {
                counter.style.color = '#ef4444';
            } else if (length > maxLength * 0.8) {
                counter.style.color = '#f59e0b';
            } else {
                counter.style.color = '#9ca3af';
            }
        });
    });
});
</script>
