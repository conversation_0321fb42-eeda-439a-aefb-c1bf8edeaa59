<!-- Compact <PERSON><PERSON><PERSON> Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Privacy Settings</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Control what information is visible to other users</p>
</div>

<div class="bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="space-y-4 p-4">
        <form method="post" action="{{ route('profile.privacy-settings.update') }}" class="space-y-4">
            @csrf
            @method('patch')
            <input type="hidden" name="current_tab" value="settings">

            <!-- Personal Information Privacy Settings -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Personal Information
                </h4>

                <div class="space-y-3">
                    <!-- Public Profile -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="flex-1">
                            <label for="is_public_profile" class="block text-sm font-medium text-gray-700 mb-1">
                                Public Profile
                            </label>
                            <p class="text-xs text-gray-500">When enabled, your profile can be found in Google search and by other users</p>
                        </div>
                        <div class="ml-4">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                       id="is_public_profile"
                                       name="is_public_profile"
                                       value="1"
                                       {{ old('is_public_profile', $user->is_public_profile) ? 'checked' : '' }}
                                       class="sr-only peer">
                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>



                    <!-- Show Date of Birth -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                        <div class="flex-1">
                            <label for="show_date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">
                                Show Date of Birth
                            </label>
                            <p class="text-xs text-gray-500">Display your date of birth on your profile</p>
                        </div>
                        <div class="ml-4">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                       id="show_date_of_birth"
                                       name="show_date_of_birth"
                                       value="1"
                                       {{ old('show_date_of_birth', $user->show_date_of_birth) ? 'checked' : '' }}
                                       class="sr-only peer">
                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>

                    <!-- Hide DOB Year -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                        <div class="flex-1">
                            <label for="hide_dob_year" class="block text-sm font-medium text-gray-700 mb-1">
                                Hide Birth Year
                            </label>
                            <p class="text-xs text-gray-500">Show only month and day, hide the year from your date of birth</p>
                        </div>
                        <div class="ml-4">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                       id="hide_dob_year"
                                       name="hide_dob_year"
                                       value="1"
                                       {{ old('hide_dob_year', $user->hide_dob_year) ? 'checked' : '' }}
                                       class="sr-only peer">
                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>

                    <!-- Show Interests & Hobbies -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                        <div class="flex-1">
                            <label for="show_interests_hobbies" class="block text-sm font-medium text-gray-700 mb-1">
                                Show Interests & Hobbies
                            </label>
                            <p class="text-xs text-gray-500">Display your interests and hobbies on your profile</p>
                        </div>
                        <div class="ml-4">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                       id="show_interests_hobbies"
                                       name="show_interests_hobbies"
                                       value="1"
                                       {{ old('show_interests_hobbies', $user->show_interests_hobbies) ? 'checked' : '' }}
                                       class="sr-only peer">
                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>

                    <!-- Show Expectations from Partner -->
                    <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                        <div class="flex-1">
                            <label for="show_expectations" class="block text-sm font-medium text-gray-700 mb-1">
                                Show Expectations from Partner
                            </label>
                            <p class="text-xs text-gray-500">Display your partner expectations on your profile</p>
                        </div>
                        <div class="ml-4">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                       id="show_expectations"
                                       name="show_expectations"
                                       value="1"
                                       {{ old('show_expectations', $user->show_expectations) ? 'checked' : '' }}
                                       class="sr-only peer">
                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Privacy Settings -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Gallery Settings
                </h4>

                @if(\App\Models\Feature::isEnabled('gallery'))
                    <div class="space-y-3">
                        <!-- Show Images -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex-1">
                                <label for="show_gallery_images" class="block text-sm font-medium text-gray-700 mb-1">
                                    Show Gallery Images
                                </label>
                                <p class="text-xs text-gray-500">Display your gallery images on your profile (only if profile is public)</p>
                            </div>
                            <div class="ml-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           id="show_gallery_images"
                                           name="show_gallery_images"
                                           value="1"
                                           {{ old('show_gallery_images', $user->show_gallery_images) ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                        <p class="text-xs text-gray-600">
                            <span class="font-medium">Gallery Feature:</span> Gallery feature is currently disabled by admin.
                        </p>
                    </div>
                @endif
            </div>

            <!-- Time Spending Privacy Settings -->
            @if(\App\Models\Feature::isEnabled('time_spending'))
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Time Spending Service
                    </h4>

                    <div class="space-y-3">
                        <!-- Show Time Spending Service -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex-1">
                                <label for="is_time_spending_enabled" class="block text-sm font-medium text-gray-700 mb-1">
                                    Enable Time Spending Service
                                </label>
                                <p class="text-xs text-gray-500">
                                    Display your time spending service and hourly rate on your profile
                                    @if(!$user->is_public_profile || !$user->show_gallery_images)
                                        <br><strong>Note:</strong> This will automatically enable "Public Profile" and "Show Images" settings.
                                    @endif
                                </p>
                            </div>
                            <div class="ml-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           id="is_time_spending_enabled"
                                           name="is_time_spending_enabled"
                                           value="1"
                                           {{ old('is_time_spending_enabled', $user->is_time_spending_enabled) ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-green-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Couple Activity Settings -->
            @if(\App\Models\Feature::isEnabled('partner_swapping'))
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        Couple Activity
                    </h4>

                    <div class="space-y-3">
                        <!-- Enable Couple Activity -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex-1">
                                <label for="is_couple_activity_enabled" class="block text-sm font-medium text-gray-700 mb-1">
                                    Enable Couple Activity
                                </label>
                                <p class="text-xs text-gray-500">
                                    Allow other users to send you couple activity requests and participate in couple activities
                                    @if(!$user->is_public_profile)
                                        <br><strong>Note:</strong> This will automatically enable "Public Profile" setting.
                                    @endif
                                </p>
                            </div>
                            <div class="ml-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           id="is_couple_activity_enabled"
                                           name="is_couple_activity_enabled"
                                           value="1"
                                           {{ old('is_couple_activity_enabled', $user->is_couple_activity_enabled) ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-pink-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Sugar Partner Settings -->
            @if(\App\Models\Feature::isEnabled('sugar_partner'))
                <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        Sugar Partner Service
                    </h4>

                    <div class="space-y-3">
                        <!-- Enable Sugar Partner -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex-1">
                                <label for="interested_in_sugar_partner" class="block text-sm font-medium text-gray-700 mb-1">
                                    Enable Sugar Partner Service
                                </label>
                                <p class="text-xs text-gray-500">
                                    Allow participation in sugar partner relationships and show related options on your profile
                                    @if(!$user->is_public_profile)
                                        <br><strong>Note:</strong> This will automatically enable "Public Profile" setting.
                                    @endif
                                </p>
                            </div>
                            <div class="ml-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           id="interested_in_sugar_partner"
                                           name="interested_in_sugar_partner"
                                           value="1"
                                           {{ old('interested_in_sugar_partner', $user->interested_in_sugar_partner) ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-rose-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-rose-600"></div>
                                </label>
                            </div>
                        </div>

                        <!-- Hide Sugar Partner Notifications -->
                        <div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                            <div class="flex-1">
                                <label for="hide_sugar_partner_notifications" class="block text-sm font-medium text-gray-700 mb-1">
                                    Hide Sugar Partner Notifications
                                </label>
                                <p class="text-xs text-gray-500">Hide transaction notifications related to Sugar Partner profile exchanges</p>
                            </div>
                            <div class="ml-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           id="hide_sugar_partner_notifications"
                                           name="hide_sugar_partner_notifications"
                                           value="1"
                                           {{ old('hide_sugar_partner_notifications', $user->hide_sugar_partner_notifications) ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-rose-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-rose-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Save Button Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <div class="flex items-center justify-end">
                    <button type="submit" class="px-4 py-2 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-sm">
                        Save Privacy Settings
                    </button>
                </div>
            </div>

            <!-- Success Message -->
            @if (session('status') === 'privacy-settings-updated')
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p class="text-xs text-green-800">Privacy settings updated successfully!</p>
                    </div>
                </div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const successMessage = document.querySelector('.bg-green-50');
                        if (successMessage) {
                            setTimeout(() => {
                                successMessage.style.transition = 'opacity 0.5s ease';
                                successMessage.style.opacity = '0';
                                setTimeout(() => {
                                    if (successMessage.parentNode) {
                                        successMessage.parentNode.removeChild(successMessage);
                                    }
                                }, 500);
                            }, 3000);
                        }
                    });
                </script>
            @endif
        </div>
    </form>
</div>
