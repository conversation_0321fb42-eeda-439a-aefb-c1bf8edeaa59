@extends('layouts.app')

@section('title', 'Sugar Partner Hard Rejects Management')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-shield-x me-2"></i>Sugar Partner Hard Rejects Management
                    </h4>
                </div>
                <div class="card-body p-4">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>Hard Reject Management
                        </h6>
                        <p class="mb-2">
                            Here you can manage users you have hard rejected or who have hard rejected you. You have two options:
                        </p>
                        <ul class="mb-0">
                            <li><strong>Convert to Soft Reject:</strong> Changes hard reject to soft reject, allowing future exchanges</li>
                            <li><strong>Remove Completely:</strong> Clears all rejection history, creating a neutral relationship status</li>
                        </ul>
                    </div>

                    @if(count($blockedUsers) > 0)
                        <div class="row">
                            @foreach($blockedUsers as $blockedUser)
                                <div class="col-md-6 mb-4">
                                    <div class="card border-danger">
                                        <div class="card-header bg-danger-subtle">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="card-title mb-0">
                                                    <i class="bi bi-person-x me-2"></i>{{ $blockedUser['user']->name }}
                                                </h6>
                                                @if($blockedUser['is_rejector'])
                                                    <span class="badge bg-danger">You Rejected</span>
                                                @else
                                                    <span class="badge bg-warning">Rejected You</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                @if($blockedUser['user']->profile_picture)
                                                    <img src="{{ asset('storage/' . $blockedUser['user']->profile_picture) }}" 
                                                         alt="{{ $blockedUser['user']->name }}" 
                                                         class="rounded-circle me-3" 
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="bi bi-person text-muted fs-4"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <h6 class="mb-1">{{ $blockedUser['user']->name }}</h6>
                                                    <small class="text-muted">{{ $blockedUser['user']->email }}</small>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <strong>Rejection Date:</strong> {{ $blockedUser['created_at']->format('M d, Y') }}<br>
                                                <strong>Reason:</strong> {{ $blockedUser['rejection_reason'] ?: 'No reason provided' }}
                                            </div>

                                            @if($blockedUser['is_rejector'])
                                                <!-- User can manage their own rejections -->
                                                <div class="d-grid gap-2">
                                                    <button type="button" 
                                                            class="btn btn-warning btn-sm" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#convertToSoftModal"
                                                            data-user1="{{ auth()->id() }}"
                                                            data-user2="{{ $blockedUser['user']->id }}"
                                                            data-username="{{ $blockedUser['user']->name }}">
                                                        <i class="bi bi-arrow-repeat me-1"></i>Convert to Soft Reject
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-outline-success btn-sm" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#removeCompletelyModal"
                                                            data-user1="{{ auth()->id() }}"
                                                            data-user2="{{ $blockedUser['user']->id }}"
                                                            data-username="{{ $blockedUser['user']->name }}">
                                                        <i class="bi bi-trash me-1"></i>Remove Completely
                                                    </button>
                                                </div>
                                            @else
                                                <!-- User was rejected by someone else -->
                                                <div class="alert alert-warning mb-0">
                                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                                    <small>This user rejected you. Only they can modify this rejection status.</small>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="bi bi-heart-fill text-success display-1"></i>
                            <h4 class="mt-3 text-success">No Hard Rejects!</h4>
                            <p class="text-muted">You don't have any hard rejected users. This means you can potentially exchange profiles with any Sugar Partner user.</p>
                            <a href="{{ route('home') }}" class="btn btn-primary">
                                <i class="bi bi-house me-1"></i>Back to Dashboard
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Convert to Soft Reject Modal -->
<div class="modal fade" id="convertToSoftModal" tabindex="-1" aria-labelledby="convertToSoftModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="convertToSoftModalLabel">
                    <i class="bi bi-arrow-repeat me-2"></i>Convert to Soft Reject
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('sugar-partner.hard-rejects.convert-to-soft') }}">
                @csrf
                <input type="hidden" name="user1_id" id="convert_user1_id">
                <input type="hidden" name="user2_id" id="convert_user2_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Convert Hard Reject to Soft Reject</strong><br>
                        This will change your hard reject of <span id="convert_username" class="fw-bold"></span> to a soft reject, which means:
                        <ul class="mt-2 mb-0">
                            <li>Future profile exchanges will be possible</li>
                            <li>The user will be notified of this change</li>
                            <li>Your rejection history will show as "soft reject" instead</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="convert_reason" class="form-label">Reason for Conversion (Optional)</label>
                        <textarea class="form-control" id="convert_reason" name="reason" rows="3" placeholder="Why are you converting this hard reject to soft reject?"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-arrow-repeat me-1"></i>Convert to Soft Reject
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Remove Completely Modal -->
<div class="modal fade" id="removeCompletelyModal" tabindex="-1" aria-labelledby="removeCompletelyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeCompletelyModalLabel">
                    <i class="bi bi-trash me-2"></i>Remove Rejection Completely
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('sugar-partner.hard-rejects.remove-completely') }}">
                @csrf
                <input type="hidden" name="user1_id" id="remove_user1_id">
                <input type="hidden" name="user2_id" id="remove_user2_id">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Remove All Rejection History</strong><br>
                        This will completely clear all rejection history between you and <span id="remove_username" class="fw-bold"></span>, which means:
                        <ul class="mt-2 mb-0">
                            <li>You will have a neutral relationship status (no accept, no reject)</li>
                            <li>Future profile exchanges will be possible</li>
                            <li>The user will be notified of this change</li>
                            <li>All previous rejection records will be permanently deleted</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="remove_reason" class="form-label">Reason for Removal (Optional)</label>
                        <textarea class="form-control" id="remove_reason" name="reason" rows="3" placeholder="Why are you clearing all rejection history?"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-trash me-1"></i>Remove Completely
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle Convert to Soft Reject modal
    const convertModal = document.getElementById('convertToSoftModal');
    if (convertModal) {
        convertModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const user1Id = button.getAttribute('data-user1');
            const user2Id = button.getAttribute('data-user2');
            const username = button.getAttribute('data-username');

            document.getElementById('convert_user1_id').value = user1Id;
            document.getElementById('convert_user2_id').value = user2Id;
            document.getElementById('convert_username').textContent = username;
        });
    }

    // Handle Remove Completely modal
    const removeModal = document.getElementById('removeCompletelyModal');
    if (removeModal) {
        removeModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const user1Id = button.getAttribute('data-user1');
            const user2Id = button.getAttribute('data-user2');
            const username = button.getAttribute('data-username');

            document.getElementById('remove_user1_id').value = user1Id;
            document.getElementById('remove_user2_id').value = user2Id;
            document.getElementById('remove_username').textContent = username;
        });
    }
});
</script>
@endpush
