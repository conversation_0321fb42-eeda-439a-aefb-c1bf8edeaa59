<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache - SettingWala</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 class="text-2xl font-bold text-gray-900 mb-6 text-center">Clear Browser Cache</h1>
        
        <div class="space-y-4">
            <button onclick="clearServiceWorker()" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                Clear Service Worker
            </button>
            
            <button onclick="clearAllCaches()" class="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                Clear All Caches
            </button>
            
            <button onclick="reloadPage()" class="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                Hard Reload
            </button>
            
            <a href="/login" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors block text-center">
                Back to Login
            </a>
        </div>
        
        <div id="status" class="mt-6 p-4 rounded-lg hidden"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `mt-6 p-4 rounded-lg ${type === 'success' ? 'bg-green-100 text-green-800' : type === 'error' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`;
            status.textContent = message;
            status.classList.remove('hidden');
        }

        async function clearServiceWorker() {
            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                    showStatus('Service Worker cleared successfully!', 'success');
                } else {
                    showStatus('Service Worker not supported', 'error');
                }
            } catch (error) {
                showStatus('Error clearing Service Worker: ' + error.message, 'error');
            }
        }

        async function clearAllCaches() {
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                    showStatus('All caches cleared successfully!', 'success');
                } else {
                    showStatus('Cache API not supported', 'error');
                }
            } catch (error) {
                showStatus('Error clearing caches: ' + error.message, 'error');
            }
        }

        function reloadPage() {
            showStatus('Performing hard reload...', 'info');
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        }

        // Auto-clear on page load
        window.addEventListener('load', async () => {
            await clearServiceWorker();
            await clearAllCaches();
            showStatus('Cache cleared automatically. You can now test Google OAuth.', 'success');
        });
    </script>
</body>
</html>
