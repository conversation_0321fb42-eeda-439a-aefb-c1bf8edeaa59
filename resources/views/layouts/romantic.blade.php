<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="color-scheme" content="light only">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="{{ App\Models\Setting::get('theme_color', '#C9B6E4') }}">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ config('app.name', 'SettingWala') }}">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Mobile App Icon -->
    @if(App\Models\Setting::get('mobile_icon'))
        <meta name="mobile-app-icon" content="{{ asset('storage/' . App\Models\Setting::get('mobile_icon')) }}">
    @endif

    <!-- PWA Manifest -->
    <link rel="manifest" href="{{ asset('manifest.json') }}">

    <!-- Apple Touch Icons -->
    @php
        $mobileIcon = App\Models\Setting::get('mobile_icon');
        $iconUrl = $mobileIcon ? asset('storage/' . $mobileIcon) : asset('images/icon-192x192.png');
    @endphp
    <link rel="apple-touch-icon" href="{{ $iconUrl }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ $iconUrl }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ $iconUrl }}">

    <title>{{ config('app.name', 'SettingWala') }} - @yield('title', 'Romantic Dating Events')</title>
    <meta name="description" content="@yield('description', 'Join romantic dating events and discover meaningful connections through love-themed experiences.')">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Romantic Theme CSS -->
    <link rel="stylesheet" href="{{ asset('css/romantic-events.css') }}?v={{ time() }}">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@next/dist/aos.css" rel="stylesheet">

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Styles -->
    @stack('styles')


</head>

<body class="bg-gray-50 min-h-screen">
    <div id="app">
        <!-- Navigation -->
        @include('layouts.navigation')

        <!-- Page Heading -->
        @if (isset($header))
            <header class="romantic-nav shadow-sm">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endif

        <!-- Page Content -->
        <main>
            @yield('content')
            @isset($slot)
                {{ $slot }}
            @endisset
        </main>

        <!-- Footer -->
        <x-footer />
    </div>

    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@next/dist/aos.js" defer></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest" defer></script>

    <!-- Initialize Scripts -->
    <script>
        // Remove loading class immediately to prevent FOUC
        document.body.classList.remove('loading');
        document.body.classList.add('loaded');

        // Wait for all resources to load before initializing
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS when available
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,
                    easing: 'ease-in-out',
                    once: true,
                    offset: 100
                });
            }

            // Initialize Lucide Icons when available
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // Button hover effects (heart animations removed)
        document.addEventListener('DOMContentLoaded', function() {
            const romanticButtons = document.querySelectorAll('.btn-romantic');

            romanticButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    // Simple hover effect without heart animation
                    this.style.transform = 'translateY(-2px)';
                });

                button.addEventListener('mouseleave', function() {
                    // Reset hover effect
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // Countdown Timer Function
        function initCountdownTimer(targetDate, elementId) {
            const countdownElement = document.getElementById(elementId);
            if (!countdownElement) return;

            function updateCountdown() {
                const now = new Date().getTime();
                const distance = targetDate - now;

                if (distance < 0) {
                    countdownElement.innerHTML = '<div class="text-center"><h3>Event Started!</h3></div>';
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                countdownElement.innerHTML = `
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="countdown-item">
                                <span class="countdown-number">${days}</span>
                                <span class="countdown-label">Days</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="countdown-item">
                                <span class="countdown-number">${hours}</span>
                                <span class="countdown-label">Hours</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="countdown-item">
                                <span class="countdown-number">${minutes}</span>
                                <span class="countdown-label">Minutes</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="countdown-item">
                                <span class="countdown-number">${seconds}</span>
                                <span class="countdown-label">Seconds</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            updateCountdown();
            setInterval(updateCountdown, 1000);
        }

        // Custom carousel functionality (Tailwind-based)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any custom carousel components here
            // This replaces Bootstrap carousel functionality
        });
    </script>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
