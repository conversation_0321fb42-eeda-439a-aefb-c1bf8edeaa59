@section('title', 'Help Center')
@section('description', 'Find answers to frequently asked questions about SettingWala. Get help with account setup, profile completion, events, payments, and more.')



<x-app-layout>
    <div class="min-h-screen bg-gradient-warm">
        <!-- Hero Section -->
        <x-hero-section
            title="Help Center"
            subtitle="Find answers to frequently asked questions and get the <span class='font-semibold text-gradient-primary'>help</span> you need"
            description="Get the help you need to make the most of your SettingWala experience with our comprehensive FAQ and support resources."
            :showSteps="false"
        />

        <!-- Search Section -->
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8 relative z-10">
            <div class="card-romantic p-8">
                <div class="relative">
                    <input type="text" placeholder="Search for help..." class="w-full px-6 py-4 text-lg border border-warm-300 rounded-2xl focus:ring-4 focus:ring-primary-200 focus:border-primary-400 transition-all duration-200">
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 text-warm-500 hover:text-primary-600 transition-colors duration-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- FAQ Categories -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-warm-900 mb-4">Browse Help Topics</h2>
                <p class="text-xl text-warm-600 max-w-2xl mx-auto">Find answers quickly by browsing our organized help categories</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 text-center hover:-translate-y-1 hover:scale-105 transition-all duration-300">
                    <div class="category-icon w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-warm-900 mb-2">Getting Started</h3>
                    <p class="text-warm-600">Account setup, profile creation, and first steps</p>
                </div>

                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 text-center hover:-translate-y-1 hover:scale-105 transition-all duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 relative z-10">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-warm-900 mb-2">Profile & Settings</h3>
                    <p class="text-warm-600">Managing your profile, photos, and preferences</p>
                </div>

                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 text-center hover:-translate-y-1 hover:scale-105 transition-all duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 relative z-10">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-warm-900 mb-2">Events & Meetings</h3>
                    <p class="text-warm-600">Joining events, meeting people, and event guidelines</p>
                </div>

                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 text-center hover:-translate-y-1 hover:scale-105 transition-all duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 via-pink-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4 relative z-10">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-warm-900 mb-2">Payments & Billing</h3>
                    <p class="text-warm-600">Payment methods, billing, and refund policies</p>
                </div>

                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 text-center hover:-translate-y-1 hover:scale-105 transition-all duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 relative z-10">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-warm-900 mb-2">Safety & Security</h3>
                    <p class="text-warm-600">Account security, privacy, and safety guidelines</p>
                </div>

                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 text-center hover:-translate-y-1 hover:scale-105 transition-all duration-300">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 relative z-10">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-warm-900 mb-2">Technical Support</h3>
                    <p class="text-warm-600">App issues, bugs, and technical troubleshooting</p>
                </div>
            </div>

            <!-- Popular FAQs -->
            <div class="mb-16 faq-container">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-warm-900 mb-4">Frequently Asked Questions</h2>
                    <p class="text-xl text-warm-600 max-w-2xl mx-auto">Get quick answers to the most common questions about SettingWala</p>
                </div>

                <div class="space-y-6" x-data="{ openFaq: null }">
                    <!-- FAQ 1 -->
                    <div class="card-romantic overflow-hidden">
                        <button @click="openFaq = openFaq === 1 ? null : 1" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-warm-50 transition-colors duration-200">
                            <h3 class="text-xl font-semibold text-warm-900">How do I complete my profile to 100%?</h3>
                            <svg class="w-6 h-6 text-warm-500 transform transition-transform duration-200" :class="{ 'rotate-180': openFaq === 1 }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 1" x-transition class="px-8 pb-6">
                            <p class="text-warm-700 leading-relaxed">
                                To complete your profile to 100%, you need to fill out all three sections: Basic Information (name, gender, date of birth), upload a profile picture, and add your interests. A complete profile is required to join any events on SettingWala.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ 2 -->
                    <div class="card-romantic overflow-hidden">
                        <button @click="openFaq = openFaq === 2 ? null : 2" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-warm-50 transition-colors duration-200">
                            <h3 class="text-xl font-semibold text-warm-900">How do payments work for events?</h3>
                            <svg class="w-6 h-6 text-warm-500 transform transition-transform duration-200" :class="{ 'rotate-180': openFaq === 2 }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 2" x-transition class="px-8 pb-6">
                            <p class="text-warm-700 leading-relaxed">
                                SettingWala uses a pay-per-event model. You only pay for the events you want to attend - there are no monthly subscriptions. Payment amounts may vary for different genders and events. We use secure Razorpay payment processing for all transactions.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ 3 -->
                    <div class="card-romantic overflow-hidden">
                        <button @click="openFaq = openFaq === 3 ? null : 3" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-warm-50 transition-colors duration-200">
                            <h3 class="text-xl font-semibold text-warm-900">Can I get a refund if I can't attend an event?</h3>
                            <svg class="w-6 h-6 text-warm-500 transform transition-transform duration-200" :class="{ 'rotate-180': openFaq === 3 }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 3" x-transition class="px-8 pb-6">
                            <p class="text-warm-700 leading-relaxed">
                                Unfortunately, we do not offer refunds for event payments under any circumstances. Please make sure you can attend an event before making a payment. We recommend checking your schedule and the event details carefully before purchasing.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ 4 -->
                    <div class="card-romantic overflow-hidden">
                        <button @click="openFaq = openFaq === 4 ? null : 4" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-warm-50 transition-colors duration-200">
                            <h3 class="text-xl font-semibold text-warm-900">How do I sign up with Google?</h3>
                            <svg class="w-6 h-6 text-warm-500 transform transition-transform duration-200" :class="{ 'rotate-180': openFaq === 4 }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 4" x-transition class="px-8 pb-6">
                            <p class="text-warm-700 leading-relaxed">
                                Click the "Login" button and select "Sign in with Google". You'll be redirected to Google's secure login page. After authentication, you'll be brought back to SettingWala with your account automatically created using your Google profile information.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ 5 -->
                    <div class="card-romantic overflow-hidden">
                        <button @click="openFaq = openFaq === 5 ? null : 5" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-warm-50 transition-colors duration-200">
                            <h3 class="text-xl font-semibold text-warm-900">Are all profiles verified?</h3>
                            <svg class="w-6 h-6 text-warm-500 transform transition-transform duration-200" :class="{ 'rotate-180': openFaq === 5 }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 5" x-transition class="px-8 pb-6">
                            <p class="text-warm-700 leading-relaxed">
                                Yes! Since we use Google OAuth for authentication, all email addresses are automatically verified. Additionally, our team reviews profiles to ensure they meet our community standards. This helps maintain a safe and authentic environment for all members.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ 6 -->
                    <div class="card-romantic overflow-hidden">
                        <button @click="openFaq = openFaq === 6 ? null : 6" class="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-warm-50 transition-colors duration-200">
                            <h3 class="text-xl font-semibold text-warm-900">What types of events do you organize?</h3>
                            <svg class="w-6 h-6 text-warm-500 transform transition-transform duration-200" :class="{ 'rotate-180': openFaq === 6 }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 6" x-transition class="px-8 pb-6">
                            <p class="text-warm-700 leading-relaxed">
                                We organize various types of events including coffee meetups, art gallery visits, hiking adventures, cooking classes, book clubs, and more. All events are designed to facilitate natural conversations and connections in comfortable, public settings.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Support -->
            <div class="bg-gradient-to-r from-primary-50 to-blush-50 rounded-3xl p-12 text-center">
                <h2 class="text-3xl font-bold text-warm-900 mb-6">Still Need Help?</h2>
                <p class="text-xl text-warm-700 mb-8 max-w-2xl mx-auto">
                    Can't find the answer you're looking for? Our support team is here to help you with any questions or issues.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('static.contact-us') }}" class="btn-primary">
                        Contact Support
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </a>
                    <a href="{{ route('static.safety-tips') }}" class="btn-secondary">
                        Safety Guidelines
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
