@section('title', 'Success Stories')
@section('description', 'Read inspiring success stories from couples who found love through SettingWala. Discover how our platform has helped people build meaningful relationships and lasting connections.')

<x-app-layout>
    <!-- Hero Section -->
    <div class="bg-gradient-warm">
        <x-hero-section
            title="Success Stories"
            subtitle="Real couples, real love stories"
            description="Discover how SettingWala has helped thousands of people find their perfect match and build lasting relationships."
        />
    </div>

    <!-- Success Stories Section -->
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Love Found Here</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    These are just a few of the many success stories from our community. Every day, new connections are made and relationships bloom through SettingWala.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Story 1 -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            A&R
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold text-gray-900">Anita & Raj</h3>
                            <p class="text-sm text-gray-500">Together for 2 years</p>
                        </div>
                    </div>
                    <div class="text-3xl text-indigo-400 opacity-70 mb-3">"</div>
                    <p class="text-gray-700 mb-4">
                        "We met through a local event organized on SettingWala. What started as a casual coffee date turned into the most beautiful relationship of our lives. Thank you for bringing us together!"
                    </p>
                    <div class="flex items-center text-yellow-400">
                        <span class="text-sm text-gray-600 ml-2">Married in 2023</span>
                    </div>
                </div>

                <!-- Story 2 -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            P&S
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold text-gray-900">Priya & Suresh</h3>
                            <p class="text-sm text-gray-500">Together for 1.5 years</p>
                        </div>
                    </div>
                    <div class="text-3xl text-indigo-400 opacity-70 mb-3">"</div>
                    <p class="text-gray-700 mb-4">
                        "The safety features and genuine profiles on SettingWala gave us confidence to meet. We connected over our shared love for travel and adventure. Now we're planning our wedding!"
                    </p>
                    <div class="flex items-center text-yellow-400">
                        <span class="text-sm text-gray-600 ml-2">Engaged in 2024</span>
                    </div>
                </div>

                <!-- Story 3 -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-teal-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            M&A
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold text-gray-900">Meera & Arjun</h3>
                            <p class="text-sm text-gray-500">Together for 3 years</p>
                        </div>
                    </div>
                    <div class="text-3xl text-green-400 opacity-70 mb-3">"</div>
                    <p class="text-gray-700 mb-4">
                        "After years of unsuccessful dating, SettingWala helped us find each other. The platform's focus on meaningful connections rather than superficial matches made all the difference."
                    </p>
                    <div class="flex items-center text-yellow-400">
                        <span class="text-sm text-gray-600 ml-2">Married in 2022</span>
                    </div>
                </div>

                <!-- Story 4 -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-red-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            K&V
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold text-gray-900">Kavya & Vikram</h3>
                            <p class="text-sm text-gray-500">Together for 2.5 years</p>
                        </div>
                    </div>
                    <div class="text-3xl text-red-400 opacity-70 mb-3">"</div>
                    <p class="text-gray-700 mb-4">
                        "We were both busy professionals who had given up on finding love. SettingWala's time-spending feature allowed us to connect despite our hectic schedules. Best decision ever!"
                    </p>
                    <div class="flex items-center text-yellow-400">
                        <span class="text-sm text-gray-600 ml-2">Recently Engaged</span>
                    </div>
                </div>

                <!-- Story 5 -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            N&R
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold text-gray-900">Neha & Rohit</h3>
                            <p class="text-sm text-gray-500">Together for 1 year</p>
                        </div>
                    </div>
                    <div class="text-3xl text-indigo-400 opacity-70 mb-3">"</div>
                    <p class="text-gray-700 mb-4">
                        "The verification process on SettingWala ensured we met genuine people. We bonded over our shared interests and values. Thank you for creating such a trustworthy platform!"
                    </p>
                    <div class="flex items-center text-yellow-400">
                        <span class="text-sm text-gray-600 ml-2">Planning Wedding</span>
                    </div>
                </div>

                <!-- Story 6 -->
                <div class="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            S&D
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold text-gray-900">Shreya & Dev</h3>
                            <p class="text-sm text-gray-500">Together for 6 months</p>
                        </div>
                    </div>
                    <div class="text-3xl text-orange-400 opacity-70 mb-3">"</div>
                    <p class="text-gray-700 mb-4">
                        "We're both introverts who found it hard to meet people. SettingWala's chat feature helped us get comfortable before meeting in person. We're so grateful for this platform!"
                    </p>
                    <div class="flex items-center text-yellow-400">
                        <span class="text-sm text-gray-600 ml-2">Going Strong</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="bg-gradient-to-r from-indigo-600 to-blue-600 py-16">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-white mb-4">Ready to Write Your Own Success Story?</h2>
            <p class="text-xl text-indigo-100 mb-8">
                Join thousands of happy couples who found love through SettingWala
            </p>
            <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                @guest
                    <a href="{{ route('register') }}" class="inline-block bg-white text-indigo-600 font-semibold px-8 py-3 rounded-lg hover:bg-gray-100 transition duration-300">
                        Join Now - It's Free
                    </a>
                    <a href="{{ route('static.how-it-works') }}" class="inline-block border-2 border-white text-white font-semibold px-8 py-3 rounded-lg hover:bg-white hover:text-indigo-600 transition duration-300">
                        Learn How It Works
                    </a>
                @else
                    <a href="{{ route('find-person.index') }}" class="inline-block bg-white text-indigo-600 font-semibold px-8 py-3 rounded-lg hover:bg-gray-100 transition duration-300">
                        Find Your Match
                    </a>
                @endguest
            </div>
        </div>
    </div>
</x-app-layout>
