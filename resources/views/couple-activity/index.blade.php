<x-app-layout>
    @section('title', 'Couple Activity')
    @section('description', 'Find and connect with partners for couple activities')

    <!-- Hero Section -->
    <x-hero-section
        title="Couple Activity"
        subtitle="Connect with like-minded individuals for <span class='font-semibold text-gradient-primary'>couple activities and events</span>"
        description="Browse available users and send requests to those you're interested in connecting with."
        :showSteps="false"
    />

    <div class="min-h-screen bg-gray-50">
        <div class="py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                <!-- Current Partner Alert -->
                @if($currentPartner)
                    <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-sm text-blue-800">
                                You are currently partnered with <strong>{{ $currentPartner->name }}</strong>.
                                You can end this partnership from your profile settings to send new requests.
                            </p>
                        </div>
                    </div>
                @endif

                <!-- Pending Requests Section -->
                @if($pendingRequests->count() > 0)
                    <div class="mb-8 bg-white rounded-lg shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Pending Requests</h3>
                            <p class="text-sm text-gray-600">Users who want to partner with you</p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($pendingRequests as $request)
                                    <div class="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-4 border border-pink-100">
                                        <div class="flex items-center mb-3">
                                            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center text-white font-semibold text-lg mr-3">
                                                {{ substr($request->requester->name, 0, 1) }}
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-900">{{ $request->requester->name }}</h4>
                                                <p class="text-sm text-gray-600">{{ ucfirst($request->requester->gender) }}</p>
                                            </div>
                                        </div>
                                        @if($request->message)
                                            <p class="text-sm text-gray-700 mb-3 italic">"{{ $request->message }}"</p>
                                        @endif
                                        <div class="flex space-x-2">
                                            <form action="{{ route('couple-activity.approve', $request) }}" method="POST" class="flex-1">
                                                @csrf
                                                <button type="submit" class="w-full px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors text-sm">
                                                    Approve
                                                </button>
                                            </form>
                                            <form action="{{ route('couple-activity.reject', $request) }}" method="POST" class="flex-1">
                                                @csrf
                                                <button type="submit" class="w-full px-3 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm">
                                                    Reject
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Available Users Section -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Available Users</h3>
                        <p class="text-sm text-gray-600">Browse and connect with users interested in couple activities</p>
                    </div>
                    <div class="p-6">
                        @if($availableUsers->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                @foreach($availableUsers as $availableUser)
                                    <div class="bg-white rounded-lg border border-gray-200 hover:border-pink-300 hover:shadow-md transition-all duration-200">
                                        <div class="p-4">
                                            <div class="flex items-center mb-3">
                                                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center text-white font-semibold text-lg mr-3">
                                                    {{ substr($availableUser->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <h4 class="font-semibold text-gray-900">{{ $availableUser->name }}</h4>
                                                    <p class="text-sm text-gray-600">{{ ucfirst($availableUser->gender) }}</p>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                    {{ $availableUser->partner_swapping_status === 'available' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                    {{ ucfirst($availableUser->partner_swapping_status) }}
                                                </span>
                                            </div>

                                            @if(!$currentPartner && !auth()->user()->hasPendingRequestWith($availableUser) && !auth()->user()->hasReceivedRequestFrom($availableUser))
                                                <button onclick="openRequestModal({{ $availableUser->id }}, '{{ $availableUser->name }}')"
                                                        class="w-full px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded hover:from-pink-600 hover:to-purple-700 transition-all text-sm">
                                                    Send Request
                                                </button>
                                            @elseif(auth()->user()->hasPendingRequestWith($availableUser))
                                                <button disabled class="w-full px-4 py-2 bg-gray-300 text-gray-500 rounded text-sm cursor-not-allowed">
                                                    Request Sent
                                                </button>
                                            @elseif(auth()->user()->hasReceivedRequestFrom($availableUser))
                                                <button disabled class="w-full px-4 py-2 bg-blue-300 text-blue-700 rounded text-sm cursor-not-allowed">
                                                    Request Received
                                                </button>
                                            @else
                                                <button disabled class="w-full px-4 py-2 bg-gray-300 text-gray-500 rounded text-sm cursor-not-allowed">
                                                    Currently Partnered
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Pagination -->
                            <div class="mt-6">
                                {{ $availableUsers->links() }}
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Available Users</h3>
                                <p class="text-gray-600">There are currently no users available for partner swapping.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Request Modal -->
    <div id="requestModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <form id="requestForm">
                    @csrf
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Send Couple Event Request</h3>
                        <p class="text-sm text-gray-600 mb-4">Send a request to <span id="partnerName" class="font-semibold"></span></p>

                        <div class="mb-4">
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message (Optional)</label>
                            <textarea id="message" name="message" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="Add a personal message..."></textarea>
                        </div>
                    </div>
                    <div class="px-6 py-3 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
                        <button type="button" onclick="closeRequestModal()"
                                class="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded hover:from-pink-600 hover:to-purple-700 transition-all">
                            Send Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentPartnerId = null;

        function openRequestModal(userId, userName) {
            document.getElementById('partnerName').textContent = userName;
            currentPartnerId = userId;
            document.getElementById('requestModal').classList.remove('hidden');
        }

        function closeRequestModal() {
            document.getElementById('requestModal').classList.add('hidden');
            document.getElementById('message').value = '';
            currentPartnerId = null;
        }

        // Close modal when clicking outside
        document.getElementById('requestModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRequestModal();
            }
        });

        // Handle form submission with AJAX
        document.getElementById('requestForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!currentPartnerId) {
                showToast('Error: No partner selected', 'error');
                return;
            }

            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            const formData = {
                partner_id: currentPartnerId,
                type: 'couple_swap',
                message: document.getElementById('message').value
            };

            fetch('/couple-activity/send-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    closeRequestModal();
                    // Reload the page to show updated state
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast(data.message || 'Error sending request', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error sending request. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            });
        });
    </script>

    <!-- Toast Notifications -->
    <x-toast />
</x-app-layout>
