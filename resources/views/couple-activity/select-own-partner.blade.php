@php
    $sentRequestsCount = auth()->user()->sentCoupleActivityRequests()
        ->where('type', 'own_partner')
        ->where('status', 'pending')
        ->count();
@endphp

<x-app-layout>
    @section('title', 'Select Your Partner')
    @section('description', 'Find and select your partner for couple activities')

    <!-- Clean Hero Section -->
    <section class="bg-white relative overflow-hidden">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20 py-16">
            <div class="text-center max-w-4xl mx-auto">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                    <i data-lucide="users" class="w-10 h-10 text-white"></i>
                </div>
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Select Your Partner
                </h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Choose your partner to get started with couple activities and events.
                </p>
            </div>
        </div>
    </section>

    <div class="min-h-screen bg-gray-50">
        <div class="py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                <!-- Step Indicator -->
                <div class="mb-8 bg-white rounded-3xl shadow-lg border border-gray-100 p-8">
                    <div class="flex items-center justify-center">
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full text-sm font-semibold">
                                1
                            </div>
                            <span class="ml-3 text-base font-semibold text-indigo-600">Select Your Partner</span>
                        </div>
                        <div class="flex-1 mx-6 h-px bg-gray-300"></div>
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-10 h-10 bg-gray-300 text-gray-500 rounded-full text-sm font-semibold">
                                2
                            </div>
                            <span class="ml-3 text-base font-medium text-gray-500">Find Couples for Activity</span>
                        </div>
                    </div>
                </div>

                <!-- Request Limit Notice -->
                @if($sentRequestsCount >= 3)
                    <div class="mb-8 bg-red-50 border border-red-200 rounded-2xl p-6">
                        <div class="flex items-start">
                            <div class="flex items-center justify-center w-10 h-10 bg-red-100 rounded-full mr-4">
                                <i data-lucide="alert-triangle" class="w-5 h-5 text-red-600"></i>
                            </div>
                            <div>
                                <h4 class="text-base font-semibold text-red-800 mb-2">Request Limit Reached</h4>
                                <p class="text-sm text-red-700">You have sent the maximum of 3 partner requests. Please wait for responses before sending more requests.</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Pending Partner Requests Section -->
                @if($pendingRequests->count() > 0)
                    <div class="mb-8 bg-white rounded-3xl shadow-lg border border-gray-100">
                        <div class="p-8 border-b border-gray-200">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full flex items-center justify-center">
                                    <i data-lucide="heart" class="w-4 h-4 text-white"></i>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900">Pending Partner Requests</h3>
                            </div>
                            <p class="text-gray-600">Users who want to be your partner</p>
                        </div>
                        <div class="p-8">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($pendingRequests as $request)
                                    <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-6 border border-indigo-200 hover:shadow-lg transition-all duration-300">
                                        <div class="flex items-center mb-4">
                                            <div class="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-indigo-700 flex items-center justify-center text-white font-bold text-lg mr-4">
                                                {{ substr($request->requester->name, 0, 1) }}
                                            </div>
                                            <div>
                                                <h4 class="font-bold text-gray-900 text-lg">{{ $request->requester->name }}</h4>
                                                <p class="text-sm text-gray-600">{{ ucfirst($request->requester->gender) }}</p>
                                            </div>
                                        </div>
                                        @if($request->message)
                                            <div class="bg-white rounded-lg p-3 mb-4">
                                                <p class="text-sm text-gray-700 italic">"{{ $request->message }}"</p>
                                            </div>
                                        @endif
                                        <div class="flex space-x-3">
                                            <form action="{{ route('couple-activity.approve', $request) }}" method="POST" class="flex-1">
                                                @csrf
                                                <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-300 text-sm font-semibold">
                                                    Accept
                                                </button>
                                            </form>
                                            <form action="{{ route('couple-activity.reject', $request) }}" method="POST" class="flex-1">
                                                @csrf
                                                <button type="submit" class="w-full px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-300 text-sm font-semibold">
                                                    Decline
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Sent Requests Section - Compact -->
                @if($sentRequests->count() > 0)
                    <div class="mb-8 bg-white rounded-2xl shadow-lg border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-6 h-6 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                                    <i data-lucide="send" class="w-3 h-3 text-white"></i>
                                </div>
                                <h3 class="text-lg font-bold text-gray-900">Sent Requests</h3>
                                <span class="text-sm text-gray-500">({{ $sentRequests->count() }})</span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                @foreach($sentRequests as $request)
                                    <div class="flex items-center justify-between p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl border border-orange-200 hover:shadow-md transition-all duration-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center text-white font-bold text-sm">
                                                {{ substr($request->partner->name, 0, 1) }}
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-900 text-sm">{{ $request->partner->name }}</h4>
                                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                    <span>{{ ucfirst($request->partner->gender) }}</span>
                                                    <span>•</span>
                                                    <span>{{ $request->created_at->diffForHumans() }}</span>
                                                    @if($request->message)
                                                        <span>•</span>
                                                        <span class="italic">"{{ Str::limit($request->message, 30) }}"</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-700">
                                                <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                                Pending
                                            </span>
                                            <form action="{{ route('couple-activity.cancel-request', $request) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-xs font-medium"
                                                        onclick="return confirm('Cancel this request?')" title="Cancel Request">
                                                    ✕ Cancel
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Available Users Section -->
                <div class="bg-white rounded-3xl shadow-lg border border-gray-100">
                    <div class="p-8 border-b border-gray-200">
                        <div class="flex justify-between items-start">
                            <div>
                                <div class="flex items-center gap-3 mb-2">
                                    <div class="w-8 h-8 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full flex items-center justify-center">
                                        <i data-lucide="users" class="w-4 h-4 text-white"></i>
                                    </div>
                                    <h3 class="text-xl font-bold text-gray-900">Available Users</h3>
                                </div>
                                <p class="text-gray-600">Browse and send partner requests to users you want to be with</p>
                            </div>

                            <div class="text-right bg-gray-50 rounded-2xl p-4">
                                <div class="text-sm font-medium text-gray-700 mb-1">Requests Sent</div>
                                <div class="text-3xl font-bold {{ $sentRequestsCount >= 3 ? 'text-red-600' : 'text-indigo-600' }}">
                                    {{ $sentRequestsCount }}/3
                                </div>
                                @if($sentRequestsCount >= 3)
                                    <div class="text-xs text-red-600 mt-1 font-medium">Maximum reached</div>
                                @else
                                    <div class="text-xs text-gray-500 mt-1">{{ 3 - $sentRequestsCount }} remaining</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        @if($availableUsers->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                                @foreach($availableUsers as $availableUser)
                                    <div class="bg-white rounded-2xl border border-gray-200 hover:border-indigo-300 hover:shadow-xl transition-all duration-300 group">
                                        <div class="p-6">
                                            <div class="flex items-center mb-4">
                                                <div class="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-indigo-700 flex items-center justify-center text-white font-bold text-lg mr-4">
                                                    {{ substr($availableUser->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <h4 class="font-bold text-gray-900 text-lg">{{ $availableUser->name }}</h4>
                                                    <p class="text-sm text-gray-600">{{ ucfirst($availableUser->gender) }}</p>
                                                </div>
                                            </div>

                                            <div class="mb-4">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800">
                                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                                    Available
                                                </span>
                                            </div>

                                            @if(!auth()->user()->hasPendingOwnPartnerRequestWith($availableUser) && !auth()->user()->hasReceivedOwnPartnerRequestFrom($availableUser))
                                                @if($sentRequestsCount >= 3)
                                                    <button disabled class="w-full px-4 py-3 bg-red-100 text-red-700 rounded-xl text-sm cursor-not-allowed font-semibold">
                                                        Request Limit Reached
                                                    </button>
                                                @else
                                                    <button onclick="openPartnerRequestModal({{ $availableUser->id }}, '{{ $availableUser->name }}')"
                                                            class="w-full px-4 py-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-xl hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 text-sm font-semibold group-hover:shadow-lg">
                                                        Send Partner Request
                                                    </button>
                                                @endif
                                            @elseif(auth()->user()->hasPendingOwnPartnerRequestWith($availableUser))
                                                <button disabled class="w-full px-4 py-3 bg-gray-100 text-gray-500 rounded-xl text-sm cursor-not-allowed font-semibold">
                                                    Request Sent
                                                </button>
                                            @else
                                                <button disabled class="w-full px-4 py-3 bg-blue-100 text-blue-700 rounded-xl text-sm cursor-not-allowed font-semibold">
                                                    Request Received
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Pagination -->
                            <div class="mt-8">
                                {{ $availableUsers->links() }}
                            </div>
                        @else
                            <div class="text-center py-16">
                                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i data-lucide="users" class="w-12 h-12 text-gray-400"></i>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 mb-3">No Available Users</h3>
                                <p class="text-gray-600 max-w-md mx-auto">There are currently no users available for partner selection. Check back later or invite friends to join!</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Partner Request Modal -->
    <div id="partnerRequestModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-md w-full">
                <form id="partnerRequestForm">
                    @csrf
                    <input type="hidden" name="type" value="own_partner">
                    <div class="p-8">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="heart" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Send Partner Request</h3>
                            <p class="text-gray-600">Send a partner request to <span id="partnerName" class="font-semibold text-indigo-600"></span></p>
                        </div>

                        <div class="mb-6">
                            <label for="message" class="block text-sm font-semibold text-gray-700 mb-3">Message (Optional)</label>
                            <textarea id="message" name="message" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                                      placeholder="Add a personal message to make your request more appealing..."></textarea>
                        </div>
                    </div>
                    <div class="px-8 py-6 bg-gray-50 flex justify-end space-x-4 rounded-b-3xl">
                        <button type="button" onclick="closePartnerRequestModal()"
                                class="px-6 py-3 text-gray-700 bg-gray-200 rounded-xl hover:bg-gray-300 transition-colors font-semibold">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-xl hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 font-semibold">
                            Send Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentPartnerId = null;

        function openPartnerRequestModal(userId, userName) {
            document.getElementById('partnerName').textContent = userName;
            currentPartnerId = userId;
            document.getElementById('partnerRequestModal').classList.remove('hidden');
        }

        function closePartnerRequestModal() {
            document.getElementById('partnerRequestModal').classList.add('hidden');
            document.getElementById('message').value = '';
            currentPartnerId = null;
        }

        // Close modal when clicking outside
        document.getElementById('partnerRequestModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePartnerRequestModal();
            }
        });

        // Handle form submission with AJAX
        document.getElementById('partnerRequestForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!currentPartnerId) {
                showToast('Error: No partner selected', 'error');
                return;
            }

            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            const formData = {
                partner_id: currentPartnerId,
                type: 'own_partner',
                message: document.getElementById('message').value
            };

            fetch('/couple-activity/send-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    closePartnerRequestModal();
                    // Reload the page to show updated state
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast(data.message || 'Error sending request', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error sending request. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            });
        });
    </script>

    <!-- Toast Notifications -->
    <x-toast />
</x-app-layout>
