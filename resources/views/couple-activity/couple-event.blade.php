<x-app-layout>
    @section('title', 'Couple Events')
    @section('description', 'Browse and join couple events with your partner')

    <!-- Hero Section -->
    <x-hero-section
        title="Couple Events"
        subtitle="Step 2: Join <span class='font-semibold text-gradient-primary'>events</span> with your partner"
        description="Browse available couple events and join them together with your partner."
        :showSteps="false"
    />

    <div class="min-h-screen bg-gray-50">
        <div class="py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

                <!-- Step Indicator -->
                <div class="mb-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div class="flex items-center justify-center">
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-semibold">
                                ✓
                            </div>
                            <span class="ml-2 text-sm font-medium text-green-600">Partner Selected</span>
                        </div>
                        <div class="flex-1 mx-4 h-px bg-green-300"></div>
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-semibold">
                                2
                            </div>
                            <span class="ml-2 text-sm font-medium text-blue-600">Join Couple Events</span>
                        </div>
                    </div>
                </div>

                <!-- Your Partnership Info -->
                <div class="mb-8 bg-white rounded-xl shadow-sm border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-100 bg-gray-50 rounded-t-xl">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                            </svg>
                            Your Partnership
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 items-center">
                            <!-- Current User -->
                            <div class="md:col-span-2">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold text-lg mr-4">
                                        {{ substr(auth()->user()->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">{{ auth()->user()->name }}</h4>
                                        <p class="text-sm text-gray-600">{{ ucfirst(auth()->user()->gender) }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Heart Icon -->
                            <div class="md:col-span-1 text-center">
                                <svg class="w-8 h-8 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                </svg>
                            </div>

                            <!-- Partner -->
                            <div class="md:col-span-2">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-green-600 flex items-center justify-center text-white font-semibold text-lg mr-4">
                                        {{ substr($ownPartner->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">{{ $ownPartner->name }}</h4>
                                        <p class="text-sm text-gray-600">{{ ucfirst($ownPartner->gender) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end mt-6">
                            <form action="{{ route('couple-activity.end-partnership') }}" method="POST"
                                  onsubmit="return confirm('Are you sure you want to end this partnership? This action cannot be undone.')">
                                @csrf
                                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    End Partnership
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                @if(!$coupleEventsEnabled)
                    <!-- Feature Disabled Notice -->
                    <div class="mb-8 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">Couple Events Currently Unavailable</h4>
                                <p class="text-sm text-yellow-700 mt-1">Couple events feature is currently disabled. Please check back later.</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Couple Events Section -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                    <div class="px-6 py-4 border-b border-gray-100 bg-gray-50 rounded-t-xl">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Available Couple Events
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">Join events designed specifically for couples</p>
                    </div>
                    <div class="p-6">
                        @if($coupleEvents->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($coupleEvents as $event)
                                    <div class="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200">
                                        <div class="p-6">
                                            <div class="flex items-center justify-between mb-4">
                                                <h4 class="text-lg font-semibold text-gray-900">{{ $event->event_name }}</h4>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Couple Event
                                                </span>
                                            </div>

                                            <div class="space-y-2 mb-4">
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                    {{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y') }}
                                                </div>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                                                </div>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    {{ $event->location }}
                                                </div>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                    </svg>
                                                    ₹{{ number_format($event->payment_amount_couple, 2) }} per couple
                                                </div>
                                            </div>

                                            @if($event->description)
                                                <p class="text-sm text-gray-700 mb-4">{{ Str::limit($event->description, 100) }}</p>
                                            @endif

                                            @if($event->user_has_paid)
                                                <button disabled class="w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-sm cursor-not-allowed font-medium">
                                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    Already Registered
                                                </button>
                                            @else
                                                <a href="{{ route('event.address.show', $event->id) }}"
                                                   class="w-full inline-block text-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                    </svg>
                                                    Join Event
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Couple Events Available</h3>
                                <p class="text-gray-600">There are currently no couple events available. Check back later for new events!</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <x-toast />
</x-app-layout>
