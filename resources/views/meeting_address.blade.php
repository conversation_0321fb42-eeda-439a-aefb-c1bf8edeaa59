@section('title', 'Connect Through Meaningful Events')
@section('description', 'Join authentic offline dating events and discover meaningful connections. A premium platform for genuine relationships built through real-world interactions.')

<x-app-layout>
    <!-- Clean Hero Section -->
    <section class="bg-white relative overflow-hidden min-h-screen flex items-center">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 pointer-events-none z-10">
            <!-- Background decorative elements removed -->
        </div>

        <div class="container mx-auto px-4 relative z-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center min-h-[75vh]">
                <!-- Hero Content -->
                <div class="space-y-8" data-aos="fade-right">
                    <div class="space-y-6">
                        <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                            Discover Love Through
                            <span class="text-indigo-600">Meaningful Events</span>
                        </h1>
                        <p class="text-xl text-gray-600 leading-relaxed max-w-lg">
                            Join exclusive dating events and discover genuine connections.
                            Experience premium matchmaking through real-world interactions.
                        </p>
                    </div>



                    <div class="flex flex-col sm:flex-row gap-4">
                        @auth
                            <a href="#events-section" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i data-lucide="calendar" class="w-5 h-5 mr-2"></i>
                                Browse Events
                            </a>
                            @if(!$isProfileComplete)
                                <a href="{{ route('profile.edit') }}" class="inline-flex items-center justify-center px-8 py-4 border-2 border-indigo-600 text-indigo-600 font-semibold rounded-full hover:bg-indigo-50 transition-all duration-300">
                                    <i data-lucide="user" class="w-5 h-5 mr-2"></i>
                                    Complete Profile ({{ $profileCompletionPercentage }}%)
                                </a>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i data-lucide="heart" class="w-5 h-5 mr-2"></i>
                                Join Now
                            </a>
                        @endauth
                    </div>
                </div>

                <!-- Hero Visual -->
                <div class="lg:pl-8" data-aos="fade-left" data-aos-delay="200">
                    <div class="bg-white rounded-3xl shadow-2xl p-8 text-center border border-gray-100">
                        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                            <i data-lucide="calendar-heart" class="w-12 h-12 text-white"></i>
                        </div>

                        <h3 class="text-2xl font-bold text-gray-900 mb-4">
                            Premium Events
                        </h3>
                        <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                            Join curated events to meet verified members.
                            Safe, authentic, and meaningful connections await you.
                        </p>


                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Events Section -->
    <section id="events-section" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            @if(count($events) > 0)
                <!-- Section Header -->
                <div class="text-center mb-16" data-aos="fade-up">
                    <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full mb-6">
                        <i data-lucide="calendar-heart" class="w-12 h-12 text-white"></i>
                    </div>
                    <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        Available Events
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Choose from our carefully curated events and start your journey to meaningful connections
                    </p>
                    <div class="flex justify-center gap-8 flex-wrap mt-8">
                        <div class="flex items-center">
                            <i data-lucide="shield-check" class="w-5 h-5 text-green-500 mr-2"></i>
                            <span class="text-gray-600 font-medium">Verified Events</span>
                        </div>
                        <div class="flex items-center">
                            <i data-lucide="users" class="w-5 h-5 text-indigo-600 mr-2"></i>
                            <span class="text-gray-600 font-medium">Like-minded People</span>
                        </div>
                        <div class="flex items-center">
                            <i data-lucide="heart" class="w-5 h-5 text-indigo-600 mr-2"></i>
                            <span class="text-gray-600 font-medium">Safe Environment</span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach ($events as $event)
                        <div class="group" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                            <div class="bg-white rounded-3xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 h-full border border-gray-100 overflow-hidden">
                                <!-- Event Header -->
                                <div class="p-6">
                                    <div class="flex items-start justify-between mb-4">
                                        <span class="inline-block px-3 py-1 rounded-full text-white text-xs font-medium
                                            {{ $event->user_has_paid ? 'bg-gradient-to-r from-green-500 to-emerald-500' : ($event->is_couple_event ? 'bg-gradient-to-r from-purple-600 to-indigo-600' : 'bg-gradient-to-r from-indigo-600 to-indigo-700') }}">
                                            {{ $event->user_has_paid ? 'Registered' : ($event->is_couple_event ? 'Couple Event' : 'Single Event') }}
                                        </span>
                                        @if($event->user_has_paid)
                                            <div class="flex items-center text-green-600">
                                                <i data-lucide="check-circle" class="w-4 h-4"></i>
                                            </div>
                                        @endif
                                    </div>

                                    <h3 class="text-xl font-bold text-gray-900 mb-3 leading-tight">
                                        {{ $event->title }}
                                    </h3>

                                    <p class="text-gray-600 text-sm leading-relaxed mb-4">
                                        {!! Str::limit(strip_tags($event->description), 100) !!}
                                    </p>
                                </div>

                                <!-- Event Details -->
                                <div class="px-6 pb-6">
                                    <div class="space-y-3 mb-6">
                                        @if($event->event_date)
                                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <div class="flex items-center justify-center w-8 h-8 bg-indigo-100 rounded-lg mr-3">
                                                    <i data-lucide="calendar" class="w-4 h-4 text-indigo-600"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="text-sm font-medium text-gray-900">{{ $event->event_date->format('M d, Y') }}</div>
                                                    <div class="text-xs text-gray-500">{{ $event->event_date->format('g:i A') }}</div>
                                                </div>
                                            </div>
                                        @endif
                                        @if($event->location)
                                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <div class="flex items-center justify-center w-8 h-8 bg-indigo-100 rounded-lg mr-3">
                                                    <i data-lucide="map-pin" class="w-4 h-4 text-indigo-600"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="text-sm font-medium text-gray-900">{{ Str::limit($event->location, 35) }}</div>
                                                    <div class="text-xs text-gray-500">Event Location</div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Action Buttons -->
                                    @if($isProfileComplete)
                                        @if($event->user_has_paid)
                                            <a href="{{ route('event.address.show', $event->id) }}" class="w-full bg-gradient-to-r from-indigo-600 to-indigo-700 text-white py-3 px-4 rounded-xl font-medium text-center block hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 text-sm">
                                                <i data-lucide="eye" class="inline w-4 h-4 mr-2"></i>
                                                View Details
                                            </a>
                                        @else
                                            @if($event->is_couple_event && (!auth()->user()->is_couple_activity_enabled || !auth()->user()->hasOwnPartner()))
                                                <div class="space-y-3">
                                                    <a href="{{ route('couple-activity.select-partner') }}" class="w-full border-2 border-indigo-600 text-indigo-600 py-3 px-4 rounded-xl font-medium text-center block hover:bg-indigo-50 transition-all duration-300 text-sm">
                                                        <i data-lucide="users" class="inline w-4 h-4 mr-2"></i>
                                                        Setup Couple Profile
                                                    </a>
                                                    <div class="bg-blue-50 border border-blue-200 text-blue-700 p-3 rounded-lg text-xs">
                                                        <i data-lucide="info" class="inline w-3 h-3 mr-1"></i>
                                                        Couple event - setup required
                                                    </div>
                                                </div>
                                            @else
                                                <a href="{{ route('payment.meeting-event', $event->id) }}" class="w-full bg-gradient-to-r from-indigo-600 to-indigo-700 text-white py-3 px-4 rounded-xl font-medium text-center block hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 text-sm">
                                                    <i data-lucide="heart" class="inline w-4 h-4 mr-2"></i>
                                                    Join Event
                                                </a>
                                            @endif
                                        @endif
                                    @else
                                        <div class="space-y-3">
                                            <a href="{{ route('profile.edit') }}" class="w-full border-2 border-indigo-600 text-indigo-600 py-3 px-4 rounded-xl font-medium text-center block hover:bg-indigo-50 transition-all duration-300 text-sm">
                                                <i data-lucide="user" class="inline w-4 h-4 mr-2"></i>
                                                Complete Profile
                                            </a>
                                            <div class="bg-yellow-50 border border-yellow-200 text-yellow-700 p-3 rounded-lg text-xs">
                                                <i data-lucide="alert-triangle" class="inline w-3 h-3 mr-1"></i>
                                                Profile {{ $profileCompletionPercentage }}% complete
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-20" data-aos="fade-up">
                    <div class="bg-white rounded-3xl shadow-2xl border border-gray-100 py-16 px-8 max-w-4xl mx-auto">
                        <div class="mb-8">
                            <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-r from-indigo-100 to-blue-100 rounded-full mb-6">
                                <i data-lucide="calendar-heart" class="w-16 h-16 text-indigo-600"></i>
                            </div>
                            <h3 class="text-4xl font-bold text-gray-900 mb-4">
                                No Events Available Right Now
                            </h3>
                            <p class="text-gray-600 text-lg">
                                New premium events are coming soon!
                            </p>
                        </div>

                        <p class="text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed text-lg">
                            We're currently preparing amazing events for you! New events are added regularly, so please check back soon for exciting opportunities to meet your perfect match.
                        </p>

                        <!-- What to do while waiting -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12 max-w-3xl mx-auto">
                            <div class="flex items-start p-6 bg-indigo-50 rounded-xl text-left">
                                <i data-lucide="user-check" class="w-6 h-6 text-indigo-600 mr-4 mt-1 flex-shrink-0"></i>
                                <div>
                                    <h6 class="font-semibold text-gray-800 mb-2">Complete Your Profile</h6>
                                    <p class="text-gray-600 text-sm">Get ready for when events become available</p>
                                </div>
                            </div>
                            <div class="flex items-start p-6 bg-indigo-50 rounded-xl text-left">
                                <i data-lucide="bell" class="w-6 h-6 text-indigo-600 mr-4 mt-1 flex-shrink-0"></i>
                                <div>
                                    <h6 class="font-semibold text-gray-800 mb-2">Stay Updated</h6>
                                    <p class="text-gray-600 text-sm">We'll notify you about new events</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            @auth
                                @if(auth()->user()->isAdmin())
                                    <a href="{{ route('admin.meeting-events.create') }}" class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white px-8 py-4 rounded-full font-semibold hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                        <i data-lucide="plus" class="inline w-5 h-5 mr-2"></i>
                                        Add New Event
                                    </a>
                                    <a href="{{ route('admin.meeting-events.index') }}" class="border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full font-semibold hover:bg-indigo-50 transition-all duration-300">
                                        <i data-lucide="settings" class="inline w-5 h-5 mr-2"></i>
                                        Manage Events
                                    </a>
                                @else
                                    @if(!$isProfileComplete)
                                        <a href="{{ route('profile.edit') }}" class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white px-8 py-4 rounded-full font-semibold hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                            <i data-lucide="user" class="inline w-5 h-5 mr-2"></i>
                                            Complete Profile ({{ $profileCompletionPercentage }}%)
                                        </a>
                                    @endif
                                    <a href="{{ route('home') }}" class="border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full font-semibold hover:bg-indigo-50 transition-all duration-300">
                                        <i data-lucide="home" class="inline w-5 h-5 mr-2"></i>
                                        Back to Dashboard
                                    </a>
                                @endif
                            @else
                                <a href="{{ route('login') }}" class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white px-8 py-4 rounded-full font-semibold hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                    <i data-lucide="log-in" class="inline w-5 h-5 mr-2"></i>
                                    Login to Join Events
                                </a>
                                <a href="{{ route('home') }}" class="border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-full font-semibold hover:bg-indigo-50 transition-all duration-300">
                                    <i data-lucide="home" class="inline w-5 h-5 mr-2"></i>
                                    Back to Home
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
                </div>
            @endif

        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Why Choose Our Platform?
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Experience premium matchmaking through authentic events and verified connections.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200">
                <!-- Feature 1 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full mb-6">
                        <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">
                        Verified Events
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        All events are verified and organized by trusted partners to ensure authenticity and safety for genuine connections.
                    </p>
                </div>

                <!-- Feature 2 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-600 to-purple-700 rounded-full mb-6">
                        <i data-lucide="users" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">
                        Like-minded People
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Meet individuals who share your interests, values, and relationship goals in comfortable, real-world settings.
                    </p>
                </div>

                <!-- Feature 3 -->
                <div class="bg-white rounded-3xl shadow-lg p-8 text-center h-full border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-600 to-green-700 rounded-full mb-6">
                        <i data-lucide="heart-handshake" class="w-10 h-10 text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">
                        Secure & Private
                    </h3>
                    <p class="text-gray-600 leading-relaxed">
                        Your privacy is our priority. All events are held in safe venues with proper security measures and friendly staff.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Initialize Lucide Icons -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>

</x-app-layout>
