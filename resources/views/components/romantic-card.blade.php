@props([
    'title' => '',
    'subtitle' => '',
    'image' => null,
    'badge' => null,
    'badgeColor' => 'romantic',
    'showHeader' => true,
    'showFooter' => false,
    'cardClass' => '',
    'headerClass' => '',
    'bodyClass' => '',
    'footerClass' => '',
    'animation' => 'fade-up',
    'animationDelay' => '0'
])

<div class="card h-100 border-0 shadow-sm romantic-card {{ $cardClass }}"
     data-aos="{{ $animation }}"
     @if($animationDelay) data-aos-delay="{{ $animationDelay }}" @endif
     style="border-radius: 16px; transition: all 0.3s ease; cursor: pointer;"
     onmouseover="this.style.transform='translateY(-8px)'; this.style.boxShadow='0 20px 40px rgba(236, 72, 153, 0.15)'"
     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 20px rgba(0, 0, 0, 0.1)'"
>

    <!-- Card Image (if provided) -->
    @if($image)
        <div class="position-relative overflow-hidden" style="border-radius: 16px 16px 0 0;">
            <img src="{{ $image }}" alt="{{ $title }}" class="card-img-top" style="height: 200px; object-fit: cover; transition: transform 0.3s ease;">
            @if($badge)
                <div class="position-absolute top-0 end-0 m-3">
                    <span class="badge text-white px-3 py-2 rounded-pill fw-medium"
                        style="font-size: 0.85rem;
                        @if($badgeColor === 'romantic') background: linear-gradient(135deg, #ec4899 0%, #dc2626 100%);
                        @elseif($badgeColor === 'couple') background: linear-gradient(135deg, #db2777 0%, #7c3aed 100%);
                        @elseif($badgeColor === 'success') background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        @elseif($badgeColor === 'warning') background: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%);
                        @elseif($badgeColor === 'info') background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
                        @else background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
                        @endif">
                        {{ $badge }}
                    </span>
                </div>
            @endif
        </div>
    @endif

    <!-- Card Header -->
    @if($showHeader && ($title || $subtitle || $badge))
        <div class="card-header border-0 bg-transparent {{ $headerClass }}" style="padding: 1.5rem;">
            @if($badge && !$image)
                <div class="mb-3">
                    <span class="badge text-white px-3 py-2 rounded-pill fw-medium"
                        style="font-size: 0.85rem;
                        @if($badgeColor === 'romantic') background: linear-gradient(135deg, #ec4899 0%, #dc2626 100%);
                        @elseif($badgeColor === 'couple') background: linear-gradient(135deg, #db2777 0%, #7c3aed 100%);
                        @elseif($badgeColor === 'success') background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        @elseif($badgeColor === 'warning') background: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%);
                        @elseif($badgeColor === 'info') background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
                        @else background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
                        @endif">
                        {{ $badge }}
                    </span>
                </div>
            @endif

            @if($title)
                <h3 class="card-title h5 fw-semibold text-dark mb-2" style="font-family: 'Playfair Display', serif;">
                    {{ $title }}
                </h3>
            @endif

            @if($subtitle)
                <p class="text-muted mb-0" style="font-size: 0.9rem;">
                    {{ $subtitle }}
                </p>
            @endif
        </div>
    @endif

    <!-- Card Body -->
    <div class="card-body {{ $bodyClass }}" style="padding: 1.5rem;">
        {{ $slot }}
    </div>

    <!-- Card Footer -->
    @if($showFooter)
        <div class="card-footer bg-light border-top {{ $footerClass }}" style="padding: 1.5rem; border-radius: 0 0 16px 16px;">
            {{ $footer ?? '' }}
        </div>
    @endif
</div>


