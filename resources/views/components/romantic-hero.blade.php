@props([
    'title' => 'Romantic Dating Events',
    'subtitle' => 'Find Love Through Meaningful Connections',
    'description' => 'Join our exclusive romantic events and discover your perfect match in a beautiful, love-filled atmosphere.',
    'showCountdown' => false,
    'eventDate' => null,
    'backgroundImage' => null,
    'customClass' => ''
])

<!-- Hero Section -->
<section class="min-h-[70vh] flex items-center relative overflow-hidden {{ $customClass }}" data-aos="fade-in">
    <!-- Floating Hearts -->
    <div class="floating-hearts"></div>
    
    <!-- Background Overlay -->
    @if($backgroundImage)
        <div class="position-absolute top-0 start-0 w-100 h-100" 
             style="background-image: url('{{ $backgroundImage }}'); background-size: cover; background-position: center; opacity: 0.1; z-index: 0;"></div>
    @endif
    
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="text-center">
                    <!-- Main Title -->
                    <h1 class="mb-4 text-4xl md:text-5xl lg:text-6xl font-bold leading-tight" data-aos="fade-up" data-aos-delay="100">
                        {!! $title !!}
                    </h1>
                    
                    <!-- Subtitle -->
                    @if($subtitle)
                        <h2 class="h3 mb-4 text-muted" data-aos="fade-up" data-aos-delay="200" style="font-family: 'Nunito', sans-serif; font-weight: 500;">
                            {!! $subtitle !!}
                        </h2>
                    @endif
                    
                    <!-- Description -->
                    @if($description)
                        <p class="lead mb-5 text-muted" data-aos="fade-up" data-aos-delay="300" style="max-width: 600px; margin: 0 auto;">
                            {{ $description }}
                        </p>
                    @endif
                    
                    <!-- Countdown Timer -->
                    @if($showCountdown && $eventDate)
                        <div class="countdown-timer mb-5" data-aos="fade-up" data-aos-delay="400">
                            <h3 class="mb-4" style="font-family: 'Playfair Display', serif;">Event Starts In</h3>
                            <div id="countdown-timer"></div>
                        </div>
                        
                        @push('scripts')
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const eventDate = new Date('{{ $eventDate }}').getTime();
                                initCountdownTimer(eventDate, 'countdown-timer');
                            });
                        </script>
                        @endpush
                    @endif
                    
                    <!-- Call to Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center" data-aos="fade-up" data-aos-delay="500">
                        {{ $slot }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Decorative Elements -->
    <!-- Background decorative elements removed -->
</section>


