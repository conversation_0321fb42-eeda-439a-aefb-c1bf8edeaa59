<!-- Modern Footer Component -->
<footer class="bg-gray-900 text-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 pointer-events-none opacity-5">
        <!-- Background decorative elements removed -->
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Main Footer Content -->
        <div class="py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="lg:col-span-1">
                    <div class="mb-6">
                        @if(App\Models\Setting::get('footer_logo'))
                            <!-- Custom Footer Logo -->
                            <img src="{{ asset('storage/' . App\Models\Setting::get('footer_logo')) }}"
                                 alt="{{ config('app.name', 'SettingWala') }}"
                                 class="h-12 w-auto">
                        @else
                            <!-- Default Brand -->
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-full flex items-center justify-center">
                                    <i data-lucide="heart" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xl font-bold text-white">{{ config('app.name', 'SettingWala') }}</span>
                            </div>
                        @endif
                    </div>
                    <p class="text-gray-300 leading-relaxed text-sm mb-6">
                        India's premier dating platform connecting users through meaningful offline events. Experience authentic relationships built on real-world connections and shared experiences.
                    </p>

                    <!-- Social Media Links -->
                    @php
                        $socialMediaLinks = [
                            'facebook_url' => ['name' => 'Facebook', 'icon' => 'facebook'],
                            'instagram_url' => ['name' => 'Instagram', 'icon' => 'instagram'],
                            'twitter_url' => ['name' => 'Twitter', 'icon' => 'twitter'],
                            'linkedin_url' => ['name' => 'LinkedIn', 'icon' => 'linkedin'],
                            'youtube_url' => ['name' => 'YouTube', 'icon' => 'youtube'],
                            'whatsapp_url' => ['name' => 'WhatsApp', 'icon' => 'message-circle']
                        ];
                        $hasSocialLinks = false;
                        foreach($socialMediaLinks as $key => $platform) {
                            if(App\Models\Setting::get($key)) {
                                $hasSocialLinks = true;
                                break;
                            }
                        }
                    @endphp

                    @if($hasSocialLinks)
                        <div class="mt-6">
                            <h4 class="text-sm font-semibold text-white mb-4">Follow Us</h4>
                            <div class="flex gap-3">
                                @foreach($socialMediaLinks as $key => $platform)
                                    @php $url = App\Models\Setting::get($key); @endphp
                                    @if($url)
                                        <a href="{{ $url }}"
                                           target="_blank"
                                           rel="noopener noreferrer"
                                           class="w-10 h-10 bg-gray-800 hover:bg-indigo-600 rounded-full flex items-center justify-center transition-colors duration-300"
                                           aria-label="{{ $platform['name'] }}">
                                            <i data-lucide="{{ $platform['icon'] }}" class="w-5 h-5"></i>
                                        </a>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-6">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="{{ route('static.about-us') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="heart" class="w-4 h-4"></i>
                            About Us
                        </a></li>
                        <li><a href="{{ route('static.how-it-works') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="help-circle" class="w-4 h-4"></i>
                            How It Works
                        </a></li>
                        <li><a href="{{ route('static.safety-tips') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="shield-check" class="w-4 h-4"></i>
                            Safety Tips
                        </a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-6">Support</h3>
                    <ul class="space-y-3">
                        <li><a href="{{ route('static.contact-us') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="mail" class="w-4 h-4"></i>
                            Contact Us
                        </a></li>
                        <li><a href="{{ route('static.privacy-policy') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="shield" class="w-4 h-4"></i>
                            Privacy Policy
                        </a></li>
                        <li><a href="{{ route('static.terms-of-service') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="file-text" class="w-4 h-4"></i>
                            Terms of Service
                        </a></li>
                        <li><a href="{{ route('static.refund-policy') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                            <i data-lucide="rotate-ccw" class="w-4 h-4"></i>
                            Refund Policy
                        </a></li>
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-6">Features</h3>
                    <ul class="space-y-3">
                        @auth
                            @if(\App\Models\Feature::isEnabled('meeting_events'))
                                <li><a href="{{ route('event.address') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                                    <i data-lucide="calendar-heart" class="w-4 h-4"></i>
                                    Browse Events
                                </a></li>
                            @endif
                            @if(\App\Models\Feature::isEnabled('time_spending') && auth()->user()->hasActiveChatSessions())
                                <li><a href="{{ route('chat.index') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                                    <i data-lucide="message-heart" class="w-4 h-4"></i>
                                    Chat
                                </a></li>
                            @endif
                            <li><a href="{{ route('profile.edit') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                                <i data-lucide="user" class="w-4 h-4"></i>
                                My Profile
                            </a></li>
                            @if(auth()->user()->is_time_spending_enabled)
                                <li><a href="{{ route('calendar.index') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                                    <i data-lucide="calendar-days" class="w-4 h-4"></i>
                                    My Bookings
                                </a></li>
                            @endif
                        @else
                            <li><a href="{{ route('register') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                                <i data-lucide="heart" class="w-4 h-4"></i>
                                Join Now
                            </a></li>
                            <li><a href="{{ route('static.help-center') }}" class="text-gray-300 hover:text-indigo-400 transition-colors duration-300 flex items-center gap-2 text-sm">
                                <i data-lucide="help-circle" class="w-4 h-4"></i>
                                Help Center
                            </a></li>
                        @endauth
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Footer -->
        <div class="border-t border-gray-700 pt-8">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-center md:text-left">
                    <p class="text-gray-400 text-sm">
                        {!! App\Models\Setting::getCopyrightText() !!}
                    </p>
                </div>
                <div class="flex flex-wrap justify-center md:justify-end gap-6">
                    <div class="flex items-center gap-2 text-gray-400 text-sm">
                        <i data-lucide="shield-check" class="w-4 h-4 text-green-500"></i>
                        <span>100% Verified</span>
                    </div>
                    <div class="flex items-center gap-2 text-gray-400 text-sm">
                        <i data-lucide="lock" class="w-4 h-4 text-indigo-500"></i>
                        <span>Secure & Private</span>
                    </div>
                    <div class="flex items-center gap-2 text-gray-400 text-sm">
                        <i data-lucide="heart" class="w-4 h-4 text-indigo-500"></i>
                        <span>Trusted Platform</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>


