@props([
    'eventDate' => null,
    'eventTime' => null,
    'title' => 'Event Starts In',
    'completedMessage' => 'Event Started!',
    'timerId' => 'countdown-timer-' . uniqid(),
    'showTitle' => true,
    'size' => 'normal', // normal, large, small
    'theme' => 'romantic', // romantic, minimal
    'animation' => 'fade-up',
    'animationDelay' => '0'
])

@php
    // Combine date and time if both provided
    $targetDateTime = null;
    if ($eventDate) {
        if ($eventTime) {
            $targetDateTime = $eventDate . ' ' . $eventTime;
        } else {
            $targetDateTime = $eventDate;
        }
    }
@endphp

@if($targetDateTime)
<div class="
    @if($theme === 'romantic') bg-gradient-to-r from-pink-500 to-red-500 text-white @else bg-white text-gray-900 border-2 border-pink-500 @endif
    @if($size === 'large') p-12 @elseif($size === 'small') p-6 @else p-8 @endif
    rounded-2xl text-center relative overflow-hidden shadow-lg"
     data-aos="{{ $animation }}"
     @if($animationDelay) data-aos-delay="{{ $animationDelay }}" @endif>

    @if($showTitle)
        <h3 class="
            @if($size === 'large') text-3xl @elseif($size === 'small') text-xl @else text-2xl @endif
            font-semibold mb-6 flex items-center justify-center">
            <i data-lucide="clock" class="me-2"></i>
            {{ $title }}
        </h3>
    @endif

    <div id="{{ $timerId }}">
        <!-- Countdown will be populated by JavaScript -->
        <div class="grid grid-cols-4 gap-4 text-center">
            <div>
                <div class="inline-block mx-2">
                    <span class="
                        @if($size === 'large') text-5xl @elseif($size === 'small') text-2xl @else text-4xl @endif
                        font-bold block leading-none mb-2">--</span>
                    <span class="
                        @if($size === 'large') text-base @elseif($size === 'small') text-xs @else text-sm @endif
                        opacity-90 uppercase tracking-wider font-semibold">Days</span>
                </div>
            </div>
            <div>
                <div class="inline-block mx-2">
                    <span class="
                        @if($size === 'large') text-5xl @elseif($size === 'small') text-2xl @else text-4xl @endif
                        font-bold block leading-none mb-2">--</span>
                    <span class="
                        @if($size === 'large') text-base @elseif($size === 'small') text-xs @else text-sm @endif
                        opacity-90 uppercase tracking-wider font-semibold">Hours</span>
                </div>
            </div>
            <div>
                <div class="inline-block mx-2">
                    <span class="
                        @if($size === 'large') text-5xl @elseif($size === 'small') text-2xl @else text-4xl @endif
                        font-bold block leading-none mb-2">--</span>
                    <span class="
                        @if($size === 'large') text-base @elseif($size === 'small') text-xs @else text-sm @endif
                        opacity-90 uppercase tracking-wider font-semibold">Minutes</span>
                </div>
            </div>
            <div>
                <div class="inline-block mx-2">
                    <span class="
                        @if($size === 'large') text-5xl @elseif($size === 'small') text-2xl @else text-4xl @endif
                        font-bold block leading-none mb-2">--</span>
                    <span class="
                        @if($size === 'large') text-base @elseif($size === 'small') text-xs @else text-sm @endif
                        opacity-90 uppercase tracking-wider font-semibold">Seconds</span>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const targetDate = new Date('{{ $targetDateTime }}').getTime();
    const timerId = '{{ $timerId }}';
    const completedMessage = '{{ $completedMessage }}';
    
    function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetDate - now;
        const countdownElement = document.getElementById(timerId);
        
        if (!countdownElement) return;

        if (distance < 0) {
            countdownElement.innerHTML = `
                <div class="text-center">
                    <div class="completed-message">
                        <i data-lucide="heart" class="heart-beat mb-3" style="width: 3rem; height: 3rem; color: var(--rose-pink);"></i>
                        <h3 class="mb-0">${completedMessage}</h3>
                    </div>
                </div>
            `;
            // Re-initialize Lucide icons for the new content
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
            return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        countdownElement.innerHTML = `
            <div class="grid grid-cols-4 gap-4 text-center">
                <div>
                    <div class="inline-block mx-2">
                        <span class="text-4xl font-bold block leading-none mb-2">${days.toString().padStart(2, '0')}</span>
                        <span class="text-sm opacity-90 uppercase tracking-wider font-semibold">Days</span>
                    </div>
                </div>
                <div>
                    <div class="inline-block mx-2">
                        <span class="text-4xl font-bold block leading-none mb-2">${hours.toString().padStart(2, '0')}</span>
                        <span class="text-sm opacity-90 uppercase tracking-wider font-semibold">Hours</span>
                    </div>
                </div>
                <div>
                    <div class="inline-block mx-2">
                        <span class="text-4xl font-bold block leading-none mb-2">${minutes.toString().padStart(2, '0')}</span>
                        <span class="text-sm opacity-90 uppercase tracking-wider font-semibold">Minutes</span>
                    </div>
                </div>
                <div>
                    <div class="inline-block mx-2">
                        <span class="text-4xl font-bold block leading-none mb-2">${seconds.toString().padStart(2, '0')}</span>
                        <span class="text-sm opacity-90 uppercase tracking-wider font-semibold">Seconds</span>
                    </div>
                </div>
            </div>
        `;
    }

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);
    
    // Store interval ID for cleanup if needed
    window.countdownIntervals = window.countdownIntervals || {};
    window.countdownIntervals[timerId] = interval;
});
</script>
@endpush



@endif
