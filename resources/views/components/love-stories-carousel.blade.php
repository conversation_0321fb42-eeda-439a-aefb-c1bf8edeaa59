@props([
    'stories' => [],
    'title' => 'Love Stories',
    'subtitle' => 'Real couples who found love through our events',
    'showTitle' => true,
    'autoplay' => true,
    'interval' => 5000,
    'carouselId' => 'love-stories-' . uniqid(),
    'animation' => 'fade-up',
    'animationDelay' => '0'
])

@php
    // Default stories if none provided
    $defaultStories = [
        [
            'name1' => 'Priya',
            'name2' => 'Arjun',
            'story' => 'We met at a romantic dinner event and instantly connected over our love for classical music. Six months later, we\'re planning our wedding!',
            'location' => 'Mumbai',
            'event' => 'Candlelight Dinner',
            'image1' => null,
            'image2' => null
        ],
        [
            'name1' => 'Sneha',
            'name2' => 'V<PERSON>ram',
            'story' => 'The couple\'s cooking class brought us together. We laughed, we cooked, and we fell in love. Now we cook together every weekend!',
            'location' => 'Delhi',
            'event' => 'Cooking Workshop',
            'image1' => null,
            'image2' => null
        ],
        [
            'name1' => 'Ananya',
            'name2' => 'Rohan',
            'story' => 'A beautiful evening at the art gallery event led to endless conversations about life and dreams. We\'ve been inseparable ever since!',
            'location' => 'Bangalore',
            'event' => 'Art Gallery Night',
            'image1' => null,
            'image2' => null
        ]
    ];
    
    $displayStories = !empty($stories) ? $stories : $defaultStories;
@endphp

<section class="love-stories-carousel py-5" 
         data-aos="{{ $animation }}" 
         @if($animationDelay) data-aos-delay="{{ $animationDelay }}" @endif>
    <div class="container">
        @if($showTitle)
            <div class="text-center mb-5">
                <h2 class="display-5 mb-3" style="font-family: 'Playfair Display', serif; color: var(--charcoal);">
                    <i data-lucide="heart" class="me-3 heart-beat" style="color: var(--rose-pink);"></i>
                    {{ $title }}
                </h2>
                @if($subtitle)
                    <p class="lead text-muted">{{ $subtitle }}</p>
                @endif
            </div>
        @endif
        
        <div id="{{ $carouselId }}" class="carousel slide" data-bs-ride="carousel" @if($autoplay) data-bs-interval="{{ $interval }}" @endif>
            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($displayStories as $index => $story)
                    <button type="button" 
                            data-bs-target="#{{ $carouselId }}" 
                            data-bs-slide-to="{{ $index }}" 
                            @if($index === 0) class="active" aria-current="true" @endif
                            aria-label="Story {{ $index + 1 }}">
                    </button>
                @endforeach
            </div>
            
            <!-- Carousel Items -->
            <div class="carousel-inner">
                @foreach($displayStories as $index => $story)
                    <div class="carousel-item @if($index === 0) active @endif">
                        <div class="row justify-content-center">
                            <div class="col-lg-8 col-xl-6">
                                <div class="story-card">
                                    <!-- Couple Avatars -->
                                    <div class="couple-avatars mb-4">
                                        <div class="avatar-container">
                                            <div class="story-avatar">
                                                @if(isset($story['image1']) && $story['image1'])
                                                    <img src="{{ $story['image1'] }}" alt="{{ $story['name1'] }}" class="w-100 h-100 object-fit-cover">
                                                @else
                                                    {{ substr($story['name1'], 0, 1) }}
                                                @endif
                                            </div>
                                            <div class="story-avatar">
                                                @if(isset($story['image2']) && $story['image2'])
                                                    <img src="{{ $story['image2'] }}" alt="{{ $story['name2'] }}" class="w-100 h-100 object-fit-cover">
                                                @else
                                                    {{ substr($story['name2'], 0, 1) }}
                                                @endif
                                            </div>
                                        </div>
                                        <div class="heart-connector">
                                            <i data-lucide="heart" class="w-4 h-4 text-pink-500"></i>
                                        </div>
                                    </div>
                                    
                                    <!-- Story Content -->
                                    <blockquote class="story-quote mb-4">
                                        "{{ $story['story'] }}"
                                    </blockquote>
                                    
                                    <!-- Couple Names -->
                                    <h4 class="couple-names mb-2">
                                        {{ $story['name1'] }} & {{ $story['name2'] }}
                                    </h4>
                                    
                                    <!-- Event & Location -->
                                    <div class="story-meta">
                                        <span class="event-badge me-3">
                                            <i data-lucide="calendar" class="me-1"></i>
                                            {{ $story['event'] }}
                                        </span>
                                        <span class="location-badge">
                                            <i data-lucide="map-pin" class="me-1"></i>
                                            {{ $story['location'] }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#{{ $carouselId }}" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#{{ $carouselId }}" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>
        </div>
    </div>
</section>


