@props([
    'title' => 'How It Works',
    'subtitle' => 'Finding your perfect match is simple with SettingWala',
    'description' => 'Follow our easy 4-step process to start your journey to meaningful relationships and lasting love.',
    'showSteps' => true,
    'customClass' => '',
    'backgroundGradient' => 'linear-gradient(135deg, rgba(255, 182, 193, 0.3) 0%, rgba(173, 216, 230, 0.3) 50%, rgba(221, 160, 221, 0.3) 100%)'
])



<!-- Enhanced Hero Section -->
<div class="relative overflow-hidden py-24 lg:py-32 {{ $customClass }}" style="background: {{ $backgroundGradient }}">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary-200 to-blush-200 rounded-full opacity-20 animate-pulse-soft"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-mauve-200 to-primary-200 rounded-full opacity-20 animate-pulse-soft" style="animation-delay: 1s;"></div>

        @if($showSteps)
            <!-- Floating Step Numbers -->
            <div class="absolute top-20 left-1/4 animate-bounce opacity-20" style="animation-delay: 2s;">
                <div class="w-16 h-16 bg-gradient-to-r from-indigo-300 to-pink-300 rounded-full flex items-center justify-center text-white font-bold text-xl">1</div>
            </div>
            <div class="absolute top-32 right-1/3 animate-bounce opacity-20" style="animation-delay: 3s;">
                <div class="w-12 h-12 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full flex items-center justify-center text-white font-bold">2</div>
            </div>
            <div class="absolute bottom-40 left-1/3 animate-bounce opacity-20" style="animation-delay: 4s;">
                <div class="w-14 h-14 bg-gradient-to-r from-purple-300 to-indigo-300 rounded-full flex items-center justify-center text-white font-bold">3</div>
            </div>
            <div class="absolute bottom-20 right-1/4 animate-bounce opacity-20" style="animation-delay: 5s;">
                <div class="w-10 h-10 bg-gradient-to-r from-indigo-300 to-pink-300 rounded-full flex items-center justify-center text-white font-bold text-sm">4</div>
            </div>
        @endif
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Enhanced Typography -->
        <div class="animate-slide-up">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 leading-tight text-gradient-romantic" style="line-height: 1.2;">
                {!! $title !!}
                @if($showSteps)
                    <span class="relative inline-block">
                        <div class="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-indigo-400 to-pink-400 rounded-full transform scale-x-0 animate-pulse" style="animation-delay: 1s;"></div>
                    </span>
                @endif
            </h1>
        </div>

        <div class="animate-slide-up" style="animation-delay: 0.3s;">
            <p class="text-xl md:text-2xl text-warm-700 max-w-4xl mx-auto leading-relaxed font-light mb-8">
                {!! $subtitle !!}
            </p>
            @if(!empty($description))
                <p class="text-lg text-warm-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $description }}
                </p>
            @endif
        </div>

        @if($showSteps)
            <!-- Step Progress Indicator -->
            <div class="mt-12 animate-slide-up" style="animation-delay: 0.6s;">
                <div class="flex justify-center items-center space-x-4 max-w-md mx-auto">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                        <div class="w-12 h-1 bg-gradient-to-r from-primary-400 to-blush-400 rounded-full"></div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-blush-500 to-blush-600 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                        <div class="w-12 h-1 bg-gradient-to-r from-blush-400 to-mauve-400 rounded-full"></div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-mauve-500 to-mauve-600 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                        <div class="w-12 h-1 bg-gradient-to-r from-mauve-400 to-primary-400 rounded-full"></div>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-r from-primary-500 via-blush-500 to-mauve-500 rounded-full flex items-center justify-center text-white font-bold text-sm">4</div>
                </div>
            </div>
        @endif
    </div>
</div>
