<x-app-layout>
    <!-- Hero Section -->
    <x-hero-section
        title="Find Your Perfect Match"
        subtitle="Discover verified members offering <span class='font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent'>quality time</span> spending services"
        description="Connect with genuine people through our premium platform"
        :showSteps="false"
        backgroundGradient="linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(147, 51, 234, 0.3) 50%, rgba(236, 72, 153, 0.3) 100%)"
    />

    <!-- Main Content -->
    <section class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Enhanced Filter Section -->
            <div class="bg-white/80 backdrop-blur-md rounded-xl shadow-lg border border-white/50 p-6 mb-8">
                <div class="mb-4">
                    <h3 class="text-lg font-bold text-gray-800 mb-2">Find Available Members</h3>
                    <p class="text-sm text-gray-600">Filter members by availability, location, price range, and more to find your perfect match.</p>
                </div>
                <form method="GET" action="{{ route('find-person.index') }}">
                    <div class="grid grid-cols-1 lg:grid-cols-6 gap-6 items-center">
                        <!-- Gender Filter -->
                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-2">Gender</label>
                            <select name="gender" id="gender" class="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:border-indigo-500 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md">
                                <option value="all" {{ $gender === 'all' ? 'selected' : '' }}>All Genders</option>
                                <option value="male" {{ $gender === 'male' ? 'selected' : '' }}>Male</option>
                                <option value="female" {{ $gender === 'female' ? 'selected' : '' }}>Female</option>
                            </select>
                        </div>

                        <!-- Location Filter -->
                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-2">Location</label>
                            <select name="location" id="location" class="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:border-purple-500 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md">
                                <option value="">All Locations</option>
                                @foreach($availableLocations as $availableLocation)
                                    <option value="{{ $availableLocation }}" {{ $location === $availableLocation ? 'selected' : '' }}>
                                        {{ $availableLocation }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Date Filter -->
                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-2">
                                Available Date
                                <span class="text-xs font-normal text-gray-500">(Optional)</span>
                            </label>
                            <input type="date"
                                   name="availability_date"
                                   id="availability_date"
                                   class="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:border-pink-500 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                   value="{{ $availabilityDate }}"
                                   min="{{ date('Y-m-d') }}"
                                   title="Select a date to see only members available on that day">
                        </div>

                        <!-- Time Filter -->
                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-2">
                                Available Time
                                <span class="text-xs font-normal text-gray-500">(Optional)</span>
                            </label>
                            <input type="time"
                                   name="availability_time"
                                   id="availability_time"
                                   class="w-full px-4 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:border-pink-500 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                   value="{{ $availabilityTime }}"
                                   title="Select a time to see only members available at that time">
                        </div>

                        <!-- Price Range -->
                        <div>
                            <div class="flex items-center gap-3">
                                <span class="text-sm font-medium text-blue-600 whitespace-nowrap" id="min-display">₹{{ $minPrice ?: ($priceRange->min_price ?? 0) }}</span>

                                <div class="flex-1 relative">
                                    <!-- Track -->
                                    <div class="absolute top-1/2 transform -translate-y-1/2 w-full h-2 bg-gray-200 rounded-full"></div>
                                    <!-- Active Track -->
                                    <div id="active-track" class="absolute top-1/2 transform -translate-y-1/2 h-2 bg-blue-500 rounded-full"></div>

                                    <!-- Range Inputs -->
                                    <input type="range"
                                           name="min_price"
                                           id="min_price"
                                           class="absolute top-1/2 transform -translate-y-1/2 w-full h-2 bg-transparent appearance-none cursor-pointer range-slider"
                                           min="0"
                                           max="{{ $priceRange->max_price ?? 10000 }}"
                                           step="50"
                                           value="{{ $minPrice ?: ($priceRange->min_price ?? 0) }}"
                                           oninput="updateDualRange()">

                                    <input type="range"
                                           name="max_price"
                                           id="max_price"
                                           class="absolute top-1/2 transform -translate-y-1/2 w-full h-2 bg-transparent appearance-none cursor-pointer range-slider"
                                           min="0"
                                           max="{{ $priceRange->max_price ?? 10000 }}"
                                           step="50"
                                           value="{{ $maxPrice ?: ($priceRange->max_price ?? 10000) }}"
                                           oninput="updateDualRange()">
                                </div>

                                <span class="text-sm font-medium text-blue-600 whitespace-nowrap" id="max-display">₹{{ $maxPrice ?: ($priceRange->max_price ?? 10000) }}</span>
                            </div>
                        </div>

                        <!-- Search and Clear Buttons -->
                        <div class="flex gap-3">
                            <button type="submit" class="flex-1 px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg transition-all duration-300 font-semibold shadow-lg hover:shadow-xl hover:shadow-purple-500/30 hover:brightness-110">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Search
                            </button>
                            @if($gender !== 'all' || $minPrice || $maxPrice || $location || $availabilityDate || $availabilityTime)
                                <a href="{{ route('find-person.index') }}" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-all duration-300 font-semibold shadow-sm hover:shadow-md">
                                    Clear
                                </a>
                            @endif
                        </div>
                    </div>
                </form>
            </div>

            <!-- Results Section with Background -->
            @if($users->count() > 0)
                <!-- Container with Light Gradient Background -->
                <div class="relative rounded-2xl p-6 mb-6" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);">
                    <!-- Subtle Background Pattern -->
                    <div class="absolute inset-0 opacity-30 rounded-2xl" style="background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);"></div>

                    <div class="relative z-10">
                        <!-- Results Count -->
                        <div class="mb-4 flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-700 font-medium">
                                    Showing {{ $users->firstItem() }}-{{ $users->lastItem() }} of {{ $users->total() }} results
                                    @if($availabilityDate && $availabilityTime)
                                        <span class="inline-block ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-700">
                                            Available on {{ date('M d, Y', strtotime($availabilityDate)) }} at {{ date('g:i A', strtotime($availabilityTime)) }}
                                        </span>
                                    @endif
                                </p>
                            </div>
                            <p class="text-xs text-gray-600">
                                {{ $totalCount }} total members
                            </p>
                        </div>

                        <!-- Users Grid -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            @foreach($users as $user)
                                <div class="group">
                                    <!-- White Blurred Card -->
                                    <div class="bg-white/70 backdrop-blur-md hover:bg-white/90 hover:backdrop-blur-lg rounded-xl p-4 border border-white/50 hover:border-indigo-300 shadow-lg hover:shadow-xl hover:shadow-indigo-500/20 transition-all duration-300 hover:bg-gradient-to-br hover:from-indigo-50/40 hover:to-purple-50/40">
                                        <!-- Gender Badge (Top Left) and Price (Top Right) -->
                                        <div class="flex justify-between items-center mb-3">
                                            <!-- Gender Badge - Left -->
                                            <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full {{ $user->gender === 'male' ? 'bg-blue-100 text-blue-700' : 'bg-pink-100 text-pink-700' }}">
                                                {{ ucfirst($user->gender) }}
                                            </span>

                                            <!-- Price - Right -->
                                            <span class="text-base font-bold text-gray-800">₹{{ number_format($user->hourly_rate, 0) }}/h</span>
                                        </div>

                                        <!-- Profile Image -->
                                        <div class="flex justify-center mb-3">
                                            <div class="w-28 h-28 rounded-full overflow-hidden border-4 border-gray-200 shadow-lg">
                                                @if($user->profile_picture)
                                                    <img src="{{ asset('storage/' . $user->profile_picture) }}"
                                                         alt="{{ $user->name }}"
                                                         class="w-full h-full object-cover"
                                                         loading="lazy"
                                                         onerror="this.src='{{ asset('images/default-avatar.png') }}';">
                                                @elseif($user->galleryImages->count() > 0)
                                                    <img src="{{ $user->galleryImages->first()->image_url }}"
                                                         alt="{{ $user->name }}"
                                                         class="w-full h-full object-cover"
                                                         loading="lazy"
                                                         onerror="this.src='{{ asset('images/default-avatar.png') }}';">
                                                @else
                                                    <img src="{{ asset('images/default-avatar.png') }}"
                                                         alt="{{ $user->name }}"
                                                         class="w-full h-full object-cover">
                                                @endif
                                            </div>
                                        </div>

                                        <!-- User Info -->
                                        <div class="text-center">
                                            <!-- Full Name -->
                                            <h3 class="text-base font-bold text-gray-900 mb-2 truncate">
                                                {{ $user->name }}
                                            </h3>

                                            <!-- Attractive Hobby Display -->
                                            <div class="mb-3">
                                                @if($user->interests)
                                                    <div class="flex flex-wrap justify-center gap-1">
                                                        @foreach(array_slice(explode(',', $user->interests), 0, 2) as $interest)
                                                            <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 text-gray-700 border border-gray-200">
                                                                {{ trim($interest) }}
                                                            </span>
                                                        @endforeach
                                                        @if(count(explode(',', $user->interests)) > 2)
                                                            <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600 border border-gray-200">
                                                                +{{ count(explode(',', $user->interests)) - 2 }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                @else
                                                    <div class="text-xs text-gray-500 italic">
                                                        ✨ Exploring new interests
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- View Detail Button -->
                                            <a href="{{ route('find-person.show', $user) }}" class="block w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:text-white font-bold py-2 px-4 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-500/30 hover:brightness-110 text-center text-sm">
                                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                View Profile
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $users->links() }}
                </div>
            @else
                <!-- No Results -->
                <div class="text-center py-12">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-100 to-blush-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
                    @if($gender !== 'all' || $minPrice || $maxPrice || $location || $availabilityDate || $availabilityTime)
                        <p class="text-sm text-gray-600 mb-4">
                            @if($availabilityDate && $availabilityTime)
                                No members are available on {{ date('M d, Y', strtotime($availabilityDate)) }} at {{ date('g:i A', strtotime($availabilityTime)) }}. Try selecting a different date/time or adjusting other filters.
                            @else
                                No members match your current filters. Try adjusting your search criteria.
                            @endif
                        </p>
                        <a href="{{ route('find-person.index') }}" class="inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                            Clear all filters
                        </a>
                    @else
                        <p class="text-sm text-gray-600 mb-4">
                            No members are currently offering time spending services. Check back later!
                        </p>
                        @if($totalCount > 0)
                            <p class="text-xs text-gray-500">
                                {{ $totalCount }} members have time spending enabled but may not meet visibility requirements.
                            </p>
                        @endif
                    @endif
                </div>
            @endif
        </div>
    </section>

    <!-- JavaScript for Enhanced Functionality -->
    <script>
        // Update dual range slider
        function updateDualRange() {
            const minSlider = document.getElementById('min_price');
            const maxSlider = document.getElementById('max_price');
            const minDisplay = document.getElementById('min-display');
            const maxDisplay = document.getElementById('max-display');
            const activeTrack = document.getElementById('active-track');

            let minVal = parseInt(minSlider.value);
            let maxVal = parseInt(maxSlider.value);

            // Ensure min is not greater than max
            if (minVal > maxVal) {
                minSlider.value = maxVal;
                minVal = maxVal;
            }

            // Update displays
            minDisplay.textContent = '₹' + minVal;
            maxDisplay.textContent = '₹' + maxVal;

            // Update active track position and width
            const minPercent = (minVal / minSlider.max) * 100;
            const maxPercent = (maxVal / maxSlider.max) * 100;

            activeTrack.style.left = minPercent + '%';
            activeTrack.style.width = (maxPercent - minPercent) + '%';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateDualRange();

            // Set default time to current time if no time is selected
            const timeInput = document.getElementById('availability_time');
            if (timeInput && !timeInput.value) {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                timeInput.value = `${hours}:${minutes}`;
            }

            // Add validation for date and time combination
            const dateInput = document.getElementById('availability_date');
            const form = document.querySelector('form');

            if (form && dateInput && timeInput) {
                form.addEventListener('submit', function(e) {
                    const hasDate = dateInput.value;
                    const hasTime = timeInput.value;

                    // If one is filled but not the other, show warning
                    if ((hasDate && !hasTime) || (!hasDate && hasTime)) {
                        e.preventDefault();
                        alert('Please select both date and time for availability filtering, or leave both empty to see all users.');
                        return false;
                    }
                });
            }
        });
    </script>

    <!-- Simple CSS for dual range slider -->
    <style>
        .range-slider {
            pointer-events: none;
        }

        .range-slider::-webkit-slider-thumb {
            appearance: none;
            height: 18px;
            width: 18px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            pointer-events: all;
            position: relative;
            z-index: 10;
        }

        .range-slider::-moz-range-thumb {
            height: 18px;
            width: 18px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            pointer-events: all;
            position: relative;
            z-index: 10;
        }

        .range-slider::-webkit-slider-track {
            height: 8px;
            background: transparent;
            border-radius: 4px;
        }

        .range-slider::-moz-range-track {
            height: 8px;
            background: transparent;
            border-radius: 4px;
            border: none;
        }

        .range-slider::-webkit-slider-thumb:hover {
            background: #2563eb;
        }

        .range-slider::-moz-range-thumb:hover {
            background: #2563eb;
        }
    </style>
</x-app-layout>
