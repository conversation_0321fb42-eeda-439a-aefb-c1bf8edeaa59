<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dispute History') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Your Dispute History</h3>
                        <p class="text-sm text-gray-600">
                            View all disputes you've raised or that have been raised against you.
                        </p>
                    </div>

                    @if ($disputes->count() > 0)
                        <div class="space-y-4">
                            @foreach ($disputes as $dispute)
                                <div class="border rounded-lg p-4 {{ $dispute->dispute_status === 'resolved' ? 'bg-green-50 border-green-200' : ($dispute->dispute_status === 'rejected' ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200') }}">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <h4 class="font-medium text-gray-900">Booking #{{ $dispute->id }}</h4>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                                    {{ $dispute->dispute_type === 'no_show' ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800' }}">
                                                    {{ $dispute->dispute_type_display }}
                                                </span>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                                    {{ $dispute->dispute_status === 'resolved' ? 'bg-green-100 text-green-800' : 
                                                       ($dispute->dispute_status === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                                    {{ ucfirst($dispute->dispute_status) }}
                                                </span>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                                <div>
                                                    <p><strong>Meeting Date:</strong> {{ $dispute->booking_date->format('M d, Y h:i A') }}</p>
                                                    <p><strong>Duration:</strong> {{ $dispute->duration_hours }} hour{{ $dispute->duration_hours > 1 ? 's' : '' }}</p>
                                                    <p><strong>Amount:</strong> ₹{{ number_format($dispute->total_amount, 2) }}</p>
                                                </div>
                                                <div>
                                                    <p><strong>Client:</strong> {{ $dispute->client->name }}</p>
                                                    <p><strong>Provider:</strong> {{ $dispute->provider->name }}</p>
                                                    <p><strong>Disputed By:</strong> 
                                                        @if($dispute->disputed_by === $dispute->client_id)
                                                            {{ $dispute->client->name }} (Client)
                                                        @else
                                                            {{ $dispute->provider->name }} (Provider)
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="mt-3">
                                                <p class="text-sm text-gray-700">
                                                    <strong>Reason:</strong> {{ $dispute->dispute_reason }}
                                                </p>
                                            </div>

                                            @if($dispute->admin_notes)
                                                <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                                                    <p class="text-sm text-blue-800">
                                                        <strong>Admin Notes:</strong> {{ $dispute->admin_notes }}
                                                    </p>
                                                </div>
                                            @endif

                                            <div class="mt-3 flex items-center justify-between text-xs text-gray-500">
                                                <span>Disputed: {{ $dispute->disputed_at->format('M d, Y h:i A') }}</span>
                                                @if($dispute->resolved_at)
                                                    <span>Resolved: {{ $dispute->resolved_at->format('M d, Y h:i A') }}</span>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="ml-4">
                                            <button onclick="viewDisputeDetailsModal({{ $dispute->id }})"
                                                    class="px-3 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 border border-blue-300 hover:border-blue-400 rounded-md transition-colors">
                                                View Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-6">
                            {{ $disputes->links() }}
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No disputes found</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                You haven't raised any disputes or had any disputes raised against you.
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Dispute Details Modal -->
    <div id="disputeDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Dispute Details</h3>
                    <button onclick="closeDisputeDetailsModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="disputeDetailsContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        async function viewDisputeDetailsModal(bookingId) {
            try {
                const response = await fetch(`/dispute/details/${bookingId}`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const dispute = data.dispute;
                    const content = document.getElementById('disputeDetailsContent');
                    
                    let evidenceHtml = '';
                    if (dispute.evidence && dispute.evidence.length > 0) {
                        evidenceHtml = `
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">Evidence Photos:</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    ${dispute.evidence.map(item => `
                                        <img src="/storage/${item.path}" class="w-full h-32 object-cover rounded border cursor-pointer" 
                                             onclick="window.open('/storage/${item.path}', '_blank')">
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }
                    
                    content.innerHTML = `
                        <div class="space-y-4">
                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <div>
                                        <div class="font-medium text-orange-800">${dispute.dispute_type_display}</div>
                                        <div class="text-sm text-orange-700">${dispute.dispute_status_display}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Reported Issue:</h4>
                                <p class="text-gray-700 bg-gray-50 p-3 rounded-lg">${dispute.dispute_reason}</p>
                            </div>
                            
                            ${evidenceHtml}
                            
                            <div class="text-sm text-gray-500">
                                <div>Reported on: ${new Date(dispute.disputed_at).toLocaleDateString()}</div>
                                ${dispute.resolved_at ? `<div>Resolved on: ${new Date(dispute.resolved_at).toLocaleDateString()}</div>` : ''}
                            </div>
                            
                            ${dispute.admin_notes ? `
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Admin Notes:</h4>
                                    <p class="text-gray-700 bg-blue-50 p-3 rounded-lg">${dispute.admin_notes}</p>
                                </div>
                            ` : ''}
                        </div>
                    `;
                    
                    document.getElementById('disputeDetailsModal').classList.remove('hidden');
                } else {
                    alert('Failed to load dispute details: ' + (data.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error loading dispute details:', error);
                alert('An error occurred while loading dispute details.');
            }
        }

        function closeDisputeDetailsModal() {
            document.getElementById('disputeDetailsModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('disputeDetailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDisputeDetailsModal();
            }
        });
    </script>

    <!-- Toast Notifications -->
    <x-toast />
</x-app-layout>
