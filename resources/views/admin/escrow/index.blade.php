@extends('layouts.admin')

@section('page-title', 'Escrow Management')

@section('page-description', 'Manage escrow payments, disputes, and releases for time spending bookings.')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Escrow Payment Management</h3>
                </div>
                <div class="card-body">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>Held Payments</h5>
                                    <h3>{{ $heldBookings->total() }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5>Disputed Payments</h5>
                                    <h3>{{ $disputedBookings->total() }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>Recent Releases</h5>
                                    <h3>{{ $recentReleases->count() }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="escrowTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active text-dark" id="held-tab" data-bs-toggle="tab" data-bs-target="#held" type="button" role="tab">
                                Held Payments ({{ $heldBookings->total() }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link text-dark" id="disputed-tab" data-bs-toggle="tab" data-bs-target="#disputed" type="button" role="tab">
                                Disputed Payments ({{ $disputedBookings->total() }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link text-dark" id="recent-tab" data-bs-toggle="tab" data-bs-target="#recent" type="button" role="tab">
                                Recent Releases
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="escrowTabContent">
                        <!-- Held Payments Tab -->
                        <div class="tab-pane fade show active" id="held" role="tabpanel">
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Booking ID</th>
                                            <th>Client</th>
                                            <th>Provider</th>
                                            <th>Amount</th>
                                            <th>Held Since</th>
                                            <th>Auto Release</th>
                                            <th>Meeting Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($heldBookings as $booking)
                                        <tr>
                                            <td>#{{ $booking->id }}</td>
                                            <td>{{ $booking->client->name }}</td>
                                            <td>{{ $booking->provider->name }}</td>
                                            <td>₹{{ number_format($booking->total_amount, 2) }}</td>
                                            <td>{{ $booking->escrow_held_at ? $booking->escrow_held_at->format('M d, Y H:i') : 'N/A' }}</td>
                                            <td>
                                                @if($booking->auto_release_at)
                                                    {{ $booking->auto_release_at->format('M d, Y H:i') }}
                                                    @if($booking->auto_release_at->isPast())
                                                        <span class="badge bg-danger">Overdue</span>
                                                    @endif
                                                @else
                                                    <span class="text-muted">Not set</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($booking->meetingVerification)
                                                    @if($booking->meetingVerification->hasBothEndPhotos())
                                                        <span class="badge bg-success">Completed</span>
                                                    @elseif($booking->meetingVerification->hasBothStartPhotos())
                                                        <span class="badge bg-warning">In Progress</span>
                                                    @else
                                                        <span class="badge bg-secondary">Not Started</span>
                                                    @endif
                                                @else
                                                    <span class="badge bg-light">No Verification</span>
                                                @endif
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-success" onclick="releasePayment({{ $booking->id }})">
                                                    Release
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="showRefundModal({{ $booking->id }})">
                                                    Refund
                                                </button>
                                                <button class="btn btn-sm btn-info" onclick="viewDetails({{ $booking->id }})">
                                                    Details
                                                </button>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="8" class="text-center">No held payments found</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                {{ $heldBookings->links() }}
                            </div>
                        </div>

                        <!-- Disputed Payments Tab -->
                        <div class="tab-pane fade" id="disputed" role="tabpanel">
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Booking ID</th>
                                            <th>Client</th>
                                            <th>Provider</th>
                                            <th>Amount</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Disputed By</th>
                                            <th>Disputed At</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($disputedBookings as $booking)
                                        <tr>
                                            <td>#{{ $booking->id }}</td>
                                            <td>{{ $booking->client->name }}</td>
                                            <td>{{ $booking->provider->name }}</td>
                                            <td>₹{{ number_format($booking->total_amount, 2) }}</td>
                                            <td>
                                                <span class="badge bg-{{ $booking->dispute_type === 'no_show' ? 'warning' : 'info' }}">
                                                    {{ $booking->dispute_type_display ?? 'N/A' }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $booking->dispute_status === 'pending' ? 'warning' : ($booking->dispute_status === 'resolved' ? 'success' : 'secondary') }}">
                                                    {{ ucfirst($booking->dispute_status ?? 'pending') }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($booking->disputed_by == $booking->client_id)
                                                    {{ $booking->client->name }} (Client)
                                                @else
                                                    {{ $booking->provider->name }} (Provider)
                                                @endif
                                            </td>
                                            <td>{{ $booking->disputed_at ? $booking->disputed_at->format('M d, Y H:i') : 'N/A' }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-success" onclick="resolveForProvider({{ $booking->id }})" title="Resolve for Provider">
                                                        <i class="fas fa-check"></i> Provider
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="resolveForClient({{ $booking->id }})" title="Resolve for Client">
                                                        <i class="fas fa-undo"></i> Client
                                                    </button>
                                                    <button class="btn btn-sm btn-info" onclick="viewDisputeDetails({{ $booking->id }})" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="9" class="text-center">No disputed payments found</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                {{ $disputedBookings->links() }}
                            </div>
                        </div>

                        <!-- Recent Releases Tab -->
                        <div class="tab-pane fade" id="recent" role="tabpanel">
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Booking ID</th>
                                            <th>Client</th>
                                            <th>Provider</th>
                                            <th>Amount</th>
                                            <th>Released At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($recentReleases as $booking)
                                        <tr>
                                            <td>#{{ $booking->id }}</td>
                                            <td>{{ $booking->client->name }}</td>
                                            <td>{{ $booking->provider->name }}</td>
                                            <td>₹{{ number_format($booking->total_amount, 2) }}</td>
                                            <td>{{ $booking->escrow_released_at ? $booking->escrow_released_at->format('M d, Y H:i') : 'N/A' }}</td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center">No recent releases found</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Refund Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="refundForm">
                    <input type="hidden" id="refundBookingId">
                    <div class="mb-3">
                        <label for="refundReason" class="form-label">Refund Reason</label>
                        <textarea class="form-control" id="refundReason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="processRefund()">Process Refund</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Ensure tab text is always black for better readability */
.nav-tabs .nav-link {
    color: #000 !important;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    color: #000 !important;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #000 !important;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

/* Improve table readability */
.table th {
    color: #000;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td {
    color: #000;
}

/* Improve card text readability */
.card-body {
    color: #000;
}

.card-title {
    color: #000;
    font-weight: 600;
}
</style>

<script>
function releasePayment(bookingId) {
    if (confirm('Are you sure you want to release this payment to the provider?')) {
        fetch(`/admin/escrow/release/${bookingId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Payment released successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function showRefundModal(bookingId) {
    document.getElementById('refundBookingId').value = bookingId;
    document.getElementById('refundReason').value = '';
    new bootstrap.Modal(document.getElementById('refundModal')).show();
}

function processRefund() {
    const bookingId = document.getElementById('refundBookingId').value;
    const reason = document.getElementById('refundReason').value;
    
    if (!reason.trim()) {
        alert('Please provide a refund reason');
        return;
    }
    
    fetch(`/admin/escrow/refund/${bookingId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Refund processed successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}

function viewDetails(bookingId) {
    fetch(`/admin/escrow/booking/${bookingId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show booking details in a modal or redirect to details page
            console.log('Booking details:', data.booking);
            // You can implement a details modal here
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}

function resolveForProvider(bookingId) {
    const reason = prompt('Enter reason for resolving in favor of provider:');
    if (!reason) return;

    if (confirm('Are you sure you want to resolve this dispute in favor of the provider? Payment will be released.')) {
        fetch(`/admin/escrow/dispute/${bookingId}/resolve-provider`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Dispute resolved in favor of provider!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function resolveForClient(bookingId) {
    const reason = prompt('Enter reason for resolving in favor of client:');
    if (!reason) return;

    if (confirm('Are you sure you want to resolve this dispute in favor of the client? Payment will be refunded.')) {
        fetch(`/admin/escrow/dispute/${bookingId}/resolve-client`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Dispute resolved in favor of client!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function viewDisputeDetails(bookingId) {
    fetch(`/admin/escrow/booking/${bookingId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const booking = data.booking;
            let evidenceHtml = '';

            if (booking.dispute_evidence && booking.dispute_evidence.length > 0) {
                evidenceHtml = `
                    <h6>Evidence Photos:</h6>
                    <div class="row">
                        ${booking.dispute_evidence.map(item => `
                            <div class="col-md-4 mb-2">
                                <img src="/storage/${item.path}" class="img-fluid rounded" style="cursor: pointer;" onclick="window.open('/storage/${item.path}', '_blank')">
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            const modalContent = `
                <div class="modal fade" id="disputeDetailsModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Dispute Details - Booking #${booking.id}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Booking Information:</h6>
                                        <p><strong>Client:</strong> ${booking.client.name}</p>
                                        <p><strong>Provider:</strong> ${booking.provider.name}</p>
                                        <p><strong>Amount:</strong> ₹${booking.total_amount}</p>
                                        <p><strong>Date:</strong> ${new Date(booking.booking_date).toLocaleDateString()}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Dispute Information:</h6>
                                        <p><strong>Type:</strong> ${booking.dispute_type_display || 'N/A'}</p>
                                        <p><strong>Status:</strong> ${booking.dispute_status || 'pending'}</p>
                                        <p><strong>Disputed By:</strong> ${booking.disputed_by === booking.client_id ? booking.client.name + ' (Client)' : booking.provider.name + ' (Provider)'}</p>
                                        <p><strong>Disputed At:</strong> ${new Date(booking.disputed_at).toLocaleString()}</p>
                                    </div>
                                </div>
                                <hr>
                                <h6>Dispute Reason:</h6>
                                <p class="bg-light p-3 rounded">${booking.dispute_reason || 'No reason provided'}</p>
                                ${evidenceHtml}
                                ${booking.admin_notes ? `
                                    <hr>
                                    <h6>Admin Notes:</h6>
                                    <p class="bg-info bg-opacity-10 p-3 rounded">${booking.admin_notes}</p>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-success" onclick="resolveForProvider(${booking.id}); bootstrap.Modal.getInstance(document.getElementById('disputeDetailsModal')).hide();">Resolve for Provider</button>
                                <button type="button" class="btn btn-danger" onclick="resolveForClient(${booking.id}); bootstrap.Modal.getInstance(document.getElementById('disputeDetailsModal')).hide();">Resolve for Client</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('disputeDetailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalContent);

            // Show modal
            new bootstrap.Modal(document.getElementById('disputeDetailsModal')).show();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}
</script>
@endsection
