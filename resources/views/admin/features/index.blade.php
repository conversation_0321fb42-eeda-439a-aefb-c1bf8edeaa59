@extends('layouts.admin')

@section('page-title', 'Features Management')
@section('page-description', 'Enable or disable application features')

@section('breadcrumbs')
    <li class="breadcrumb-item active">Features</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-toggles2 me-2"></i>Application Features
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshFeatures()">
                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Feature</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($features as $feature)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="feature-icon me-3">
                                                @if($feature->name === 'subscription_model')
                                                    <i class="bi bi-credit-card-fill text-success"></i>
                                                @elseif($feature->name === 'sugar_partner')
                                                    <i class="bi bi-heart-fill text-danger"></i>
                                                @elseif($feature->name === 'partner_swapping')
                                                    <i class="bi bi-heart-fill text-purple"></i>
                                                @elseif($feature->name === 'time_spending')
                                                    <i class="bi bi-clock-fill text-success"></i>
                                                @elseif($feature->name === 'gallery')
                                                    <i class="bi bi-images text-info"></i>
                                                @elseif($feature->name === 'meeting_events')
                                                    <i class="bi bi-calendar-event text-purple"></i>
                                                @elseif($feature->name === 'chat_system')
                                                    <i class="bi bi-chat-dots-fill text-primary"></i>
                                                @elseif($feature->name === 'notifications')
                                                    <i class="bi bi-bell-fill text-warning"></i>
                                                @elseif($feature->name === 'user_verification')
                                                    <i class="bi bi-patch-check-fill text-success"></i>
                                                @elseif($feature->name === 'premium_membership')
                                                    <i class="bi bi-star-fill text-warning"></i>
                                                @elseif($feature->name === 'location_services')
                                                    <i class="bi bi-geo-alt-fill text-danger"></i>
                                                @elseif($feature->name === 'privacy_controls')
                                                    <i class="bi bi-shield-fill-check text-success"></i>
                                                @elseif($feature->name === 'meeting_verification')
                                                    <i class="bi bi-camera-fill text-info"></i>
                                                @elseif($feature->name === 'rating_review_system')
                                                    <i class="bi bi-star-fill text-warning"></i>
                                                @else
                                                    <i class="bi bi-gear-fill text-primary"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $feature->label }}</h6>
                                                <small class="text-muted">{{ $feature->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="mb-0">{{ $feature->description }}</p>
                                        @if($feature->options)
                                            <small class="text-muted">
                                                @if($feature->name === 'sugar_partner' && isset($feature->options['pricing']))
                                                    Pricing: Sugar Daddy/Mommy: {{ $feature->options['currency'] ?? 'INR' }} {{ number_format($feature->options['pricing']['sugar_daddy'] ?? 200, 2) }},
                                                    Sugar Babe/Boy: {{ $feature->options['currency'] ?? 'INR' }} {{ number_format($feature->options['pricing']['sugar_companion_female'] ?? 100, 2) }}
                                                @else
                                                    Options: {{ implode(', ', array_filter(array_values($feature->options), function($value) { return !is_array($value); })) }}
                                                @endif
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $feature->is_enabled ? 'bg-success' : 'bg-secondary' }}">
                                            {{ $feature->is_enabled ? 'Enabled' : 'Disabled' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2 align-items-center">
                                            @if($feature->is_enabled)
                                                <form method="POST" action="{{ route('admin.features.update-status', $feature) }}" style="display: inline;">
                                                    @csrf
                                                    <input type="hidden" name="is_enabled" value="0">
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        <i class="bi bi-toggle-on me-1"></i>Enabled
                                                    </button>
                                                </form>
                                            @else
                                                <form method="POST" action="{{ route('admin.features.update-status', $feature) }}" style="display: inline;">
                                                    @csrf
                                                    <input type="hidden" name="is_enabled" value="1">
                                                    <button type="submit" class="btn btn-secondary btn-sm">
                                                        <i class="bi bi-toggle-off me-1"></i>Disabled
                                                    </button>
                                                </form>
                                            @endif

                                            @if($feature->name === 'sugar_partner')
                                                <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#sugarPartnerPricingModal">
                                                    <i class="bi bi-currency-rupee me-1"></i>Pricing
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-toggles2 display-4 d-block mb-3"></i>
                                            <h5>No Features Found</h5>
                                            <p>No application features are configured yet.</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<!-- Sugar Partner Pricing Modal -->
<div class="modal fade" id="sugarPartnerPricingModal" tabindex="-1" aria-labelledby="sugarPartnerPricingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sugarPartnerPricingModalLabel">
                    <i class="bi bi-heart-fill text-danger me-2"></i>Sugar Partner Pricing Settings
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('admin.features.update-sugar-partner-pricing', $features->where('name', 'sugar_partner')->first()) }}">
                @csrf
                <div class="modal-body">
                    @php
                        $sugarPartnerFeature = $features->where('name', 'sugar_partner')->first();
                        $currentPricing = $sugarPartnerFeature?->options['pricing'] ?? [
                            'sugar_daddy' => 200.00,
                            'sugar_mommy' => 200.00,
                            'sugar_companion_female' => 100.00,
                            'sugar_companion_male' => 100.00
                        ];
                        $currentCurrency = $sugarPartnerFeature?->options['currency'] ?? 'INR';
                    @endphp

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Profile Exchange Pricing:</strong> Set different prices for each Sugar Partner type. Both users in an exchange will pay their respective type's price.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sugar_daddy_price" class="form-label">
                                    <i class="bi bi-person-suit me-1"></i>Sugar Daddy Price
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number"
                                           class="form-control"
                                           id="sugar_daddy_price"
                                           name="sugar_daddy_price"
                                           value="{{ $currentPricing['sugar_daddy'] }}"
                                           min="0"
                                           max="99999.99"
                                           step="0.01"
                                           required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sugar_mommy_price" class="form-label">
                                    <i class="bi bi-person-dress me-1"></i>Sugar Mommy Price
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number"
                                           class="form-control"
                                           id="sugar_mommy_price"
                                           name="sugar_mommy_price"
                                           value="{{ $currentPricing['sugar_mommy'] }}"
                                           min="0"
                                           max="99999.99"
                                           step="0.01"
                                           required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sugar_companion_female_price" class="form-label">
                                    <i class="bi bi-person-heart me-1"></i>Sugar Babe Price
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number"
                                           class="form-control"
                                           id="sugar_companion_female_price"
                                           name="sugar_companion_female_price"
                                           value="{{ $currentPricing['sugar_companion_female'] }}"
                                           min="0"
                                           max="99999.99"
                                           step="0.01"
                                           required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sugar_companion_male_price" class="form-label">
                                    <i class="bi bi-person-plus me-1"></i>Sugar Boy Price
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number"
                                           class="form-control"
                                           id="sugar_companion_male_price"
                                           name="sugar_companion_male_price"
                                           value="{{ $currentPricing['sugar_companion_male'] }}"
                                           min="0"
                                           max="99999.99"
                                           step="0.01"
                                           required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="currency" class="form-label">Currency</label>
                        <select class="form-select" id="currency" name="currency" required>
                            <option value="INR" {{ $currentCurrency === 'INR' ? 'selected' : '' }}>INR (Indian Rupee)</option>
                            <option value="USD" {{ $currentCurrency === 'USD' ? 'selected' : '' }}>USD (US Dollar)</option>
                            <option value="EUR" {{ $currentCurrency === 'EUR' ? 'selected' : '' }}>EUR (Euro)</option>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="bi bi-currency-rupee me-2"></i>Current Pricing Structure
                        </h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <strong>Sugar Daddy:</strong> {{ $currentCurrency }} {{ number_format($currentPricing['sugar_daddy'], 2) }}<br>
                                <strong>Sugar Mommy:</strong> {{ $currentCurrency }} {{ number_format($currentPricing['sugar_mommy'], 2) }}
                            </div>
                            <div class="col-6">
                                <strong>Sugar Babe:</strong> {{ $currentCurrency }} {{ number_format($currentPricing['sugar_companion_female'], 2) }}<br>
                                <strong>Sugar Boy:</strong> {{ $currentCurrency }} {{ number_format($currentPricing['sugar_companion_male'], 2) }}
                            </div>
                        </div>
                        <hr>
                        <p class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Each user pays according to their Sugar Partner type. Both must complete payment before viewing profiles.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-1"></i>Save Pricing
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function refreshFeatures() {
    window.location.reload();
}
</script>
@endpush
