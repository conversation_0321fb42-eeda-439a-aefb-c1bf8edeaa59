@extends('layouts.admin')

@section('page-title', 'Revenue Management')

@section('page-description', 'Monitor platform revenue, commission earnings, and financial statistics.')

@section('content')
<div class="container-fluid">
    <!-- Revenue Statistics Cards -->
    <div class="row g-4 mb-4">
        <!-- Total Platform Revenue -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="bi bi-currency-rupee text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Total Revenue</p>
                                    <h3 class="mb-0 fw-bold">₹{{ number_format($totalStats['total_revenue'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Platform Fees -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                                <i class="bi bi-cash-stack text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Platform Fees</p>
                                    <h3 class="mb-0 fw-bold">₹{{ number_format($totalStats['total_platform_fees'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commission Earnings -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                                <i class="bi bi-percent text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Commission</p>
                                    <h3 class="mb-0 fw-bold">₹{{ number_format($totalStats['total_commission'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Revenue -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="bi bi-credit-card text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Subscription Revenue</p>
                                    <h3 class="mb-0 fw-bold">₹{{ number_format($totalStats['total_subscription_revenue'] ?? 0, 2) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Bookings -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-3 d-flex align-items-center justify-content-center"
                                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
                                <i class="bi bi-calendar-check text-white fs-5"></i>
                            </div>
                        </div>
                        <div class="ms-3 flex-fill">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <p class="text-muted mb-1 fw-medium" style="font-size: 0.875rem;">Paid Bookings</p>
                                    <h3 class="mb-0 fw-bold">{{ number_format($totalStats['total_bookings']) }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Revenue Records -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0 fw-bold">Recent Revenue Records</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="exportRevenue()">
                                <i class="bi bi-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Date</th>
                                    <th class="border-0 fw-semibold">Type</th>
                                    <th class="border-0 fw-semibold">Reference</th>
                                    <th class="border-0 fw-semibold">User</th>
                                    <th class="border-0 fw-semibold">Platform Fee</th>
                                    <th class="border-0 fw-semibold">Commission</th>
                                    <th class="border-0 fw-semibold">Total Earnings</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentRevenues as $revenue)
                                <tr>
                                    <td class="py-3">
                                        <span class="text-muted">{{ $revenue->created_at->format('M d, Y H:i') }}</span>
                                    </td>
                                    <td class="py-3">
                                        @if($revenue->revenue_type === 'booking_payment')
                                            <span class="badge bg-primary">Booking</span>
                                        @elseif($revenue->revenue_type === 'subscription_payment')
                                            <span class="badge bg-success">Subscription</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($revenue->revenue_type) }}</span>
                                        @endif
                                    </td>
                                    <td class="py-3">
                                        @if($revenue->revenue_type === 'booking_payment' && $revenue->booking)
                                            <span class="badge bg-primary">#{{ $revenue->booking_id }}</span>
                                        @elseif($revenue->revenue_type === 'subscription_payment' && $revenue->subscription)
                                            <span class="badge bg-success">{{ $revenue->subscription->subscriptionPlan->name ?? 'Subscription' }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="py-3">
                                        @if($revenue->revenue_type === 'booking_payment' && $revenue->booking)
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-2">
                                                    @if($revenue->booking->client->profile_picture)
                                                        <img src="{{ asset('storage/' . $revenue->booking->client->profile_picture) }}"
                                                             alt="{{ $revenue->booking->client->name }}"
                                                             class="rounded-circle"
                                                             style="width: 32px; height: 32px; object-fit: cover;">
                                                    @else
                                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                                             style="width: 32px; height: 32px;">
                                                            <i class="bi bi-person text-white"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div>
                                                    <div class="fw-medium">{{ $revenue->booking->client->name }}</div>
                                                    <small class="text-muted">Client</small>
                                                </div>
                                            </div>
                                        @elseif($revenue->revenue_type === 'subscription_payment' && $revenue->subscription)
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0 me-2">
                                                    @if($revenue->subscription->user->profile_picture)
                                                        <img src="{{ asset('storage/' . $revenue->subscription->user->profile_picture) }}"
                                                             alt="{{ $revenue->subscription->user->name }}"
                                                             class="rounded-circle"
                                                             style="width: 32px; height: 32px; object-fit: cover;">
                                                    @else
                                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                                             style="width: 32px; height: 32px;">
                                                            <i class="bi bi-person text-white"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div>
                                                    <div class="fw-medium">{{ $revenue->subscription->user->name }}</div>
                                                    <small class="text-muted">Subscriber</small>
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="py-3">
                                        <span class="text-success fw-medium">₹{{ number_format($revenue->platform_fee, 2) }}</span>
                                    </td>
                                    <td class="py-3">
                                        <span class="text-info fw-medium">₹{{ number_format($revenue->commission_amount, 2) }}</span>
                                    </td>
                                    <td class="py-3">
                                        <span class="text-primary fw-bold">₹{{ number_format($revenue->total_admin_earnings, 2) }}</span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4 text-muted">
                                        <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                        No revenue records found
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Ensure text is black for better readability */
.table th {
    color: #000 !important;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td {
    color: #000 !important;
}

.card-body {
    color: #000 !important;
}

.card-title, .card-header h5 {
    color: #000 !important;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
}

.fw-bold, .fw-medium {
    color: #000 !important;
}
</style>

<script>
function exportRevenue() {
    window.location.href = '{{ route("admin.revenue.export") }}';
}
</script>
@endsection
