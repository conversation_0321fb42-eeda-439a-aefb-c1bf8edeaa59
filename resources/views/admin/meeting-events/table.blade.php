@if($events->count() > 0)
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th scope="col" style="width: 60px;">Sr. No</th>
                    <th scope="col">Event</th>
                    <th scope="col">Payment Amounts</th>
                    <th scope="col">Status</th>
                    <th scope="col">Event Date</th>
                    <th scope="col" style="width: 150px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($events as $index => $event)
                    <tr>
                        <td class="fw-medium">{{ $events->firstItem() + $index }}</td>
                        <td>
                            <div>
                                <div class="fw-medium">
                                    {{ $event->title }}
                                    @if($event->is_couple_event)
                                        <span class="badge bg-pink text-white ms-2" title="Couple Event">
                                            <i class="bi bi-heart"></i> Couple
                                        </span>
                                    @endif
                                </div>
                                <small class="text-muted">{{ Str::limit(strip_tags($event->description), 50) }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                @if($event->is_couple_event && \App\Models\Feature::isEnabled('partner_swapping'))
                                    <!-- Show only couple payment for couple events when feature is enabled -->
                                    <div><small class="text-muted">Couple:</small> <span class="fw-medium">₹{{ number_format($event->payment_amount_couple, 2) }}</span></div>
                                @else
                                    <!-- Show individual payments for regular events or when couple feature is disabled -->
                                    <div><small class="text-muted">Boys:</small> <span class="fw-medium">₹{{ number_format($event->payment_amount_boys, 2) }}</span></div>
                                    <div><small class="text-muted">Girls:</small> <span class="fw-medium">₹{{ number_format($event->payment_amount_girls, 2) }}</span></div>
                                @endif
                            </div>
                        </td>
                        <td>
                            <button type="button" 
                                    class="btn btn-sm {{ $event->is_event_enabled ? 'btn-success' : 'btn-danger' }} rounded-pill btn-toggle-event-status"
                                    data-event-id="{{ $event->id }}">
                                <i class="bi {{ $event->is_event_enabled ? 'bi-check-circle' : 'bi-x-circle' }} me-1"></i>
                                {{ $event->is_event_enabled ? 'Enabled' : 'Disabled' }}
                            </button>
                        </td>
                        <td>
                            <div>{{ $event->event_date ? $event->event_date->format('M d, Y') : 'Not set' }}</div>
                            @if($event->event_date)
                                <small class="text-muted">{{ $event->event_date->format('H:i') }}</small>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex gap-2">
                                <a href="{{ route('admin.meeting-events.edit', $event) }}"
                                   class="btn btn-outline-primary btn-sm" title="Edit Event" data-bs-toggle="tooltip">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-outline-danger btn-sm btn-delete-event"
                                        data-event-id="{{ $event->id }}"
                                        title="Delete Event" data-bs-toggle="tooltip">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="card-footer bg-white border-top">
        <div class="d-flex justify-content-center">
            @if($events->hasPages())
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        @if($events->onFirstPage())
                            <li class="page-item disabled"><span class="page-link">Previous</span></li>
                        @else
                            <li class="page-item"><button class="page-link" onclick="loadEventsPage({{ $events->currentPage() - 1 }})">Previous</button></li>
                        @endif

                        @foreach($events->getUrlRange(1, $events->lastPage()) as $page => $url)
                            @if($page == $events->currentPage())
                                <li class="page-item active"><span class="page-link">{{ $page }}</span></li>
                            @else
                                <li class="page-item"><button class="page-link" onclick="loadEventsPage({{ $page }})">{{ $page }}</button></li>
                            @endif
                        @endforeach

                        @if($events->hasMorePages())
                            <li class="page-item"><button class="page-link" onclick="loadEventsPage({{ $events->currentPage() + 1 }})">Next</button></li>
                        @else
                            <li class="page-item disabled"><span class="page-link">Next</span></li>
                        @endif
                    </ul>
                </nav>
            @endif
        </div>
    </div>
@else
    <!-- Empty State -->
    <div class="card-body text-center py-5">
        <i class="bi bi-calendar-x display-1 text-muted mb-3"></i>
        <h5 class="fw-bold">No meeting events</h5>
        <p class="text-muted mb-4">Get started by creating a new meeting event.</p>
        <a href="{{ route('admin.meeting-events.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Create Meeting Event
        </a>
    </div>
@endif

<script>
function loadEventsPage(page) {
    const currentFilters = {
        search: document.querySelector('input[name="search"]')?.value || '',
        status: document.querySelector('select[name="status"]')?.value || '',
        sort_by: document.querySelector('input[name="sort_by"]')?.value || 'created_at',
        sort_order: document.querySelector('input[name="sort_order"]')?.value || 'desc',
        page: page
    };
    
    if (window.adminPanel) {
        window.adminPanel.refreshTableData('events-table', currentFilters);
    }
}
</script>
