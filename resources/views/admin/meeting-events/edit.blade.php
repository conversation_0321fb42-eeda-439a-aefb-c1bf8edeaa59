@extends('layouts.admin')

@section('page-title', 'Edit Meeting Event')

@section('page-description', 'Update meeting event details, payment amounts, and rules.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <a href="{{ route('admin.meeting-events.index') }}" class="text-decoration-none">Meeting Events</a>
    </li>
    <li class="breadcrumb-item">
        <span class="text-muted">Edit</span>
    </li>
@endsection

@section('header-actions')
    <a href="{{ route('admin.meeting-events.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>Back to Events
    </a>
@endsection

@section('content')
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <h5 class="card-title mb-0 fw-bold">
                <i class="bi bi-pencil-square me-2 text-primary"></i>Edit Meeting Event: {{ $meetingEvent->title }}
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.meeting-events.update', $meetingEvent) }}">
                @csrf
                @method('PUT')

                <!-- Event Title -->
                <div class="mb-4">
                    <label for="title" class="form-label fw-medium">Event Title</label>
                    <input type="text"
                           id="title"
                           name="title"
                           value="{{ old('title', $meetingEvent->title) }}"
                           class="form-control @error('title') is-invalid @enderror"
                           placeholder="Enter event title"
                           required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Event Description -->
                <div class="mb-4">
                    <label for="description" class="form-label fw-medium">Event Description</label>
                    <div class="border rounded-3 @error('description') border-danger @enderror">
                        <div id="description-toolbar" class="border-bottom p-2 bg-light rounded-top">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" onclick="formatText('bold')" class="btn btn-outline-secondary">
                                    <strong>B</strong>
                                </button>
                                <button type="button" onclick="formatText('italic')" class="btn btn-outline-secondary">
                                    <em>I</em>
                                </button>
                                <button type="button" onclick="formatText('underline')" class="btn btn-outline-secondary">
                                    <u>U</u>
                                </button>
                                <button type="button" onclick="formatText('insertUnorderedList')" class="btn btn-outline-secondary">
                                    <i class="bi bi-list-ul"></i>
                                </button>
                            </div>
                        </div>
                        <div id="description-editor"
                             contenteditable="true"
                             class="p-3 rounded-bottom"
                             style="min-height: 120px; outline: none;">{!! old('description', $meetingEvent->description) !!}</div>
                    </div>
                    <input type="hidden" id="description" name="description" value="{{ old('description', $meetingEvent->description) }}">
                    @error('description')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Rules and Regulations -->
                <div class="mb-4">
                    <label for="rules_and_regulations" class="form-label fw-medium">Rules and Regulations</label>
                    <div class="border rounded-3 @error('rules_and_regulations') border-danger @enderror">
                        <div id="rules-toolbar" class="border-bottom p-2 bg-light rounded-top">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" onclick="formatRulesText('bold')" class="btn btn-outline-secondary">
                                    <strong>B</strong>
                                </button>
                                <button type="button" onclick="formatRulesText('italic')" class="btn btn-outline-secondary">
                                    <em>I</em>
                                </button>
                                <button type="button" onclick="formatRulesText('insertUnorderedList')" class="btn btn-outline-secondary">
                                    <i class="bi bi-list-ul"></i>
                                </button>
                                <button type="button" onclick="formatRulesText('insertOrderedList')" class="btn btn-outline-secondary">
                                    <i class="bi bi-list-ol"></i>
                                </button>
                            </div>
                        </div>
                        <div id="rules-editor"
                             contenteditable="true"
                             class="p-3 rounded-bottom"
                             style="min-height: 120px; outline: none;">{!! old('rules_and_regulations', $meetingEvent->rules_and_regulations) !!}</div>
                    </div>
                    <input type="hidden" id="rules_and_regulations" name="rules_and_regulations" value="{{ old('rules_and_regulations', $meetingEvent->rules_and_regulations) }}">
                    @error('rules_and_regulations')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Payment Amounts -->
                <div class="row g-3 mb-4">
                    <div class="col-md-6" id="boys_payment_field">
                        <label for="payment_amount_boys" class="form-label fw-medium">Payment Amount for Boys (₹)</label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number"
                                   id="payment_amount_boys"
                                   name="payment_amount_boys"
                                   value="{{ old('payment_amount_boys', $meetingEvent->payment_amount_boys) }}"
                                   step="0.01"
                                   min="0"
                                   class="form-control @error('payment_amount_boys') is-invalid @enderror"
                                   required>
                        </div>
                        @error('payment_amount_boys')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6" id="girls_payment_field">
                        <label for="payment_amount_girls" class="form-label fw-medium">Payment Amount for Girls (₹)</label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number"
                                   id="payment_amount_girls"
                                   name="payment_amount_girls"
                                   value="{{ old('payment_amount_girls', $meetingEvent->payment_amount_girls) }}"
                                   step="0.01"
                                   min="0"
                                   class="form-control @error('payment_amount_girls') is-invalid @enderror"
                                   required>
                        </div>
                        @error('payment_amount_girls')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-12" id="couple_payment_field" style="display: none;">
                        <label for="payment_amount_couple" class="form-label fw-medium">Payment Amount for Couples (₹)</label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number"
                                   id="payment_amount_couple"
                                   name="payment_amount_couple"
                                   value="{{ old('payment_amount_couple', $meetingEvent->payment_amount_couple) }}"
                                   step="0.01"
                                   min="0"
                                   class="form-control @error('payment_amount_couple') is-invalid @enderror">
                        </div>
                        @error('payment_amount_couple')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Amount charged per couple for this event</small>
                    </div>
                </div>

                <!-- Event Date -->
                <div class="mb-4">
                    <label for="event_date" class="form-label fw-medium">Event Date <small class="text-muted">(Optional)</small></label>
                    <input type="datetime-local"
                           id="event_date"
                           name="event_date"
                           value="{{ old('event_date', $meetingEvent->event_date ? $meetingEvent->event_date->format('Y-m-d\TH:i') : '') }}"
                           class="form-control @error('event_date') is-invalid @enderror">
                    @error('event_date')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Location -->
                <div class="mb-4">
                    <label for="location" class="form-label fw-medium">Event Location <small class="text-muted">(Optional)</small></label>
                    <input type="text"
                           class="form-control @error('location') is-invalid @enderror"
                           id="location"
                           name="location"
                           value="{{ old('location', $meetingEvent->location) }}"
                           placeholder="Enter event location (e.g., Hotel Name, Restaurant, Park, etc.)">
                    @error('location')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Enter the venue name or address where the event will take place</small>
                </div>

                <!-- Event Status -->
                <div class="mb-4">
                    <div class="form-check">
                        <input type="checkbox"
                               id="is_event_enabled"
                               name="is_event_enabled"
                               value="1"
                               {{ old('is_event_enabled', $meetingEvent->is_event_enabled) ? 'checked' : '' }}
                               class="form-check-input">
                        <label for="is_event_enabled" class="form-check-label fw-medium">
                            <i class="bi bi-eye me-1"></i>Enable this event (users can see and access it)
                        </label>
                    </div>
                    @error('is_event_enabled')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Couple Event Option (only show if couple activity feature is enabled) -->
                @if(\App\Models\Feature::isEnabled('partner_swapping'))
                    <div class="mb-4">
                        <div class="form-check">
                            <input type="checkbox"
                                   id="is_couple_event"
                                   name="is_couple_event"
                                   value="1"
                                   {{ old('is_couple_event', $meetingEvent->is_couple_event) ? 'checked' : '' }}
                                   class="form-check-input">
                            <label for="is_couple_event" class="form-check-label fw-medium">
                                <i class="bi bi-heart me-1"></i>This is a couple event
                            </label>
                            <div class="form-text text-muted">
                                <small>Only users with couple activity enabled and a confirmed partner can register for couple events.</small>
                            </div>
                        </div>
                        @error('is_couple_event')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                @endif

                <!-- Submit Button -->
                <div class="d-flex justify-content-end gap-3">
                    <a href="{{ route('admin.meeting-events.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>Update Event
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Payment fields toggle for couple events
        document.addEventListener('DOMContentLoaded', function() {
            const coupleEventCheckbox = document.getElementById('is_couple_event');
            const boysPaymentField = document.getElementById('boys_payment_field');
            const girlsPaymentField = document.getElementById('girls_payment_field');
            const couplePaymentField = document.getElementById('couple_payment_field');
            const boysPaymentInput = document.getElementById('payment_amount_boys');
            const girlsPaymentInput = document.getElementById('payment_amount_girls');
            const couplePaymentInput = document.getElementById('payment_amount_couple');

            function togglePaymentFields() {
                if (coupleEventCheckbox && coupleEventCheckbox.checked) {
                    // Show couple payment, hide individual payments
                    boysPaymentField.style.display = 'none';
                    girlsPaymentField.style.display = 'none';
                    couplePaymentField.style.display = 'block';

                    // Clear individual payment values and remove required attribute
                    boysPaymentInput.value = '0';
                    girlsPaymentInput.value = '0';
                    boysPaymentInput.removeAttribute('required');
                    girlsPaymentInput.removeAttribute('required');

                    // Add required attribute to couple payment
                    couplePaymentInput.setAttribute('required', 'required');
                } else {
                    // Show individual payments, hide couple payment
                    boysPaymentField.style.display = 'block';
                    girlsPaymentField.style.display = 'block';
                    couplePaymentField.style.display = 'none';

                    // Clear couple payment value and add required attribute to individual payments
                    couplePaymentInput.value = '0';
                    boysPaymentInput.setAttribute('required', 'required');
                    girlsPaymentInput.setAttribute('required', 'required');

                    // Remove required attribute from couple payment
                    couplePaymentInput.removeAttribute('required');
                }
            }

            // Initial toggle based on current state
            togglePaymentFields();

            // Listen for changes to couple event checkbox
            if (coupleEventCheckbox) {
                coupleEventCheckbox.addEventListener('change', togglePaymentFields);
            }
        });

        // Rich text editor functions
        function formatText(command) {
            document.execCommand(command, false, null);
            updateHiddenField('description');
        }

        function formatRulesText(command) {
            document.getElementById('rules-editor').focus();
            document.execCommand(command, false, null);
            updateHiddenField('rules_and_regulations');
        }

        function updateHiddenField(fieldName) {
            let editorId;
            if (fieldName === 'rules_and_regulations') {
                editorId = 'rules-editor';
            } else {
                editorId = fieldName + '-editor';
            }

            const editor = document.getElementById(editorId);
            const hiddenField = document.getElementById(fieldName);

            if (editor && hiddenField) {
                hiddenField.value = editor.innerHTML;
            }
        }

        // Update hidden fields when content changes
        document.getElementById('description-editor').addEventListener('input', function() {
            updateHiddenField('description');
        });

        document.getElementById('rules-editor').addEventListener('input', function() {
            updateHiddenField('rules_and_regulations');
        });

        // Initialize hidden fields on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateHiddenField('description');
            updateHiddenField('rules_and_regulations');
        });
    </script>


@endsection
