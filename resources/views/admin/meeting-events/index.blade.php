@extends('layouts.admin')

@section('page-title', 'Meeting Events')

@section('page-description', 'Manage meeting events, schedules, and event configurations.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Meeting Events</span>
    </li>
@endsection

@section('header-actions')
    <a href="{{ route('admin.meeting-events.create') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-2"></i>Create New Event
    </a>
@endsection

@section('content')
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <h5 class="card-title mb-0 fw-bold">
                <i class="bi bi-calendar-event me-2 text-primary"></i>Meeting Events Management (<span id="events-count">{{ $events->total() }}</span>)
            </h5>
        </div>

        <div id="events-table">
            @include('admin.meeting-events.table', ['events' => $events])
        </div>
    </div>
@endsection
