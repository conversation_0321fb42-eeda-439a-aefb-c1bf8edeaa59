@extends('layouts.admin')

@section('title', 'Withdrawal Management')



@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Withdrawal Management</h1>
            <p class="mb-0 text-muted">Manage user withdrawal requests</p>
        </div>
        <div>
            <a href="{{ route('admin.withdrawals.export') }}" class="btn btn-outline-primary">
                <i class="bi bi-download"></i> Export CSV
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] }}</div>
                            <div class="text-xs text-muted">₹{{ number_format($stats['total_amount_pending'], 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-history fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Processing</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['processing'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-arrow-repeat fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completed'] }}</div>
                            <div class="text-xs text-muted">₹{{ number_format($stats['total_amount_completed'], 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Failed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['failed'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-x-circle fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Withdrawals Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Withdrawal Requests</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="withdrawalsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Bank Account</th>
                            <th>Status</th>
                            <th>Requested</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($withdrawals as $withdrawal)
                        <tr>
                            <td>#{{ $withdrawal->id }}</td>
                            <td>
                                <div>
                                    <strong>{{ $withdrawal->user->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $withdrawal->user->email }}</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>₹{{ number_format($withdrawal->amount, 2) }}</strong>
                                    @if($withdrawal->processing_fee > 0)
                                    <br>
                                    <small class="text-muted">Fee: ₹{{ number_format($withdrawal->processing_fee, 2) }}</small>
                                    <br>
                                    <small class="text-success">Net: ₹{{ number_format($withdrawal->net_amount, 2) }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $withdrawal->bankAccount->bank_name ?? 'N/A' }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $withdrawal->bankAccount->masked_account_number ?? 'N/A' }}</small>
                                </div>
                            </td>
                            <td>
                                @php
                                    switch($withdrawal->status) {
                                        case 'pending':
                                            $statusClass = 'bg-warning text-dark';
                                            $statusDisplay = 'Pending Review';
                                            break;
                                        case 'processing':
                                            $statusClass = 'bg-info text-dark';
                                            $statusDisplay = 'Processing';
                                            break;
                                        case 'completed':
                                            $statusClass = 'bg-success';
                                            $statusDisplay = 'Completed';
                                            break;
                                        case 'failed':
                                            $statusClass = 'bg-danger';
                                            $statusDisplay = 'Failed';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-secondary';
                                            $statusDisplay = 'Cancelled';
                                            break;
                                        default:
                                            $statusClass = 'bg-secondary';
                                            $statusDisplay = ucfirst($withdrawal->status ?? 'Unknown');
                                            break;
                                    }
                                @endphp
                                <span class="badge {{ $statusClass }}">{{ $statusDisplay }}</span>
                            </td>
                            <td>
                                <div>
                                    {{ $withdrawal->requested_at->format('M d, Y') }}
                                    <br>
                                    <small class="text-muted">{{ $withdrawal->requested_at->format('h:i A') }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary view-btn"
                                            data-id="{{ $withdrawal->id }}">
                                        <i class="bi bi-eye"></i> View
                                    </button>
                                    @if($withdrawal->status === 'pending')
                                    <button type="button" class="btn btn-sm btn-success process-btn"
                                            data-id="{{ $withdrawal->id }}">
                                        <i class="bi bi-check"></i> Process
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-btn"
                                            data-id="{{ $withdrawal->id }}">
                                        <i class="bi bi-x"></i> Reject
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox fa-3x mb-3"></i>
                                    <p>No withdrawal requests found.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($withdrawals->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $withdrawals->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Process Withdrawal Modal -->
<div class="modal fade" id="processModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Withdrawal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="processForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="transaction_reference">Transaction Reference *</label>
                        <input type="text" class="form-control" id="transaction_reference" 
                               name="transaction_reference" required>
                        <small class="form-text text-muted">Enter the bank transaction reference number</small>
                    </div>
                    <div class="form-group">
                        <label for="admin_notes">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Process Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Withdrawal Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Withdrawal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="reason">Rejection Reason *</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="reject_admin_notes">Admin Notes</label>
                        <textarea class="form-control" id="reject_admin_notes" name="admin_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Withdrawal Modal -->
<div class="modal fade" id="viewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdrawal Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="withdrawalDetails">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@endsection



<script>
// Simple inline script - no dependencies
document.addEventListener('DOMContentLoaded', function() {
    // Find all buttons
    const viewBtns = document.querySelectorAll('.view-btn');
    const processBtns = document.querySelectorAll('.process-btn');
    const rejectBtns = document.querySelectorAll('.reject-btn');

    let currentWithdrawalId = null;

    // View withdrawal function
    function viewWithdrawal(id) {

        fetch(`/admin/withdrawals/${id}/details`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const w = data.withdrawal;

                    const modalContent = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Withdrawal Information</h6>
                                <div class="mb-2"><strong>Request ID:</strong> #${w.id}</div>
                                <div class="mb-2"><strong>Amount:</strong> ₹${parseFloat(w.amount).toLocaleString()}</div>
                                <div class="mb-2"><strong>Processing Fee:</strong> ₹${parseFloat(w.processing_fee || 0).toLocaleString()}</div>
                                <div class="mb-2"><strong>Net Amount:</strong> ₹${parseFloat(w.net_amount).toLocaleString()}</div>
                                <div class="mb-2"><strong>Status:</strong> <span class="badge badge-warning">${w.status_display || (w.status ? w.status.charAt(0).toUpperCase() + w.status.slice(1) : 'Unknown')}</span></div>
                                <div class="mb-2"><strong>Requested:</strong> ${new Date(w.requested_at).toLocaleDateString()}</div>
                                ${w.notes ? `<div class="mb-2"><strong>User Notes:</strong> ${w.notes}</div>` : ''}
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">User Information</h6>
                                <div class="mb-2"><strong>User ID:</strong> #${w.user.id}</div>
                                <div class="mb-2"><strong>Name:</strong> ${w.user.name}</div>
                                <div class="mb-2"><strong>Email:</strong> ${w.user.email}</div>
                                <div class="mb-2"><strong>Contact:</strong> ${w.user.contact_number || 'Not provided'}</div>
                                <div class="mb-2"><strong>Gender:</strong> ${w.user.gender ? w.user.gender.charAt(0).toUpperCase() + w.user.gender.slice(1) : 'Not specified'}</div>

                                <h6 class="text-primary mb-3 mt-4">Bank Account</h6>
                                <div class="mb-2"><strong>Bank:</strong> ${w.bank_account.bank_name || 'Not provided'}</div>
                                <div class="mb-2"><strong>Account Holder:</strong> ${w.bank_account.account_holder_name || 'Not provided'}</div>
                                <div class="mb-2"><strong>Account:</strong> ${w.bank_account.masked_account_number || w.bank_account.account_number || 'Not provided'}</div>
                                <div class="mb-2"><strong>IFSC:</strong> ${w.bank_account.ifsc_code || 'Not provided'}</div>
                                <div class="mb-2"><strong>Account Type:</strong> ${w.bank_account.account_type ? w.bank_account.account_type.charAt(0).toUpperCase() + w.bank_account.account_type.slice(1) : 'Not specified'}</div>
                                <div class="mb-2"><strong>Payment Method:</strong> ${w.bank_account.payment_method_type === 'gpay' ? 'G-Pay' : 'Bank Account'}</div>
                                ${w.admin_notes ? `<div class="mb-2 mt-3"><strong>Admin Notes:</strong> ${w.admin_notes}</div>` : ''}
                            </div>
                        </div>
                    `;

                    document.getElementById('withdrawalDetails').innerHTML = modalContent;

                    // Show modal using Bootstrap 5
                    const viewModal = new bootstrap.Modal(document.getElementById('viewModal'));
                    viewModal.show();

                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error loading withdrawal details: ' + error.message);
            });
    }

    // Process withdrawal function
    function processWithdrawal(id) {
        currentWithdrawalId = id;
        // Clear form
        document.getElementById('processForm').reset();
        // Show modal using Bootstrap 5
        const processModal = new bootstrap.Modal(document.getElementById('processModal'));
        processModal.show();
    }

    // Reject withdrawal function
    function rejectWithdrawal(id) {
        currentWithdrawalId = id;
        // Clear form
        document.getElementById('rejectForm').reset();
        // Show modal using Bootstrap 5
        const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
        rejectModal.show();
    }

    // Add click listeners
    viewBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            console.log('View clicked for ID:', id);
            viewWithdrawal(id);
        });
    });

    processBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            console.log('Process clicked for ID:', id);
            processWithdrawal(id);
        });
    });

    rejectBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            console.log('Reject clicked for ID:', id);
            rejectWithdrawal(id);
        });
    });

    // Process form submission
    document.getElementById('processForm').addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Process form submitted for withdrawal ID:', currentWithdrawalId);

        if (!currentWithdrawalId) {
            alert('No withdrawal selected');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = 'Processing...';

        console.log('Sending process request to:', `/admin/withdrawals/${currentWithdrawalId}/process`);

        fetch(`/admin/withdrawals/${currentWithdrawalId}/process`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Withdrawal processed successfully!');
                const processModal = bootstrap.Modal.getInstance(document.getElementById('processModal'));
                processModal.hide();
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error processing withdrawal: ' + error.message);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Reject form submission
    document.getElementById('rejectForm').addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Reject form submitted for withdrawal ID:', currentWithdrawalId);

        if (!currentWithdrawalId) {
            alert('No withdrawal selected');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = 'Rejecting...';

        console.log('Sending reject request to:', `/admin/withdrawals/${currentWithdrawalId}/reject`);

        fetch(`/admin/withdrawals/${currentWithdrawalId}/reject`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Withdrawal rejected successfully!');
                const rejectModal = bootstrap.Modal.getInstance(document.getElementById('rejectModal'));
                rejectModal.hide();
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error rejecting withdrawal: ' + error.message);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Close button handlers
    document.querySelectorAll('[data-bs-dismiss="modal"], .close').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    });

});
</script>
