@extends('layouts.admin')

@section('title', 'Withdrawal Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Withdrawal Details</h1>
            <p class="mb-0 text-muted">Request #{{ $withdrawal->id }}</p>
        </div>
        <div>
            <a href="{{ route('admin.withdrawals.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Withdrawal Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Withdrawal Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Request ID</label>
                                <div class="fw-bold">#{{ $withdrawal->id }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Amount</label>
                                <div class="fw-bold text-primary fs-5">₹{{ number_format($withdrawal->amount, 2) }}</div>
                            </div>
                            @if($withdrawal->processing_fee > 0)
                            <div class="mb-3">
                                <label class="form-label text-muted">Processing Fee</label>
                                <div class="fw-bold text-warning">₹{{ number_format($withdrawal->processing_fee, 2) }}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Net Amount</label>
                                <div class="fw-bold text-success fs-5">₹{{ number_format($withdrawal->net_amount, 2) }}</div>
                            </div>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    @php
                                        $statusClass = match($withdrawal->status) {
                                            'pending' => 'warning',
                                            'processing' => 'info',
                                            'completed' => 'success',
                                            'failed' => 'danger',
                                            'cancelled' => 'secondary',
                                            default => 'secondary'
                                        };
                                    @endphp
                                    <span class="badge badge-{{ $statusClass }} fs-6">{{ $withdrawal->status_display }}</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Requested At</label>
                                <div class="fw-bold">{{ $withdrawal->requested_at->format('M d, Y h:i A') }}</div>
                            </div>
                            @if($withdrawal->processed_at)
                            <div class="mb-3">
                                <label class="form-label text-muted">Processed At</label>
                                <div class="fw-bold">{{ $withdrawal->processed_at->format('M d, Y h:i A') }}</div>
                            </div>
                            @endif
                            @if($withdrawal->transaction_reference)
                            <div class="mb-3">
                                <label class="form-label text-muted">Transaction Reference</label>
                                <div class="fw-bold">{{ $withdrawal->transaction_reference }}</div>
                            </div>
                            @endif
                        </div>
                    </div>

                    @if($withdrawal->notes)
                    <div class="mb-3">
                        <label class="form-label text-muted">User Notes</label>
                        <div class="p-3 bg-light rounded">{{ $withdrawal->notes }}</div>
                    </div>
                    @endif

                    @if($withdrawal->admin_notes)
                    <div class="mb-3">
                        <label class="form-label text-muted">Admin Notes</label>
                        <div class="p-3 bg-light rounded">{{ $withdrawal->admin_notes }}</div>
                    </div>
                    @endif

                    @if($withdrawal->failure_reason)
                    <div class="mb-3">
                        <label class="form-label text-muted">Failure Reason</label>
                        <div class="p-3 bg-danger-light rounded text-danger">{{ $withdrawal->failure_reason }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            @if($withdrawal->status === 'pending')
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" onclick="processWithdrawal({{ $withdrawal->id }})">
                            <i class="bi bi-check-circle"></i> Process Withdrawal
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejectWithdrawal({{ $withdrawal->id }})">
                            <i class="bi bi-x-circle"></i> Reject Withdrawal
                        </button>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- User & Bank Information -->
        <div class="col-lg-4">
            <!-- User Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Name</label>
                        <div class="fw-bold">{{ $withdrawal->user->name }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email</label>
                        <div>{{ $withdrawal->user->email }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Phone</label>
                        <div>{{ $withdrawal->user->phone ?? 'Not provided' }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">User ID</label>
                        <div class="text-muted">#{{ $withdrawal->user->id }}</div>
                    </div>
                </div>
            </div>

            <!-- Bank Account Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Bank Account</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Bank Name</label>
                        <div class="fw-bold">{{ $withdrawal->bankAccount->bank_name }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Account Holder</label>
                        <div>{{ $withdrawal->bankAccount->account_holder_name }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Account Number</label>
                        <div class="font-monospace">{{ $withdrawal->bankAccount->masked_account_number }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">IFSC Code</label>
                        <div class="font-monospace">{{ $withdrawal->bankAccount->ifsc_code }}</div>
                    </div>
                    @if($withdrawal->bankAccount->branch_name)
                    <div class="mb-3">
                        <label class="form-label text-muted">Branch</label>
                        <div>{{ $withdrawal->bankAccount->branch_name }}</div>
                    </div>
                    @endif
                </div>
            </div>

            @if($withdrawal->processedBy)
            <!-- Processed By Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Processed By</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Admin</label>
                        <div class="fw-bold">{{ $withdrawal->processedBy->name }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email</label>
                        <div>{{ $withdrawal->processedBy->email }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Processed At</label>
                        <div>{{ $withdrawal->processed_at->format('M d, Y h:i A') }}</div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Process Withdrawal Modal -->
<div class="modal fade" id="processModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Withdrawal</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="processForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="transaction_reference">Transaction Reference *</label>
                        <input type="text" class="form-control" id="transaction_reference" 
                               name="transaction_reference" required>
                        <small class="form-text text-muted">Enter the bank transaction reference number</small>
                    </div>
                    <div class="form-group">
                        <label for="admin_notes">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Process Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Withdrawal Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Withdrawal</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="reason">Rejection Reason *</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="reject_admin_notes">Admin Notes</label>
                        <textarea class="form-control" id="reject_admin_notes" name="admin_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
let currentWithdrawalId = {{ $withdrawal->id }};

function processWithdrawal(withdrawalId) {
    $('#processModal').modal('show');
}

function rejectWithdrawal(withdrawalId) {
    $('#rejectModal').modal('show');
}

// Process withdrawal form submission
$('#processForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    $.ajax({
        url: `/admin/withdrawals/${currentWithdrawalId}/process`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#processModal').modal('hide');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('Error processing withdrawal: ' + xhr.responseJSON.message);
        }
    });
});

// Reject withdrawal form submission
$('#rejectForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    $.ajax({
        url: `/admin/withdrawals/${currentWithdrawalId}/reject`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                $('#rejectModal').modal('hide');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('Error rejecting withdrawal: ' + xhr.responseJSON.message);
        }
    });
});
</script>
@endsection
