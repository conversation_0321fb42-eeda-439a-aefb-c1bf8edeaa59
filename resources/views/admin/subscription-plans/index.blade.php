@extends('layouts.admin')

@section('page-title', 'Subscription Plans')

@section('page-description', 'Manage Time Spending Service subscription plans with pricing and duration settings.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Subscription Plans</span>
    </li>
@endsection

@section('header-actions')
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPlanModal">
        <i class="bi bi-plus-circle me-2"></i>Add New Plan
    </button>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Plans</label>
                    <input type="text" class="form-control" id="search" placeholder="Search by name or description...">
                </div>
                <div class="col-md-3">
                    <label for="status-filter" class="form-label">Status</label>
                    <select class="form-select" id="status-filter">
                        <option value="">All Plans</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="sort-by" class="form-label">Sort By</label>
                    <select class="form-select" id="sort-by">
                        <option value="sort_order">Sort Order</option>
                        <option value="name">Name</option>
                        <option value="duration_months">Duration</option>
                        <option value="amount">Price</option>
                        <option value="created_at">Created Date</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort-direction" class="form-label">Order</label>
                    <select class="form-select" id="sort-direction">
                        <option value="asc">Ascending</option>
                        <option value="desc">Descending</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Plans Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Subscription Plans</h5>
            <span class="badge bg-primary" id="total-count">{{ $plans->total() }} plans</span>
        </div>
        <div class="card-body p-0">
            <div id="plans-table-container">
                @include('admin.subscription-plans.table', ['plans' => $plans])
            </div>
        </div>
    </div>
</div>

<!-- Create Plan Modal -->
<div class="modal fade" id="createPlanModal" tabindex="-1" aria-labelledby="createPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPlanModalLabel">Create New Subscription Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createPlanForm">
                @csrf
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="create-name" class="form-label">Plan Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="create-name" name="name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="create-duration" class="form-label">Duration (Months) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="create-duration" name="duration_months" min="1" max="12" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="create-original-price" class="form-label">Original Price (₹) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="create-original-price" name="original_price" min="0" step="0.01" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="create-discount-price" class="form-label">Discount Price (₹)</label>
                            <input type="number" class="form-control" id="create-discount-price" name="discount_price" min="0" step="0.01">
                            <small class="form-text text-muted">Leave empty for no discount. Must be less than original price.</small>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="create-sort-order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="create-sort-order" name="sort_order" min="0" value="0">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-12">
                            <label for="create-description" class="form-label">Description</label>
                            <textarea class="form-control" id="create-description" name="description" rows="3" placeholder="Optional plan description..."></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="create-is-active" name="is_active" checked>
                                <label class="form-check-label" for="create-is-active">
                                    Active (visible to users)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="spinner-border spinner-border-sm d-none me-2" role="status"></span>
                        Create Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Plan Modal -->
<div class="modal fade" id="editPlanModal" tabindex="-1" aria-labelledby="editPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPlanModalLabel">Edit Subscription Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editPlanForm">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit-plan-id" name="plan_id">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit-name" class="form-label">Plan Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit-name" name="name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit-duration" class="form-label">Duration (Months) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit-duration" name="duration_months" min="1" max="12" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit-original-price" class="form-label">Original Price (₹) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit-original-price" name="original_price" min="0" step="0.01" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit-discount-price" class="form-label">Discount Price (₹)</label>
                            <input type="number" class="form-control" id="edit-discount-price" name="discount_price" min="0" step="0.01">
                            <small class="form-text text-muted">Leave empty for no discount. Must be less than original price.</small>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit-sort-order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="edit-sort-order" name="sort_order" min="0">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-12">
                            <label for="edit-description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit-description" name="description" rows="3" placeholder="Optional plan description..."></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit-is-active" name="is_active">
                                <label class="form-check-label" for="edit-is-active">
                                    Active (visible to users)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="spinner-border spinner-border-sm d-none me-2" role="status"></span>
                        Update Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let searchTimeout;

    // Setup CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    
    // Search functionality
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(loadPlans, 500);
    });
    
    // Filter functionality
    $('#status-filter, #sort-by, #sort-direction').on('change', loadPlans);
    

    
    // Client-side validation for discount pricing
    function validateDiscountPricing(originalPriceId, discountPriceId) {
        const originalPrice = parseFloat($(originalPriceId).val()) || 0;
        const discountPrice = parseFloat($(discountPriceId).val()) || 0;

        if (discountPrice > 0 && discountPrice >= originalPrice) {
            $(discountPriceId).addClass('is-invalid');
            $(discountPriceId).siblings('.invalid-feedback').text('Discount price must be less than original price.');
            return false;
        } else {
            $(discountPriceId).removeClass('is-invalid');
            $(discountPriceId).siblings('.invalid-feedback').text('');
            return true;
        }
    }

    // Client-side validation for yearly plan pricing constraint
    function validateYearlyPlanPricing(durationId, originalPriceId, discountPriceId) {
        const duration = parseInt($(durationId).val()) || 0;
        const originalPrice = parseFloat($(originalPriceId).val()) || 0;
        const discountPrice = parseFloat($(discountPriceId).val()) || 0;
        const effectivePrice = discountPrice > 0 ? discountPrice : originalPrice;

        if (duration === 12 && effectivePrice > 499) {
            const errorMessage = 'Yearly plans cannot exceed ₹499. Please adjust the pricing.';

            if (discountPrice > 0) {
                $(discountPriceId).addClass('is-invalid');
                $(discountPriceId).siblings('.invalid-feedback').text(errorMessage);
            } else {
                $(originalPriceId).addClass('is-invalid');
                $(originalPriceId).siblings('.invalid-feedback').text(errorMessage);
            }
            return false;
        } else {
            // Clear yearly plan validation errors if they exist
            if (duration === 12) {
                $(originalPriceId).removeClass('is-invalid');
                $(discountPriceId).removeClass('is-invalid');
                // Only clear if the error is about yearly pricing
                if ($(originalPriceId).siblings('.invalid-feedback').text().includes('Yearly plans')) {
                    $(originalPriceId).siblings('.invalid-feedback').text('');
                }
                if ($(discountPriceId).siblings('.invalid-feedback').text().includes('Yearly plans')) {
                    $(discountPriceId).siblings('.invalid-feedback').text('');
                }
            }
            return true;
        }
    }

    // Add real-time validation
    $('#create-discount-price, #edit-discount-price').on('input', function() {
        const isCreate = $(this).attr('id').includes('create');
        const durationId = isCreate ? '#create-duration' : '#edit-duration';
        const originalPriceId = isCreate ? '#create-original-price' : '#edit-original-price';
        const discountPriceId = isCreate ? '#create-discount-price' : '#edit-discount-price';
        validateDiscountPricing(originalPriceId, discountPriceId);
        validateYearlyPlanPricing(durationId, originalPriceId, discountPriceId);
    });

    $('#create-original-price, #edit-original-price').on('input', function() {
        const isCreate = $(this).attr('id').includes('create');
        const durationId = isCreate ? '#create-duration' : '#edit-duration';
        const originalPriceId = isCreate ? '#create-original-price' : '#edit-original-price';
        const discountPriceId = isCreate ? '#create-discount-price' : '#edit-discount-price';
        validateDiscountPricing(originalPriceId, discountPriceId);
        validateYearlyPlanPricing(durationId, originalPriceId, discountPriceId);
    });

    $('#create-duration, #edit-duration').on('change', function() {
        const isCreate = $(this).attr('id').includes('create');
        const durationId = isCreate ? '#create-duration' : '#edit-duration';
        const originalPriceId = isCreate ? '#create-original-price' : '#edit-original-price';
        const discountPriceId = isCreate ? '#create-discount-price' : '#edit-discount-price';
        validateYearlyPlanPricing(durationId, originalPriceId, discountPriceId);
    });

    // Create plan form submission
    $('#createPlanForm').on('submit', function(e) {
        e.preventDefault();

        // Validate discount pricing
        if (!validateDiscountPricing('#create-original-price', '#create-discount-price')) {
            return false;
        }

        // Validate yearly plan pricing
        if (!validateYearlyPlanPricing('#create-duration', '#create-original-price', '#create-discount-price')) {
            return false;
        }
        
        const submitBtn = $(this).find('button[type="submit"]');
        const spinner = submitBtn.find('.spinner-border');
        
        submitBtn.prop('disabled', true);
        spinner.removeClass('d-none');
        
        // Build form data manually to handle checkbox properly
        const formData = {
            _token: $('meta[name="csrf-token"]').attr('content'),
            name: $('#create-name').val(),
            duration_months: $('#create-duration').val(),
            amount: $('#create-original-price').val(), // Keep for backward compatibility
            original_price: $('#create-original-price').val(),
            discount_price: $('#create-discount-price').val() || null,
            sort_order: $('#create-sort-order').val(),
            description: $('#create-description').val(),
            is_active: $('#create-is-active').is(':checked') ? 1 : 0
        };

        $.post('/admin/subscription-plans', formData)
            .done(function(response) {
                console.log('Create plan success:', response);
                $('#createPlanModal').modal('hide');
                $('#createPlanForm')[0].reset();
                loadPlans();
                showAlert(response.message, 'success');
            })
            .fail(function(xhr) {
                console.error('Create plan error:', xhr.responseText, xhr.status);
                handleFormErrors(xhr, '#createPlanForm');
            })
            .always(function() {
                submitBtn.prop('disabled', false);
                spinner.addClass('d-none');
            });
    });
    
    // Edit plan form submission
    $('#editPlanForm').on('submit', function(e) {
        e.preventDefault();

        // Validate discount pricing
        if (!validateDiscountPricing('#edit-original-price', '#edit-discount-price')) {
            return false;
        }

        // Validate yearly plan pricing
        if (!validateYearlyPlanPricing('#edit-duration', '#edit-original-price', '#edit-discount-price')) {
            return false;
        }
        
        const planId = $('#edit-plan-id').val();
        const submitBtn = $(this).find('button[type="submit"]');
        const spinner = submitBtn.find('.spinner-border');
        
        submitBtn.prop('disabled', true);
        spinner.removeClass('d-none');
        
        // Build form data manually to handle checkbox properly
        const formData = {
            _token: $('meta[name="csrf-token"]').attr('content'),
            _method: 'PUT',
            name: $('#edit-name').val(),
            duration_months: $('#edit-duration').val(),
            amount: $('#edit-original-price').val(), // Keep for backward compatibility
            original_price: $('#edit-original-price').val(),
            discount_price: $('#edit-discount-price').val() || null,
            sort_order: $('#edit-sort-order').val(),
            description: $('#edit-description').val(),
            is_active: $('#edit-is-active').is(':checked') ? 1 : 0
        };

        $.ajax({
            url: `/admin/subscription-plans/${planId}`,
            method: 'PUT',
            data: formData,
            success: function(response) {
                console.log('Update plan success:', response);
                $('#editPlanModal').modal('hide');
                loadPlans();
                showAlert(response.message, 'success');
            },
            error: function(xhr) {
                console.error('Update plan error:', xhr.responseText, xhr.status);
                handleFormErrors(xhr, '#editPlanForm');
            },
            complete: function() {
                submitBtn.prop('disabled', false);
                spinner.addClass('d-none');
            }
        });
    });
    
    // Handle form errors
    function handleFormErrors(xhr, formSelector) {
        console.log('Handling form errors:', xhr.status, xhr.responseJSON);

        if (xhr.status === 422) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                $(formSelector + ' .is-invalid').removeClass('is-invalid');
                $(formSelector + ' .invalid-feedback').text('');

                $.each(errors, function(field, messages) {
                    const input = $(formSelector + ' [name="' + field + '"]');
                    input.addClass('is-invalid');
                    input.siblings('.invalid-feedback').text(messages[0]);
                });
            } else {
                showAlert('Validation error occurred.', 'danger');
            }
        } else if (xhr.status === 500) {
            const message = xhr.responseJSON?.message || 'Server error occurred. Please try again.';
            showAlert(message, 'danger');
        } else if (xhr.status === 419) {
            showAlert('Session expired. Please refresh the page and try again.', 'danger');
        } else {
            const message = xhr.responseJSON?.message || 'An error occurred. Please try again.';
            showAlert(message, 'danger');
        }
    }
    

});

// Global variables
let loadPlansFailureCount = 0;

// Global function to show alerts
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);

    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Global function to load plans data
function loadPlans() {
    const formData = {
        search: $('#search').val(),
        status: $('#status-filter').val(),
        sort_by: $('#sort-by').val(),
        sort_direction: $('#sort-direction').val()
    };

    $.get('/admin/subscription-plans/data', formData)
        .done(function(response) {
            if (response.success) {
                $('#plans-table-container').html(response.html);
                $('#total-count').text(response.pagination.total + ' plans');
                loadPlansFailureCount = 0; // Reset failure count on success
            } else {
                showAlert('Failed to load plans data.', 'danger');
            }
        })
        .fail(function(xhr) {
            console.error('Error loading plans data:', xhr.status, xhr.responseText);
            loadPlansFailureCount++;

            if (loadPlansFailureCount >= 3) {
                showAlert('Multiple failures loading plans data. Refreshing page...', 'warning');
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
                return;
            }

            if (xhr.status === 404) {
                showAlert('Plans data endpoint not found. Please refresh the page.', 'danger');
            } else if (xhr.status === 500) {
                showAlert('Server error loading plans. Please try again.', 'danger');
            } else {
                showAlert('Error loading plans. Please try again.', 'danger');
            }
        });
}

// Global functions for table actions
function editPlan(planId) {
    $.get(`/admin/subscription-plans/${planId}`)
        .done(function(response) {
            const plan = response.plan;
            $('#edit-plan-id').val(plan.id);
            $('#edit-name').val(plan.name);
            $('#edit-duration').val(plan.duration_months);
            $('#edit-original-price').val(plan.original_price);
            $('#edit-discount-price').val(plan.discount_price || '');
            $('#edit-sort-order').val(plan.sort_order);
            $('#edit-description').val(plan.description);
            $('#edit-is-active').prop('checked', plan.is_active);
            $('#editPlanModal').modal('show');
        })
        .fail(function() {
            showAlert('Error loading plan details. Please try again.', 'danger');
        });
}

function togglePlanStatus(planId) {
    if (confirm('Are you sure you want to toggle this plan\'s status?')) {
        // Disable the button to prevent multiple clicks
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Updating...';

        $.post(`/admin/subscription-plans/${planId}/toggle-status`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
            .done(function(response) {
                console.log('Toggle status success:', response);
                loadPlans();
                showAlert(response.message, 'success');
            })
            .fail(function(xhr) {
                console.error('Toggle status error:', xhr.responseText, xhr.status);
                const message = xhr.responseJSON?.message || 'Error updating plan status. Please try again.';
                showAlert(message, 'danger');
            })
            .always(function() {
                // Re-enable the button
                button.disabled = false;
                button.innerHTML = originalText;
            });
    }
}

function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
        // Disable the button to prevent multiple clicks
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Deleting...';

        $.ajax({
            url: `/admin/subscription-plans/${planId}`,
            method: 'DELETE',
            data: { _token: $('meta[name="csrf-token"]').attr('content') },
            success: function(response) {
                loadPlans();
                showAlert(response.message, 'success');
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Error deleting plan. Please try again.';
                showAlert(message, 'danger');
            },
            complete: function() {
                // Re-enable the button
                button.disabled = false;
                button.innerHTML = originalText;
            }
        });
    }
}
</script>
@endpush
