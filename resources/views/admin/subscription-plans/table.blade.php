<div class="table-responsive">
    <table class="table table-hover align-middle">
        <thead class="table-light">
            <tr>
                <th>Sr. No</th>
                <th>Plan Name</th>
                <th>Duration</th>
                <th>Pricing</th>
                <th>Monthly Rate</th>
                <th>Status</th>
                <th>Subscriptions</th>
                <th>Sort Order</th>
                <th>Created</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($plans as $index => $plan)
                <tr>
                    <td>{{ $plans->firstItem() + $index }}</td>
                    <td>
                        <div class="fw-semibold">{{ $plan->name }}</div>
                        @if($plan->description)
                            <small class="text-muted">{{ Str::limit($plan->description, 50) }}</small>
                        @endif
                    </td>
                    <td>
                        <span class="badge bg-info">{{ $plan->duration_text }}</span>
                    </td>
                    <td>
                        @if($plan->hasDiscount())
                            <div class="d-flex flex-column">
                                <span class="fw-semibold text-success">{{ $plan->formatted_effective_price }}</span>
                                <small class="text-muted text-decoration-line-through">{{ $plan->formatted_original_price }}</small>
                                <small class="text-success">
                                    <i class="bi bi-tag-fill"></i> {{ $plan->discount_percentage }}% off
                                </small>
                            </div>
                        @else
                            <span class="fw-semibold text-success">{{ $plan->formatted_original_price }}</span>
                        @endif
                    </td>
                    <td>
                        <span class="text-muted">{{ $plan->formatted_monthly_rate }}</span>
                    </td>
                    <td>
                        @if($plan->is_active)
                            <span class="badge bg-success">Active</span>
                        @else
                            <span class="badge bg-secondary">Inactive</span>
                        @endif
                    </td>
                    <td>
                        <span class="badge bg-primary">{{ $plan->user_subscriptions_count }}</span>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">{{ $plan->sort_order }}</span>
                    </td>
                    <td>
                        <small class="text-muted">{{ $plan->created_at->format('M d, Y') }}</small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="editPlan({{ $plan->id }})" title="Edit Plan">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-{{ $plan->is_active ? 'warning' : 'success' }}" 
                                    onclick="togglePlanStatus({{ $plan->id }})" 
                                    title="{{ $plan->is_active ? 'Deactivate' : 'Activate' }} Plan">
                                <i class="bi bi-{{ $plan->is_active ? 'pause' : 'play' }}"></i>
                            </button>
                            @if($plan->user_subscriptions_count == 0)
                                <button type="button" class="btn btn-outline-danger" onclick="deletePlan({{ $plan->id }})" title="Delete Plan">
                                    <i class="bi bi-trash"></i>
                                </button>
                            @else
                                <button type="button" class="btn btn-outline-secondary" disabled title="Cannot delete - has subscriptions">
                                    <i class="bi bi-trash"></i>
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                            <p class="mb-0">No subscription plans found.</p>
                            <small>Create your first plan to get started.</small>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@if($plans->hasPages())
    <div class="d-flex justify-content-between align-items-center mt-3">
        <div class="text-muted">
            Showing {{ $plans->firstItem() }} to {{ $plans->lastItem() }} of {{ $plans->total() }} plans
        </div>
        <div>
            {{ $plans->links() }}
        </div>
    </div>
@endif
