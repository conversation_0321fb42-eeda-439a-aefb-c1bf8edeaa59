@extends('layouts.admin')

@section('page-title', 'User Management')

@section('page-description', 'Manage user accounts, permissions, and view user activity across the platform.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Users</span>
    </li>
@endsection

@section('header-actions')
    <button type="button" class="btn btn-primary">
        <i class="bi bi-plus-circle me-2"></i>Add User
    </button>
@endsection

@section('content')
    <!-- Filters and Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.users') }}" id="filterForm">
                <div class="row g-3 align-items-end">
                    <div class="col-lg-4">
                        <label class="form-label fw-medium">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" name="search" class="form-control border-start-0"
                                   placeholder="Search by name, email, or phone..."
                                   value="{{ request('search') }}" style="box-shadow: none;">
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Role</label>
                        <select name="role" class="form-select">
                            <option value="">All Roles</option>
                            <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                            <option value="user" {{ request('role') === 'user' ? 'selected' : '' }}>User</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="paid" {{ request('status') === 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="not_paid" {{ request('status') === 'not_paid' ? 'selected' : '' }}>Not Paid</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Gender</label>
                        <select name="gender" class="form-select">
                            <option value="">All Genders</option>
                            <option value="male" {{ request('gender') === 'male' ? 'selected' : '' }}>Male</option>
                            <option value="female" {{ request('gender') === 'female' ? 'selected' : '' }}>Female</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-funnel me-1"></i>Filter
                            </button>
                            <a href="{{ route('admin.users') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Hidden sorting inputs -->
                <input type="hidden" name="sort_by" value="{{ request('sort_by', 'created_at') }}">
                <input type="hidden" name="sort_order" value="{{ request('sort_order', 'desc') }}">
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fw-bold">
                    <i class="bi bi-people me-2 text-primary"></i>All Users (<span id="users-count">{{ $users->total() ?? $users->count() }}</span>)
                </h5>
                <div class="d-flex align-items-center gap-3">
                    <!-- Bulk Actions -->
                    <div class="d-none" id="bulk-actions">
                        <select class="form-select form-select-sm">
                            <option>Bulk Actions</option>
                            <option>Make Admin</option>
                            <option>Remove Admin</option>
                            <option>Export Selected</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <div id="users-table">
            @include('admin.users.table', ['users' => $users])
        </div>
    </div>
@endsection
