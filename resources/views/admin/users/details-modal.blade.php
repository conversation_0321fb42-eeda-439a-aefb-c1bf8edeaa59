<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header bg-primary text-white">
            <h5 class="modal-title">
                <i class="bi bi-person-circle me-2"></i>User Details
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="row">
                <!-- Profile Section -->
                <div class="col-md-4 text-center mb-4">
                    @if($user->profile_picture)
                        <img class="rounded-circle mb-3" src="{{ asset('storage/' . $user->profile_picture) }}" alt="{{ $user->name }}" style="width: 120px; height: 120px; object-fit: cover;">
                    @else
                        <div class="rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center"
                             style="width: 120px; height: 120px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <span class="text-white fw-bold" style="font-size: 2rem;">{{ substr($user->name, 0, 1) }}</span>
                        </div>
                    @endif
                    <h5 class="fw-bold">{{ $user->name }}</h5>
                    <p class="text-muted">User ID: {{ $user->id }}</p>

                    <!-- Status Badge -->
                    @if($user->is_suspended ?? false)
                        <span class="badge bg-danger-subtle text-danger">
                            <i class="bi bi-pause-circle me-1"></i>Suspended
                        </span>
                    @else
                        <span class="badge bg-success-subtle text-success">
                            <i class="bi bi-check-circle me-1"></i>Active
                        </span>
                    @endif
                </div>

                <!-- Details Section -->
                <div class="col-md-8">
                    <div class="row g-3">
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Email</label>
                            <div class="d-flex align-items-center">
                                <span>{{ $user->email }}</span>
                                <i class="bi {{ $user->email_verified_at ? 'bi-check-circle text-success' : 'bi-x-circle text-danger' }} ms-2"></i>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Contact Number</label>
                            <div>{{ $user->contact_number ?? 'Not provided' }}</div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Gender</label>
                            <div>
                                @if($user->gender === 'male')
                                    <span class="badge bg-info-subtle text-info">
                                        <i class="bi bi-person me-1"></i>
                                        Male
                                    </span>
                                @elseif($user->gender === 'female')
                                    <span class="badge bg-warning-subtle text-warning">
                                        <i class="bi bi-person-dress me-1"></i>
                                        Female
                                    </span>
                                @else
                                    <span class="text-muted">Not specified</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Date of Birth</label>
                            <div>{{ $user->date_of_birth ? $user->date_of_birth->format('M d, Y') : 'Not provided' }}</div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Interests</label>
                            <div>
                                @if($user->interests)
                                    @foreach(explode(',', $user->interests) as $interest)
                                        <span class="badge bg-secondary-subtle text-secondary me-1">{{ trim($interest) }}</span>
                                    @endforeach
                                @else
                                    <span class="text-muted">Not specified</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Expectations</label>
                            <div>{{ $user->expectation ?? 'Not specified' }}</div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Joined Date</label>
                            <div>{{ $user->created_at->format('M d, Y H:i') }}</div>
                        </div>
                        <div class="col-sm-6">
                            <label class="form-label fw-medium text-muted">Last Updated</label>
                            <div>{{ $user->updated_at->format('M d, Y H:i') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Log Section -->
            <hr class="my-4">
            <h6 class="fw-bold mb-3">
                <i class="bi bi-clock-history me-2 text-primary"></i>Change Log History
            </h6>
            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-sm">
                    <thead class="table-light sticky-top">
                        <tr>
                            <th style="width: 120px;">Date & Time</th>
                            <th style="width: 120px;">Field Changed</th>
                            <th>Previous Value</th>
                            <th>New Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($user->changeLogs->take(20) as $log)
                            <tr>
                                <td>
                                    <div class="fw-medium" style="font-size: 0.8rem;">{{ $log->created_at->format('M d, Y') }}</div>
                                    <small class="text-muted">{{ $log->created_at->format('H:i A') }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info-subtle text-info">
                                        {{ $log->formatted_field_name }}
                                    </span>
                                </td>
                                <td>
                                    <div class="text-muted" style="font-size: 0.85rem;">
                                        @if($log->field_name === 'profile_picture' && $log->old_value)
                                            <i class="bi bi-image me-1"></i>Previous image
                                        @elseif(empty($log->old_value))
                                            <em class="text-muted">Not set</em>
                                        @else
                                            {{ Str::limit($log->formatted_old_value, 30) }}
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="text-success fw-medium" style="font-size: 0.85rem;">
                                        @if($log->field_name === 'profile_picture' && $log->new_value)
                                            <i class="bi bi-image me-1"></i>New image uploaded
                                        @elseif(empty($log->new_value))
                                            <em class="text-danger">Removed</em>
                                        @else
                                            {{ Str::limit($log->formatted_new_value, 30) }}
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="bi bi-clock-history display-6 text-muted mb-2"></i>
                                    <div>No change logs available</div>
                                    <small>Changes will appear here when the user updates their profile</small>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            @if($user->changeLogs->count() > 20)
                <div class="text-center mt-2">
                    <small class="text-muted">Showing latest 20 changes. Total: {{ $user->changeLogs->count() }} changes</small>
                </div>
            @endif
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            @if($user->is_suspended ?? false)
                <button type="button" class="btn btn-success btn-activate-user" data-user-id="{{ $user->id }}">
                    <i class="bi bi-check-circle me-2"></i>Activate User
                </button>
            @else
                <button type="button" class="btn btn-warning btn-suspend-user" data-user-id="{{ $user->id }}">
                    <i class="bi bi-pause-circle me-2"></i>Suspend User
                </button>
            @endif
        </div>
    </div>
</div>
