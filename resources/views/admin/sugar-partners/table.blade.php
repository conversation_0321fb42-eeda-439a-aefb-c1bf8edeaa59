@if ($users->count() > 0)
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th scope="col" style="width: 60px;">Sr. No</th>
                    <th scope="col" style="width: 80px;">Profile</th>
                    <th scope="col" class="sortable" style="cursor: pointer;" onclick="sortTable('name')">
                        <div class="d-flex align-items-center">
                            <span>Username</span>
                            @if(request('sort_by') === 'name')
                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                            @else
                                <i class="bi bi-arrow-down-up ms-1 text-muted"></i>
                            @endif
                        </div>
                    </th>
                    <th scope="col">Phone Number</th>
                    <th scope="col">Gender</th>
                    <th scope="col" class="sortable" style="cursor: pointer;" onclick="sortTable('email')">
                        <div class="d-flex align-items-center">
                            <span>Email Address</span>
                            @if(request('sort_by') === 'email')
                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                            @else
                                <i class="bi bi-arrow-down-up ms-1 text-muted"></i>
                            @endif
                        </div>
                    </th>
                    <th scope="col" style="width: 120px;">I Am</th>
                    <th scope="col" style="width: 140px;">I Want</th>
                    <th scope="col" style="width: 150px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $index => $user)
                    <tr>
                        <td class="fw-medium">{{ $users->firstItem() + $index }}</td>
                        <td>
                            @if($user->profile_picture)
                                <img class="rounded-circle" src="{{ asset('storage/' . $user->profile_picture) }}" alt="{{ $user->name }}" style="width: 50px; height: 50px; object-fit: cover;">
                            @else
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #e11d48 0%, #f43f5e 100%);">
                                    <span class="text-white fw-semibold">{{ substr($user->name, 0, 1) }}</span>
                                </div>
                            @endif
                        </td>
                        <td>
                            <div class="fw-medium">{{ $user->name }}</div>
                            <small class="text-muted">ID: {{ $user->id }}</small>
                        </td>
                        <td>
                            <div>{{ $user->contact_number ?? 'Not provided' }}</div>
                        </td>
                        <td>
                            @if($user->gender === 'male')
                                <span class="badge bg-info-subtle text-info">
                                    <i class="bi bi-person me-1"></i>
                                    Male
                                </span>
                            @elseif($user->gender === 'female')
                                <span class="badge bg-warning-subtle text-warning">
                                    <i class="bi bi-person-dress me-1"></i>
                                    Female
                                </span>
                            @else
                                <span class="text-muted">Not specified</span>
                            @endif
                        </td>
                        <td>
                            <div>{{ $user->email }}</div>
                            <small class="text-muted">
                                <i class="bi {{ $user->email_verified_at ? 'bi-check-circle text-success' : 'bi-x-circle text-danger' }}"></i>
                                {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                            </small>
                        </td>
                        <!-- I Am Column -->
                        <td>
                            @php $whatIAm = $user->getWhatIAmForAdmin(); @endphp
                            @if($whatIAm !== 'None')
                                <span class="badge bg-primary-subtle text-primary">
                                    <i class="bi bi-person-check me-1"></i>
                                    {{ $whatIAm }}
                                </span>
                            @else
                                <span class="text-muted">None</span>
                            @endif
                        </td>
                        <!-- I Want Column -->
                        <td>
                            @php $whatIWant = $user->getWhatIWantForAdmin(); @endphp
                            @if(!in_array('None', $whatIWant))
                                <div class="d-flex flex-wrap gap-1">
                                    @foreach($whatIWant as $type)
                                        <span class="badge bg-danger-subtle text-danger">
                                            <i class="bi bi-heart me-1"></i>
                                            {{ $type }}
                                        </span>
                                    @endforeach
                                </div>
                            @else
                                <span class="text-muted">None</span>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="{{ route('admin.sugar-partners.profile', $user->id) }}" 
                                   class="btn btn-outline-primary btn-sm"
                                   title="View Profile" data-bs-toggle="tooltip">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @if($user->is_suspended ?? false)
                                    <button type="button" class="btn btn-outline-success btn-sm btn-activate-user"
                                            data-user-id="{{ $user->id }}"
                                            title="Activate User" data-bs-toggle="tooltip">
                                        <i class="bi bi-check-circle"></i>
                                    </button>
                                @else
                                    <button type="button" class="btn btn-outline-warning btn-sm btn-suspend-user"
                                            data-user-id="{{ $user->id }}"
                                            title="Suspend User" data-bs-toggle="tooltip">
                                        <i class="bi bi-pause-circle"></i>
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="card-footer bg-white border-top">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-block d-sm-none">
                @if($users->hasPages())
                    <div class="d-flex gap-2">
                        @if($users->onFirstPage())
                            <span class="btn btn-outline-secondary btn-sm disabled">Previous</span>
                        @else
                            <button class="btn btn-outline-primary btn-sm" onclick="loadPage({{ $users->currentPage() - 1 }})">Previous</button>
                        @endif

                        @if($users->hasMorePages())
                            <button class="btn btn-outline-primary btn-sm" onclick="loadPage({{ $users->currentPage() + 1 }})">Next</button>
                        @else
                            <span class="btn btn-outline-secondary btn-sm disabled">Next</span>
                        @endif
                    </div>
                @endif
            </div>
            <div class="d-none d-sm-flex justify-content-between align-items-center w-100">
                <div>
                    <small class="text-muted">
                        @if($users->total() > 0)
                            Showing <strong>{{ $users->firstItem() }}</strong> to <strong>{{ $users->lastItem() }}</strong> of <strong>{{ $users->total() }}</strong> results
                        @else
                            No results found
                        @endif
                    </small>
                </div>
                <div>
                    @if($users->hasPages())
                        <nav aria-label="Page navigation">
                            <ul class="pagination pagination-sm mb-0">
                                @if($users->onFirstPage())
                                    <li class="page-item disabled"><span class="page-link">Previous</span></li>
                                @else
                                    <li class="page-item"><button class="page-link" onclick="loadPage({{ $users->currentPage() - 1 }})">Previous</button></li>
                                @endif

                                @foreach($users->getUrlRange(1, $users->lastPage()) as $page => $url)
                                    @if($page == $users->currentPage())
                                        <li class="page-item active"><span class="page-link">{{ $page }}</span></li>
                                    @else
                                        <li class="page-item"><button class="page-link" onclick="loadPage({{ $page }})">{{ $page }}</button></li>
                                    @endif
                                @endforeach

                                @if($users->hasMorePages())
                                    <li class="page-item"><button class="page-link" onclick="loadPage({{ $users->currentPage() + 1 }})">Next</button></li>
                                @else
                                    <li class="page-item disabled"><span class="page-link">Next</span></li>
                                @endif
                            </ul>
                        </nav>
                    @endif
                </div>
            </div>
        </div>
    </div>
@else
    <!-- Empty State -->
    <div class="card-body text-center py-5">
        <i class="bi bi-heart display-1 text-muted mb-3"></i>
        <h5 class="fw-bold">No Sugar Partners found</h5>
        <p class="text-muted mb-4">No Sugar Partners match your current filters.</p>
        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
            <i class="bi bi-arrow-clockwise me-2"></i>Clear Filters
        </button>
    </div>
@endif

<script>
function clearFilters() {
    document.querySelector('input[name="search"]').value = '';
    document.querySelector('select[name="gender"]').value = '';
    document.querySelector('select[name="sugar_partner_type"]').value = '';
    document.querySelector('select[name="status"]').value = '';
    document.querySelector('input[name="date_from"]').value = '';
    document.querySelector('input[name="date_to"]').value = '';

    document.getElementById('filterForm').submit();
}
</script>
