@extends('layouts.admin')

@section('page-title', 'Exchange #' . $exchange->id)

@section('page-description', 'View detailed information about this Sugar Partner profile exchange.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <a href="{{ route('admin.sugar-partners.index') }}">Sugar Partners</a>
    </li>
    <li class="breadcrumb-item">
        <a href="{{ route('admin.sugar-partners.exchanges.index') }}">Exchanges</a>
    </li>
    <li class="breadcrumb-item">
        <span class="text-muted">Exchange #{{ $exchange->id }}</span>
    </li>
@endsection

@section('header-actions')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.sugar-partners.exchanges.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to Exchanges
        </a>
        @if(!in_array($exchange->status, ['responses_completed', 'cancelled']))
            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelExchangeModal">
                <i class="bi bi-x-circle me-2"></i>Cancel Exchange
            </button>
        @endif
    </div>
@endsection

@section('content')
<div class="row">
    <!-- Exchange Overview -->
    <div class="col-12 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">
                    <i class="bi bi-arrow-left-right me-2"></i>Exchange Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">Exchange ID</h6>
                            <span class="badge bg-primary fs-6">#{{ $exchange->id }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">Status</h6>
                            @php
                                $statusClass = match($exchange->status) {
                                    'pending_payment' => 'bg-warning',
                                    'payment_completed' => 'bg-info',
                                    'profiles_viewed' => 'bg-primary',
                                    'responses_completed' => 'bg-success',
                                    'cancelled' => 'bg-danger',
                                    default => 'bg-secondary'
                                };
                            @endphp
                            <span class="badge {{ $statusClass }} fs-6">
                                {{ ucfirst(str_replace('_', ' ', $exchange->status)) }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">Exchange Price</h6>
                            <span class="fw-bold fs-6">{{ $exchange->currency }} {{ number_format($exchange->exchange_price, 2) }}</span>
                            <small class="text-muted d-block">per user</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">Initiated</h6>
                            <span class="fw-bold">{{ $exchange->created_at->format('M d, Y') }}</span>
                            <small class="text-muted d-block">{{ $exchange->created_at->format('h:i A') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Information -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>{{ $exchange->user1->name }}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    @if($exchange->user1->profile_picture)
                        <img src="{{ asset('storage/' . $exchange->user1->profile_picture) }}" 
                             alt="{{ $exchange->user1->name }}" 
                             class="rounded-circle me-3" 
                             style="width: 60px; height: 60px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-person text-muted fs-4"></i>
                        </div>
                    @endif
                    <div>
                        <h6 class="mb-1">{{ $exchange->user1->name }}</h6>
                        <p class="text-muted mb-0">{{ $exchange->user1->email }}</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>Sugar Partner Type:</strong>
                    @php $whatIAm1 = $exchange->user1->getWhatIAmForAdmin(); @endphp
                    @if($whatIAm1 !== 'None')
                        <span class="badge bg-primary-subtle text-primary">{{ $whatIAm1 }}</span>
                    @else
                        <span class="text-muted">Not specified</span>
                    @endif
                </div>

                <div class="mb-3">
                    <strong>Payment Status:</strong>
                    @if($exchange->userHasPaid($exchange->user1_id))
                        <span class="badge bg-success">Paid</span>
                    @else
                        <span class="badge bg-warning">Pending</span>
                    @endif
                </div>

                @php $user1Response = $exchange->getUserResponse($exchange->user1_id); @endphp
                <div class="mb-3">
                    <strong>Response:</strong>
                    @if($user1Response)
                        <span class="badge {{ $user1Response->getRejectionTypeBadgeClass() }}">
                            {{ $user1Response->getRejectionTypeDisplayName() }}
                        </span>
                    @else
                        <span class="badge bg-secondary">Pending</span>
                    @endif
                </div>

                <a href="{{ route('admin.sugar-partners.profile', $exchange->user1) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-eye me-1"></i>View Full Profile
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>{{ $exchange->user2->name }}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    @if($exchange->user2->profile_picture)
                        <img src="{{ asset('storage/' . $exchange->user2->profile_picture) }}" 
                             alt="{{ $exchange->user2->name }}" 
                             class="rounded-circle me-3" 
                             style="width: 60px; height: 60px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-person text-muted fs-4"></i>
                        </div>
                    @endif
                    <div>
                        <h6 class="mb-1">{{ $exchange->user2->name }}</h6>
                        <p class="text-muted mb-0">{{ $exchange->user2->email }}</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>Sugar Partner Type:</strong>
                    @php $whatIAm2 = $exchange->user2->getWhatIAmForAdmin(); @endphp
                    @if($whatIAm2 !== 'None')
                        <span class="badge bg-primary-subtle text-primary">{{ $whatIAm2 }}</span>
                    @else
                        <span class="text-muted">Not specified</span>
                    @endif
                </div>

                <div class="mb-3">
                    <strong>Payment Status:</strong>
                    @if($exchange->userHasPaid($exchange->user2_id))
                        <span class="badge bg-success">Paid</span>
                    @else
                        <span class="badge bg-warning">Pending</span>
                    @endif
                </div>

                @php $user2Response = $exchange->getUserResponse($exchange->user2_id); @endphp
                <div class="mb-3">
                    <strong>Response:</strong>
                    @if($user2Response)
                        <span class="badge {{ $user2Response->getRejectionTypeBadgeClass() }}">
                            {{ $user2Response->getRejectionTypeDisplayName() }}
                        </span>
                    @else
                        <span class="badge bg-secondary">Pending</span>
                    @endif
                </div>

                <a href="{{ route('admin.sugar-partners.profile', $exchange->user2) }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-eye me-1"></i>View Full Profile
                </a>
            </div>
        </div>
    </div>

    <!-- Payment Details -->
    @if($exchange->payments->count() > 0)
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-credit-card me-2"></i>Payment Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Paid At</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($exchange->payments as $payment)
                                    <tr>
                                        <td>{{ $payment->user->name }}</td>
                                        <td>{{ $payment->currency }} {{ number_format($payment->amount, 2) }}</td>
                                        <td>{{ $payment->getPaymentMethodDisplayName() }}</td>
                                        <td>
                                            <span class="badge {{ $payment->getStatusBadgeClass() }}">
                                                {{ ucfirst($payment->status) }}
                                            </span>
                                        </td>
                                        <td>{{ $payment->paid_at?->format('M d, Y h:i A') ?? 'N/A' }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Responses -->
    @if($exchange->rejections->count() > 0)
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-chat-heart me-2"></i>User Responses
                    </h6>
                </div>
                <div class="card-body">
                    @foreach($exchange->rejections as $rejection)
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">{{ $rejection->rejector->name }}</h6>
                                    <span class="badge {{ $rejection->getRejectionTypeBadgeClass() }}">
                                        {{ $rejection->getRejectionTypeDisplayName() }}
                                    </span>
                                </div>
                                @if($rejection->rejection_reason)
                                    <p class="mb-2"><strong>Reason:</strong> {{ $rejection->rejection_reason }}</p>
                                @endif
                                @if($rejection->admin_note)
                                    <div class="alert alert-info mb-2">
                                        <strong>Admin Note:</strong> {{ $rejection->admin_note }}
                                    </div>
                                @endif
                                <small class="text-muted">
                                    Submitted: {{ $rejection->created_at->format('M d, Y h:i A') }}
                                    @if($rejection->notification_sent)
                                        • Notification sent
                                    @endif
                                </small>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Rejection History -->
    @if($rejectionHistory['total_rejections'] > 0)
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Rejection History Between Users
                    </h6>
                </div>
                <div class="card-body">
                    @if($rejectionHistory['has_hard_reject'])
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Hard Reject Alert:</strong> These users have previously hard rejected each other.
                        </div>
                    @endif
                    
                    @if($rejectionHistory['soft_reject_count'] > 0)
                        <div class="alert alert-warning">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Soft Rejections:</strong> {{ $rejectionHistory['soft_reject_count'] }} previous soft rejection(s) between these users.
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Rejector</th>
                                    <th>Type</th>
                                    <th>Reason</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($rejectionHistory['rejections'] as $rejection)
                                    <tr>
                                        <td>{{ $rejection->created_at->format('M d, Y') }}</td>
                                        <td>{{ $rejection->rejector->name }}</td>
                                        <td>
                                            <span class="badge {{ $rejection->getRejectionTypeBadgeClass() }}">
                                                {{ $rejection->getRejectionTypeDisplayName() }}
                                            </span>
                                        </td>
                                        <td>{{ $rejection->rejection_reason ?: 'No reason provided' }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Admin Notes -->
    @if($exchange->admin_notes)
        <div class="col-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-sticky me-2"></i>Admin Notes
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ $exchange->admin_notes }}</p>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Cancel Exchange Modal -->
@if(!in_array($exchange->status, ['responses_completed', 'cancelled']))
<div class="modal fade" id="cancelExchangeModal" tabindex="-1" aria-labelledby="cancelExchangeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelExchangeModalLabel">
                    <i class="bi bi-x-circle me-2"></i>Cancel Exchange
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('admin.sugar-partners.exchanges.cancel', $exchange) }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Cancelling this exchange will refund any completed payments and notify both users.
                    </div>

                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Cancellation Reason</label>
                        <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" required placeholder="Explain why this exchange is being cancelled..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-x-circle me-1"></i>Cancel Exchange
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection
