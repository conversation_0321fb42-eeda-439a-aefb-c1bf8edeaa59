@extends('layouts.admin')

@section('page-title', 'Sugar Partner Exchanges')

@section('page-description', 'Manage Sugar Partner profile exchanges, view payment status, and monitor user responses.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <a href="{{ route('admin.sugar-partners.index') }}">Sugar Partners</a>
    </li>
    <li class="breadcrumb-item">
        <span class="text-muted">Exchanges</span>
    </li>
@endsection

@section('header-actions')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.sugar-partners.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to Sugar Partners
        </a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#initiateExchangeModal">
            <i class="bi bi-plus-circle me-2"></i>Initiate New Exchange
        </button>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <!-- Status Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">{{ $statusCounts['all'] }}</h5>
                        <p class="card-text small">Total Exchanges</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">{{ $statusCounts['pending_payment'] }}</h5>
                        <p class="card-text small">Pending Payment</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">{{ $statusCounts['payment_completed'] }}</h5>
                        <p class="card-text small">Payment Complete</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">{{ $statusCounts['profiles_viewed'] }}</h5>
                        <p class="card-text small">Profiles Viewed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">{{ $statusCounts['responses_completed'] }}</h5>
                        <p class="card-text small">Completed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-danger">{{ $statusCounts['cancelled'] }}</h5>
                        <p class="card-text small">Cancelled</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.sugar-partners.exchanges.index') }}" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Search by user name or email...">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="pending_payment" {{ request('status') === 'pending_payment' ? 'selected' : '' }}>Pending Payment</option>
                            <option value="payment_completed" {{ request('status') === 'payment_completed' ? 'selected' : '' }}>Payment Completed</option>
                            <option value="profiles_viewed" {{ request('status') === 'profiles_viewed' ? 'selected' : '' }}>Profiles Viewed</option>
                            <option value="responses_completed" {{ request('status') === 'responses_completed' ? 'selected' : '' }}>Responses Completed</option>
                            <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.sugar-partners.exchanges.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Exchanges Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-arrow-left-right me-2"></i>Profile Exchanges
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshExchanges()">
                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Exchange ID</th>
                                <th>Users</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Payment Status</th>
                                <th>Initiated By</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($exchanges as $exchange)
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">#{{ $exchange->id }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-semibold">{{ $exchange->user1->name }}</span>
                                            <small class="text-muted">{{ $exchange->user1->email }}</small>
                                        </div>
                                        <div class="text-center my-1">
                                            <i class="bi bi-arrow-left-right text-muted"></i>
                                        </div>
                                        <div class="d-flex flex-column">
                                            <span class="fw-semibold">{{ $exchange->user2->name }}</span>
                                            <small class="text-muted">{{ $exchange->user2->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ $exchange->currency }} {{ number_format($exchange->exchange_price, 2) }}</span>
                                        <small class="text-muted d-block">per user</small>
                                    </td>
                                    <td>
                                        @php
                                            $statusClass = match($exchange->status) {
                                                'pending_payment' => 'bg-warning',
                                                'payment_completed' => 'bg-info',
                                                'profiles_viewed' => 'bg-primary',
                                                'responses_completed' => 'bg-success',
                                                'cancelled' => 'bg-danger',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $statusClass }}">
                                            {{ ucfirst(str_replace('_', ' ', $exchange->status)) }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $user1Paid = $exchange->userHasPaid($exchange->user1_id);
                                            $user2Paid = $exchange->userHasPaid($exchange->user2_id);
                                        @endphp
                                        <div class="d-flex flex-column gap-1">
                                            <span class="badge {{ $user1Paid ? 'bg-success' : 'bg-secondary' }} small">
                                                {{ $exchange->user1->name }}: {{ $user1Paid ? 'Paid' : 'Pending' }}
                                            </span>
                                            <span class="badge {{ $user2Paid ? 'bg-success' : 'bg-secondary' }} small">
                                                {{ $exchange->user2->name }}: {{ $user2Paid ? 'Paid' : 'Pending' }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-semibold">{{ $exchange->initiatedByAdmin->name }}</span>
                                            <small class="text-muted">{{ $exchange->created_at->format('M d, Y') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {{ $exchange->created_at->format('M d, Y h:i A') }}
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.sugar-partners.exchanges.show', $exchange) }}" 
                                               class="btn btn-outline-primary btn-sm"
                                               title="View Details" data-bs-toggle="tooltip">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            @if(!in_array($exchange->status, ['responses_completed', 'cancelled']))
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                        onclick="cancelExchange({{ $exchange->id }})"
                                                        title="Cancel Exchange" data-bs-toggle="tooltip">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-arrow-left-right display-4 d-block mb-3"></i>
                                            <h5>No Exchanges Found</h5>
                                            <p>No profile exchanges have been initiated yet.</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($exchanges->hasPages())
                <div class="card-footer">
                    {{ $exchanges->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function refreshExchanges() {
    window.location.reload();
}

function cancelExchange(exchangeId) {
    // Implementation for cancel exchange modal
    // This would open a modal to get cancellation reason
    alert('Cancel exchange functionality - to be implemented');
}
</script>
@endpush
