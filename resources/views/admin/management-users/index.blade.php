@extends('layouts.admin')

@section('page-title', 'Management Users')

@section('page-description', 'Manage admin users, super admins, editors, and accountants with role-based access control.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Management Users</span>
    </li>
@endsection

@section('header-actions')
    <button type="button" class="btn btn-primary">
        <i class="bi bi-plus-circle me-2"></i>Add Management User
    </button>
@endsection

@section('content')
    <!-- Filters and Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.management-users') }}" id="filterForm">
                <div class="row g-3 align-items-end">
                    <div class="col-lg-4">
                        <label class="form-label fw-medium">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" name="search" class="form-control border-start-0"
                                   placeholder="Search by name, email, or phone..."
                                   value="{{ request('search') }}" style="box-shadow: none;">
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Role</label>
                        <select name="role" class="form-select">
                            <option value="">All Roles</option>
                            <option value="super_admin" {{ request('role') === 'super_admin' ? 'selected' : '' }}>Super Admin</option>
                            <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                            <option value="editor" {{ request('role') === 'editor' ? 'selected' : '' }}>Editor</option>
                            <option value="accountant" {{ request('role') === 'accountant' ? 'selected' : '' }}>Accountant</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-funnel me-1"></i>Filter
                            </button>
                            <a href="{{ route('admin.management-users') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Hidden sorting inputs -->
                <input type="hidden" name="sort_by" value="{{ request('sort_by', 'created_at') }}">
                <input type="hidden" name="sort_order" value="{{ request('sort_order', 'desc') }}">
            </form>
        </div>
    </div>

    <!-- Management Users Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0 fw-bold">
                        <i class="bi bi-shield-check me-2 text-primary"></i>Management Users
                    </h5>
                    <p class="text-muted mb-0 mt-1"><span id="management-users-count">{{ $users->total() }}</span> management users found</p>
                </div>
                <div class="d-flex gap-2">
                    <span class="badge bg-danger-subtle text-danger">
                        <i class="bi bi-shield-fill-exclamation me-1"></i>Super Admin
                    </span>
                    <span class="badge bg-primary-subtle text-primary">
                        <i class="bi bi-shield-check me-1"></i>Admin
                    </span>
                    <span class="badge bg-info-subtle text-info">
                        <i class="bi bi-pencil-square me-1"></i>Editor
                    </span>
                    <span class="badge bg-warning-subtle text-warning">
                        <i class="bi bi-calculator me-1"></i>Accountant
                    </span>
                </div>
            </div>
        </div>

        <div id="management-users-table">
            @include('admin.management-users.table', ['users' => $users])
        </div>
    </div>
@endsection
