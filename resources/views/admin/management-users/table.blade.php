@if ($users->count() > 0)
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th scope="col" style="width: 60px;">Sr. No</th>
                    <th scope="col" style="width: 80px;">Profile</th>
                    <th scope="col" class="sortable" style="cursor: pointer;" onclick="sortManagementTable('name')">
                        <div class="d-flex align-items-center">
                            <span>Full Name</span>
                            @if(request('sort_by') === 'name')
                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                            @else
                                <i class="bi bi-arrow-down-up ms-1 text-muted"></i>
                            @endif
                        </div>
                    </th>
                    <th scope="col">Contact Number</th>
                    <th scope="col">Role</th>
                    <th scope="col" class="sortable" style="cursor: pointer;" onclick="sortManagementTable('email')">
                        <div class="d-flex align-items-center">
                            <span>Email</span>
                            @if(request('sort_by') === 'email')
                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                            @else
                                <i class="bi bi-arrow-down-up ms-1 text-muted"></i>
                            @endif
                        </div>
                    </th>
                    <th scope="col" style="width: 150px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($users as $index => $user)
                    <tr>
                        <td class="fw-medium">{{ $users->firstItem() + $index }}</td>
                        <td>
                            @if($user->profile_picture)
                                <img class="rounded-circle" src="{{ asset('storage/' . $user->profile_picture) }}" alt="{{ $user->name }}" style="width: 50px; height: 50px; object-fit: cover;">
                            @else
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <span class="text-white fw-semibold">{{ substr($user->name, 0, 1) }}</span>
                                </div>
                            @endif
                        </td>
                        <td>
                            <div class="fw-medium">{{ $user->name }}</div>
                            <small class="text-muted">ID: {{ $user->id }}</small>
                        </td>
                        <td>
                            <div>{{ $user->contact_number ?? 'Not provided' }}</div>
                        </td>
                        <td>
                            @if($user->role)
                                <span class="badge 
                                    @if($user->role === 'super_admin') bg-danger-subtle text-danger
                                    @elseif($user->role === 'admin') bg-primary-subtle text-primary
                                    @elseif($user->role === 'editor') bg-info-subtle text-info
                                    @elseif($user->role === 'accountant') bg-success-subtle text-success
                                    @else bg-secondary-subtle text-secondary
                                    @endif">
                                    <i class="bi 
                                        @if($user->role === 'super_admin') bi-shield-fill-exclamation
                                        @elseif($user->role === 'admin') bi-shield-check
                                        @elseif($user->role === 'editor') bi-pencil-square
                                        @elseif($user->role === 'accountant') bi-calculator
                                        @else bi-person
                                        @endif me-1"></i>
                                    {{ ucfirst(str_replace('_', ' ', $user->role)) }}
                                </span>
                            @else
                                <span class="text-muted">No role assigned</span>
                            @endif
                        </td>
                        <td>
                            <div>{{ $user->email }}</div>
                            <small class="text-muted">
                                <i class="bi {{ $user->email_verified_at ? 'bi-check-circle text-success' : 'bi-x-circle text-danger' }}"></i>
                                {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                            </small>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <button type="button" class="btn btn-outline-info btn-sm btn-view-user"
                                        data-user-id="{{ $user->id }}"
                                        title="View Details" data-bs-toggle="tooltip">
                                    <i class="bi bi-eye"></i>
                                </button>
                                @if($user->id !== auth()->id())
                                    @if($user->is_suspended ?? false)
                                        <button type="button" class="btn btn-outline-success btn-sm btn-activate-user"
                                                data-user-id="{{ $user->id }}"
                                                title="Activate User" data-bs-toggle="tooltip">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                    @else
                                        <button type="button" class="btn btn-outline-warning btn-sm btn-suspend-user"
                                                data-user-id="{{ $user->id }}"
                                                title="Suspend User" data-bs-toggle="tooltip">
                                            <i class="bi bi-pause-circle"></i>
                                        </button>
                                    @endif
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="card-footer bg-white border-top">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-block d-sm-none">
                @if($users->hasPages())
                    <div class="d-flex gap-2">
                        @if($users->onFirstPage())
                            <span class="btn btn-outline-secondary btn-sm disabled">Previous</span>
                        @else
                            <button class="btn btn-outline-primary btn-sm" onclick="loadManagementPage({{ $users->currentPage() - 1 }})">Previous</button>
                        @endif

                        @if($users->hasMorePages())
                            <button class="btn btn-outline-primary btn-sm" onclick="loadManagementPage({{ $users->currentPage() + 1 }})">Next</button>
                        @else
                            <span class="btn btn-outline-secondary btn-sm disabled">Next</span>
                        @endif
                    </div>
                @endif
            </div>
            <div class="d-none d-sm-flex justify-content-between align-items-center w-100">
                <div>
                    <small class="text-muted">
                        @if($users->total() > 0)
                            Showing <strong>{{ $users->firstItem() }}</strong> to <strong>{{ $users->lastItem() }}</strong> of <strong>{{ $users->total() }}</strong> results
                        @else
                            No results found
                        @endif
                    </small>
                </div>
                <div>
                    @if($users->hasPages())
                        <nav aria-label="Page navigation">
                            <ul class="pagination pagination-sm mb-0">
                                @if($users->onFirstPage())
                                    <li class="page-item disabled"><span class="page-link">Previous</span></li>
                                @else
                                    <li class="page-item"><button class="page-link" onclick="loadManagementPage({{ $users->currentPage() - 1 }})">Previous</button></li>
                                @endif

                                @foreach($users->getUrlRange(1, $users->lastPage()) as $page => $url)
                                    @if($page == $users->currentPage())
                                        <li class="page-item active"><span class="page-link">{{ $page }}</span></li>
                                    @else
                                        <li class="page-item"><button class="page-link" onclick="loadManagementPage({{ $page }})">{{ $page }}</button></li>
                                    @endif
                                @endforeach

                                @if($users->hasMorePages())
                                    <li class="page-item"><button class="page-link" onclick="loadManagementPage({{ $users->currentPage() + 1 }})">Next</button></li>
                                @else
                                    <li class="page-item disabled"><span class="page-link">Next</span></li>
                                @endif
                            </ul>
                        </nav>
                    @endif
                </div>
            </div>
        </div>
    </div>
@else
    <!-- Empty State -->
    <div class="card-body text-center py-5">
        <i class="bi bi-shield-check display-1 text-muted mb-3"></i>
        <h5 class="fw-bold">No management users found</h5>
        <p class="text-muted mb-4">No management users match your current filters.</p>
        <button type="button" class="btn btn-outline-secondary" onclick="clearManagementFilters()">
            <i class="bi bi-arrow-clockwise me-2"></i>Clear Filters
        </button>
    </div>
@endif

<script>
function loadManagementPage(page) {
    const currentFilters = {
        search: document.querySelector('input[name="search"]')?.value || '',
        role: document.querySelector('select[name="role"]')?.value || '',
        sort_by: document.querySelector('input[name="sort_by"]')?.value || 'created_at',
        sort_order: document.querySelector('input[name="sort_order"]')?.value || 'desc',
        page: page
    };
    
    if (window.adminPanel) {
        window.adminPanel.refreshTableData('management-users-table', currentFilters);
    }
}

function sortManagementTable(column) {
    const sortByInput = document.querySelector('input[name="sort_by"]');
    const sortOrderInput = document.querySelector('input[name="sort_order"]');
    
    // If clicking the same column, toggle order
    if (sortByInput.value === column) {
        sortOrderInput.value = sortOrderInput.value === 'asc' ? 'desc' : 'asc';
    } else {
        // New column, default to ascending
        sortByInput.value = column;
        sortOrderInput.value = 'asc';
    }
    
    if (window.adminPanel) {
        window.adminPanel.refreshTableData('management-users-table');
    }
}

function clearManagementFilters() {
    document.querySelector('input[name="search"]').value = '';
    document.querySelector('select[name="role"]').value = '';
    
    if (window.adminPanel) {
        window.adminPanel.refreshTableData('management-users-table');
    }
}
</script>
