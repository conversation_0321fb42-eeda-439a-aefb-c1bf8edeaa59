@extends('layouts.admin')

@section('page-title', 'Notification Management')

@section('page-description', 'Send notifications to users and manage notification history.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Notifications</span>
    </li>
@endsection

@section('content')
    <!-- Send New Notification Form -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white border-bottom">
            <h5 class="card-title mb-0 fw-bold">
                <i class="bi bi-send me-2 text-primary"></i>Send New Notification
            </h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.notifications.store') }}" method="POST" data-notification-form data-ajax>
                @csrf

                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="user_id" class="form-label fw-medium">User</label>
                        <div class="position-relative">
                            <input type="text" id="user_search" class="form-control @error('user_id') is-invalid @enderror"
                                   placeholder="Search users or select 'All Users'..." autocomplete="off">
                            <input type="hidden" id="user_id" name="user_id" value="{{ old('user_id') }}">
                            <div id="user_dropdown" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none;">
                                <div class="dropdown-item" data-value="all">
                                    <i class="bi bi-people-fill me-2 text-primary"></i>
                                    <strong>All Users</strong>
                                    <small class="text-muted d-block">Send notification to all registered users</small>
                                </div>
                                <div class="dropdown-divider"></div>
                                <div id="user_list">
                                    <!-- Users will be loaded here via AJAX -->
                                </div>
                            </div>
                        </div>
                        @error('user_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="type" class="form-label fw-medium">Type</label>
                        <select id="type" name="type" class="form-select @error('type') is-invalid @enderror">
                            <option value="general" {{ old('type') == 'general' ? 'selected' : '' }}>General</option>
                            <option value="payment" {{ old('type') == 'payment' ? 'selected' : '' }}>Payment</option>
                            <option value="match" {{ old('type') == 'match' ? 'selected' : '' }}>Match</option>
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <label for="title" class="form-label fw-medium">Title</label>
                        <input type="text" id="title" name="title" class="form-control @error('title') is-invalid @enderror"
                               value="{{ old('title') }}" placeholder="Enter notification title">
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <label for="body" class="form-label fw-medium">Message</label>
                        <textarea id="body" name="body" rows="4" class="form-control @error('body') is-invalid @enderror"
                                  placeholder="Enter notification message">{{ old('body') }}</textarea>
                        @error('body')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-12">
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send me-2"></i>Send Notification
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Sent Notifications -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <h5 class="card-title mb-0 fw-bold">
                <i class="bi bi-bell me-2 text-primary"></i>Sent Notifications (<span id="notifications-count">{{ $notifications->total() }}</span>)
            </h5>
        </div>

        <div id="notifications-table">
            @include('admin.notifications.table', ['notifications' => $notifications])
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSearch = document.getElementById('user_search');
    const userDropdown = document.getElementById('user_dropdown');
    const userList = document.getElementById('user_list');
    const userIdInput = document.getElementById('user_id');
    let searchTimeout;

    // Show dropdown when input is focused
    userSearch.addEventListener('focus', function() {
        userDropdown.style.display = 'block';
        if (userList.children.length === 0) {
            loadUsers('');
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.position-relative')) {
            userDropdown.style.display = 'none';
        }
    });

    // Handle search input
    userSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        searchTimeout = setTimeout(() => {
            loadUsers(query);
        }, 300);
    });

    // Handle dropdown item clicks
    userDropdown.addEventListener('click', function(e) {
        const item = e.target.closest('.dropdown-item');
        if (item) {
            e.preventDefault();
            const value = item.getAttribute('data-value');
            const text = item.textContent.trim();

            userIdInput.value = value;
            userSearch.value = value === 'all' ? 'All Users' : text;
            userDropdown.style.display = 'none';
        }
    });

    // Load users via AJAX
    function loadUsers(query) {
        fetch(`/admin/api/users/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                userList.innerHTML = '';

                if (data.users && data.users.length > 0) {
                    data.users.forEach(user => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.setAttribute('data-value', user.id);
                        item.innerHTML = `
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-circle me-2 text-secondary"></i>
                                <div>
                                    <div class="fw-medium">${user.name}</div>
                                    <small class="text-muted">${user.email}</small>
                                </div>
                            </div>
                        `;
                        userList.appendChild(item);
                    });
                } else {
                    const noResults = document.createElement('div');
                    noResults.className = 'dropdown-item-text text-muted';
                    noResults.textContent = 'No users found';
                    userList.appendChild(noResults);
                }
            })
            .catch(error => {
                console.error('Error loading users:', error);
                userList.innerHTML = '<div class="dropdown-item-text text-danger">Error loading users</div>';
            });
    }

    // Set initial value if there's an old value
    const oldValue = userIdInput.value;
    if (oldValue) {
        if (oldValue === 'all') {
            userSearch.value = 'All Users';
        } else {
            // Find user name from the old value (you might need to make an API call here)
            userSearch.value = 'Loading...';
            fetch(`/admin/api/users/${oldValue}`)
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        userSearch.value = `${data.user.name} (${data.user.email})`;
                    }
                })
                .catch(() => {
                    userSearch.value = '';
                    userIdInput.value = '';
                });
        }
    }
});
</script>
@endpush
