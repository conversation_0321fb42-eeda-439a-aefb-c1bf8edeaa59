@if ($notifications->count() > 0)
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th scope="col">Title</th>
                    <th scope="col">Message</th>
                    <th scope="col">Type</th>
                    <th scope="col">User</th>
                    <th scope="col">Sent At</th>
                    <th scope="col" style="width: 100px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($notifications as $notification)
                    <tr>
                        <td>
                            <div class="fw-medium">{{ $notification->title }}</div>
                        </td>
                        <td>
                            <div class="text-muted">{{ Str::limit($notification->body, 50) }}</div>
                        </td>
                        <td>
                            <span class="badge
                                @if ($notification->type === 'general') bg-primary-subtle text-primary
                                @elseif ($notification->type === 'payment') bg-success-subtle text-success
                                @elseif ($notification->type === 'match') bg-warning-subtle text-warning
                                @endif">
                                <i class="bi
                                    @if ($notification->type === 'general') bi-info-circle
                                    @elseif ($notification->type === 'payment') bi-credit-card
                                    @elseif ($notification->type === 'match') bi-heart
                                    @endif me-1"></i>
                                {{ ucfirst($notification->type) }}
                            </span>
                        </td>
                        <td>
                            @if($notification->sent_to_all)
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle me-2 d-flex align-items-center justify-content-center"
                                         style="width: 32px; height: 32px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                        <i class="bi bi-people-fill text-white" style="font-size: 0.75rem;"></i>
                                    </div>
                                    <div>
                                        <div class="fw-medium">All Users</div>
                                        <small class="text-muted">{{ $notification->total_recipients }} recipients</small>
                                    </div>
                                </div>
                            @else
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle me-2 d-flex align-items-center justify-content-center"
                                         style="width: 32px; height: 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                        <span class="text-white fw-semibold" style="font-size: 0.75rem;">{{ substr($notification->user->name, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <div class="fw-medium">{{ $notification->user->name }}</div>
                                        <small class="text-muted">{{ $notification->user->email }}</small>
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td>
                            <div>{{ $notification->sent_at ? $notification->sent_at->format('M d, Y') : 'Not sent' }}</div>
                            @if($notification->sent_at)
                                <small class="text-muted">{{ $notification->sent_at->format('H:i') }}</small>
                            @endif
                        </td>
                        <td>
                            <button type="button" 
                                    class="btn btn-outline-danger btn-sm btn-delete-notification"
                                    data-notification-id="{{ $notification->id }}"
                                    title="Delete Notification" data-bs-toggle="tooltip">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="card-footer bg-white border-top">
        <div class="d-flex justify-content-center">
            @if($notifications->hasPages())
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        @if($notifications->onFirstPage())
                            <li class="page-item disabled"><span class="page-link">Previous</span></li>
                        @else
                            <li class="page-item"><button class="page-link" onclick="loadNotificationsPage({{ $notifications->currentPage() - 1 }})">Previous</button></li>
                        @endif

                        @foreach($notifications->getUrlRange(1, $notifications->lastPage()) as $page => $url)
                            @if($page == $notifications->currentPage())
                                <li class="page-item active"><span class="page-link">{{ $page }}</span></li>
                            @else
                                <li class="page-item"><button class="page-link" onclick="loadNotificationsPage({{ $page }})">{{ $page }}</button></li>
                            @endif
                        @endforeach

                        @if($notifications->hasMorePages())
                            <li class="page-item"><button class="page-link" onclick="loadNotificationsPage({{ $notifications->currentPage() + 1 }})">Next</button></li>
                        @else
                            <li class="page-item disabled"><span class="page-link">Next</span></li>
                        @endif
                    </ul>
                </nav>
            @endif
        </div>
    </div>
@else
    <!-- Empty State -->
    <div class="card-body text-center py-5">
        <i class="bi bi-bell-slash display-1 text-muted mb-3"></i>
        <h5 class="fw-bold">No notifications sent yet</h5>
        <p class="text-muted mb-0">Start by sending your first notification to users.</p>
    </div>
@endif

<script>
function loadNotificationsPage(page) {
    const currentFilters = {
        search: document.querySelector('input[name="search"]')?.value || '',
        type: document.querySelector('select[name="type"]')?.value || '',
        sort_by: document.querySelector('input[name="sort_by"]')?.value || 'created_at',
        sort_order: document.querySelector('input[name="sort_order"]')?.value || 'desc',
        page: page
    };
    
    if (window.adminPanel) {
        window.adminPanel.refreshTableData('notifications-table', currentFilters);
    }
}
</script>
