@extends('layouts.admin')

@section('title', 'Data Cleanup')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🧹 Data Cleanup Tool</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> Warning</h5>
                        <p>This tool will permanently delete user data from the database while preserving:</p>
                        <ul>
                            <li>✅ Admin Users and Accounts</li>
                            <li>✅ Subscription Plans and Features</li>
                            <li>✅ System Settings</li>
                            <li>✅ Admin Configuration</li>
                        </ul>
                        <p><strong>The following data will be DELETED:</strong></p>
                        <ul>
                            <li>❌ Regular user accounts and profiles (Admin users preserved)</li>
                            <li>❌ All bookings and transactions</li>
                            <li>❌ All chat messages and notifications</li>
                            <li>❌ All uploaded files (profile pictures, gallery images, etc.)</li>
                            <li>❌ All session and cache data</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h5>📋 What will be preserved:</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> Admin Users</li>
                                        <li><i class="fas fa-check text-success"></i> Subscription Plans</li>
                                        <li><i class="fas fa-check text-success"></i> Feature Settings</li>
                                        <li><i class="fas fa-check text-success"></i> System Settings</li>
                                        <li><i class="fas fa-check text-success"></i> Meeting Addresses</li>
                                        <li><i class="fas fa-check text-success"></i> Admin Configuration</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h5>🗑️ What will be deleted:</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-times text-danger"></i> Regular User Accounts</li>
                                        <li><i class="fas fa-times text-danger"></i> User Subscriptions</li>
                                        <li><i class="fas fa-times text-danger"></i> Bookings & Transactions</li>
                                        <li><i class="fas fa-times text-danger"></i> Chat Messages</li>
                                        <li><i class="fas fa-times text-danger"></i> Uploaded Files</li>
                                        <li><i class="fas fa-times text-danger"></i> Session Data</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmCleanup">
                            <label class="form-check-label" for="confirmCleanup">
                                <strong>I understand that this action cannot be undone and will delete all user data</strong>
                            </label>
                        </div>

                        <button type="button" class="btn btn-danger btn-lg" id="executeCleanup" disabled>
                            <i class="fas fa-trash-alt"></i> Execute Data Cleanup
                        </button>
                    </div>

                    <div id="cleanupProgress" class="mt-4" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin"></i> Cleaning up data... Please wait.
                        </div>
                    </div>

                    <div id="cleanupResult" class="mt-4" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmCleanup');
    const executeButton = document.getElementById('executeCleanup');
    const progressDiv = document.getElementById('cleanupProgress');
    const resultDiv = document.getElementById('cleanupResult');

    // Enable/disable button based on checkbox
    confirmCheckbox.addEventListener('change', function() {
        executeButton.disabled = !this.checked;
    });

    // Execute cleanup
    executeButton.addEventListener('click', function() {
        if (!confirm('Are you absolutely sure you want to delete all user data? This action cannot be undone!')) {
            return;
        }

        // Show progress
        progressDiv.style.display = 'block';
        resultDiv.style.display = 'none';
        executeButton.disabled = true;

        // Execute cleanup
        fetch('{{ route("admin.data.cleanup.execute") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            progressDiv.style.display = 'none';
            
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> Success!</h5>
                        <p>${data.message}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-circle"></i> Error!</h5>
                        <p>${data.message}</p>
                    </div>
                `;
            }
            
            resultDiv.style.display = 'block';
            confirmCheckbox.checked = false;
            executeButton.disabled = true;
        })
        .catch(error => {
            progressDiv.style.display = 'none';
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-circle"></i> Error!</h5>
                    <p>An unexpected error occurred: ${error.message}</p>
                </div>
            `;
            resultDiv.style.display = 'block';
            confirmCheckbox.checked = false;
            executeButton.disabled = true;
        });
    });
});
</script>
@endsection
