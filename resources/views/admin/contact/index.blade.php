@extends('layouts.admin')

@section('title', 'Contact Submissions')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Contact Submissions</h1>
            <p class="mb-0 text-muted">Manage and respond to user contact form submissions</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="stat-total">{{ $stats['total'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-envelope-fill fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="stat-pending">{{ $stats['pending'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-fill fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">In Progress</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="stat-in-progress">{{ $stats['in_progress'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-gear-fill fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Solved</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="stat-solved">{{ $stats['solved'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle-fill fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Follow Up</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="stat-follow-up">{{ $stats['follow_up'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-arrow-repeat fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Closed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="stat-closed">{{ $stats['closed'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-x-circle-fill fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters & Search</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="statusFilter" class="form-label">Status</label>
                    <select id="statusFilter" class="form-select">
                        <option value="all">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="solved">Solved</option>
                        <option value="follow_up">Follow Up</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="priorityFilter" class="form-label">Priority</label>
                    <select id="priorityFilter" class="form-select">
                        <option value="all">All Priorities</option>
                        <option value="urgent">Urgent</option>
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="searchInput" class="form-label">Search</label>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search by name, email, subject, or message...">
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="bi bi-x-circle"></i> Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Submissions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Contact Submissions</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="contactTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Subject</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Assigned To</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="contactTableBody">
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    <span id="pagination-info">Showing 0 to 0 of 0 entries</span>
                </div>
                <nav aria-label="Contact submissions pagination">
                    <ul class="pagination mb-0" id="pagination-controls">
                        <!-- Pagination will be generated via JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Contact Details Modal -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactModalLabel">Contact Submission Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contactModalBody">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
    let currentPage = 1;
    let currentFilters = {
        status: 'all',
        priority: 'all',
        search: ''
    };

    document.addEventListener('DOMContentLoaded', function() {
        loadContactSubmissions();
        
        // Filter event listeners
        document.getElementById('statusFilter').addEventListener('change', function() {
            currentFilters.status = this.value;
            currentPage = 1;
            loadContactSubmissions();
        });
        
        document.getElementById('priorityFilter').addEventListener('change', function() {
            currentFilters.priority = this.value;
            currentPage = 1;
            loadContactSubmissions();
        });
        
        // Search with debounce
        let searchTimeout;
        document.getElementById('searchInput').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentFilters.search = this.value;
                currentPage = 1;
                loadContactSubmissions();
            }, 500);
        });
    });

    function loadContactSubmissions() {
        const params = new URLSearchParams({
            page: currentPage,
            status: currentFilters.status,
            priority: currentFilters.priority,
            search: currentFilters.search,
            per_page: 15
        });

        fetch(`/admin/contact/data?${params}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            renderContactTable(data.data);
            renderPagination(data.pagination);
        })
        .catch(error => {
            console.error('Error loading contact submissions:', error);
        });
    }

    function renderContactTable(submissions) {
        const tbody = document.getElementById('contactTableBody');
        
        if (submissions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-inbox fa-3x mb-3"></i>
                            <p>No contact submissions found</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = submissions.map(submission => `
            <tr>
                <td>${submission.id}</td>
                <td>${submission.first_name} ${submission.last_name}</td>
                <td>${submission.email}</td>
                <td>${submission.subject}</td>
                <td><span class="badge bg-${getStatusBadgeColor(submission.status)}">${getFormattedStatus(submission.status)}</span></td>
                <td><span class="badge bg-${getPriorityBadgeColor(submission.priority)}">${getFormattedPriority(submission.priority)}</span></td>
                <td>${submission.assigned_admin ? submission.assigned_admin.name : '<span class="text-muted">Unassigned</span>'}</td>
                <td>${formatDate(submission.created_at)}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewContact(${submission.id})" title="View Details">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteContact(${submission.id})" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    function renderPagination(pagination) {
        const info = document.getElementById('pagination-info');
        const controls = document.getElementById('pagination-controls');
        
        info.textContent = `Showing ${pagination.from || 0} to ${pagination.to || 0} of ${pagination.total} entries`;
        
        if (pagination.last_page <= 1) {
            controls.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        if (pagination.current_page > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.current_page - 1})">Previous</a></li>`;
        }
        
        // Page numbers
        for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.last_page, pagination.current_page + 2); i++) {
            paginationHTML += `<li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>`;
        }
        
        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.current_page + 1})">Next</a></li>`;
        }
        
        controls.innerHTML = paginationHTML;
    }

    function changePage(page) {
        currentPage = page;
        loadContactSubmissions();
    }

    function clearFilters() {
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('priorityFilter').value = 'all';
        document.getElementById('searchInput').value = '';
        
        currentFilters = {
            status: 'all',
            priority: 'all',
            search: ''
        };
        currentPage = 1;
        loadContactSubmissions();
    }

    function refreshData() {
        loadContactSubmissions();
        loadStats();
    }

    function loadStats() {
        fetch('/admin/contact/stats', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('stat-total').textContent = data.stats.total;
            document.getElementById('stat-pending').textContent = data.stats.pending;
            document.getElementById('stat-in-progress').textContent = data.stats.in_progress;
            document.getElementById('stat-solved').textContent = data.stats.solved;
            document.getElementById('stat-follow-up').textContent = data.stats.follow_up;
            document.getElementById('stat-closed').textContent = data.stats.closed;
        })
        .catch(error => {
            // Handle error silently
        });
    }

    function viewContact(id) {
        fetch(`/admin/contact/${id}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderContactModal(data.submission);
                new bootstrap.Modal(document.getElementById('contactModal')).show();
            } else {
                alert('Error loading contact details');
            }
        })
        .catch(error => {
            alert('Error loading contact details');
        });
    }

    function renderContactModal(submission) {
        const modalBody = document.getElementById('contactModalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Contact Information</h6>
                    <p><strong>Name:</strong> ${submission.first_name} ${submission.last_name}</p>
                    <p><strong>Email:</strong> ${submission.email}</p>
                    <p><strong>Subject:</strong> ${submission.subject}</p>
                    <p><strong>Submitted:</strong> ${formatDate(submission.created_at)}</p>
                </div>
                <div class="col-md-6">
                    <h6>Status Information</h6>
                    <p><strong>Status:</strong> <span class="badge bg-${getStatusBadgeColor(submission.status)}">${getFormattedStatus(submission.status)}</span></p>
                    <p><strong>Priority:</strong> <span class="badge bg-${getPriorityBadgeColor(submission.priority)}">${getFormattedPriority(submission.priority)}</span></p>
                    <p><strong>Assigned To:</strong> ${submission.assigned_admin ? submission.assigned_admin.name : 'Unassigned'}</p>
                    <p><strong>Responded:</strong> ${submission.responded_at ? formatDate(submission.responded_at) : 'Not yet'}</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Message</h6>
                    <div class="border rounded p-3 bg-light">
                        ${submission.message.replace(/\n/g, '<br>')}
                    </div>
                </div>
            </div>
            ${submission.admin_notes ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Admin Notes</h6>
                    <div class="border rounded p-3 bg-light">
                        ${submission.admin_notes.replace(/\n/g, '<br>')}
                    </div>
                </div>
            </div>
            ` : ''}
            <div class="row mt-4">
                <div class="col-12">
                    <h6>Update Status</h6>
                    <form id="updateContactForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="updateStatus" class="form-label">Status</label>
                                <select id="updateStatus" class="form-select" name="status">
                                    <option value="pending" ${submission.status === 'pending' ? 'selected' : ''}>Pending</option>
                                    <option value="in_progress" ${submission.status === 'in_progress' ? 'selected' : ''}>In Progress</option>
                                    <option value="solved" ${submission.status === 'solved' ? 'selected' : ''}>Solved</option>
                                    <option value="follow_up" ${submission.status === 'follow_up' ? 'selected' : ''}>Follow Up</option>
                                    <option value="closed" ${submission.status === 'closed' ? 'selected' : ''}>Closed</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="updatePriority" class="form-label">Priority</label>
                                <select id="updatePriority" class="form-select" name="priority">
                                    <option value="low" ${submission.priority === 'low' ? 'selected' : ''}>Low</option>
                                    <option value="medium" ${submission.priority === 'medium' ? 'selected' : ''}>Medium</option>
                                    <option value="high" ${submission.priority === 'high' ? 'selected' : ''}>High</option>
                                    <option value="urgent" ${submission.priority === 'urgent' ? 'selected' : ''}>Urgent</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="updateAssigned" class="form-label">Assign To</label>
                                <select id="updateAssigned" class="form-select" name="assigned_to">
                                    <option value="">Unassigned</option>
                                    ${getAdminUsersOptions(submission.assigned_to)}
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label for="updateNotes" class="form-label">Admin Notes</label>
                                <textarea id="updateNotes" class="form-control" name="admin_notes" rows="3" placeholder="Add notes about this submission...">${submission.admin_notes || ''}</textarea>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary" onclick="updateContact(${submission.id})">Update</button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    function updateContact(id) {
        const form = document.getElementById('updateContactForm');
        const formData = new FormData(form);

        // Convert FormData to regular object
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        console.log('Sending update data:', data);

        fetch(`/admin/contact/${id}`, {
            method: 'PUT',
            body: JSON.stringify(data),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('contactModal')).hide();
                loadContactSubmissions();
                loadStats();
            } else {
                if (data.errors) {
                    let errorMessage = 'Validation errors:\n';
                    Object.keys(data.errors).forEach(field => {
                        errorMessage += `${field}: ${data.errors[field].join(', ')}\n`;
                    });
                    alert(errorMessage);
                } else {
                    alert(data.message || 'Error updating contact submission');
                }
            }
        })
        .catch(error => {
            console.error('Error updating contact:', error);
            alert('Error updating contact submission: ' + error.message);
        });
    }

    function deleteContact(id) {
        if (!confirm('Are you sure you want to delete this contact submission?')) {
            return;
        }

        const formData = new FormData();
        formData.append('_method', 'DELETE');

        fetch(`/admin/contact/${id}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadContactSubmissions();
                loadStats();
            } else {
                alert(data.message || 'Error deleting contact submission');
            }
        })
        .catch(error => {
            console.error('Error deleting contact:', error);
            alert('Error deleting contact submission');
        });
    }

    // Helper functions
    function getAdminUsersOptions(selectedId) {
        const adminUsers = @json($adminUsers);
        return adminUsers.map(admin =>
            `<option value="${admin.id}" ${selectedId == admin.id ? 'selected' : ''}>${admin.name}</option>`
        ).join('');
    }

    function getStatusBadgeColor(status) {
        const colors = {
            'pending': 'warning',
            'in_progress': 'info',
            'solved': 'success',
            'follow_up': 'primary',
            'closed': 'secondary'
        };
        return colors[status] || 'secondary';
    }

    function getPriorityBadgeColor(priority) {
        const colors = {
            'low': 'success',
            'medium': 'info',
            'high': 'warning',
            'urgent': 'danger'
        };
        return colors[priority] || 'info';
    }

    function getFormattedStatus(status) {
        const statuses = {
            'pending': 'Pending',
            'in_progress': 'In Progress',
            'solved': 'Solved',
            'follow_up': 'Follow Up',
            'closed': 'Closed'
        };
        return statuses[status] || status;
    }

    function getFormattedPriority(priority) {
        return priority.charAt(0).toUpperCase() + priority.slice(1);
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
</script>
@endpush
