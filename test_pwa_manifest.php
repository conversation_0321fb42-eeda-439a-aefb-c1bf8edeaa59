<?php
/**
 * Test script to verify PWA manifest functionality
 * Run this to check if uploaded mobile icon and theme colors are working
 */

// Simulate Laravel environment for testing
require_once 'vendor/autoload.php';

// Create a simple test to check manifest.json endpoint
echo "Testing PWA Manifest Configuration...\n\n";

// Test 1: Check if manifest.json is accessible
echo "1. Testing manifest.json endpoint:\n";
$manifestUrl = 'http://localhost:8000/manifest.json';
$context = stream_context_create([
    'http' => [
        'timeout' => 10
    ]
]);

try {
    $manifestContent = file_get_contents($manifestUrl, false, $context);
    if ($manifestContent) {
        $manifest = json_decode($manifestContent, true);
        
        echo "✅ Manifest accessible\n";
        echo "   App Name: " . ($manifest['name'] ?? 'Not set') . "\n";
        echo "   Theme Color: " . ($manifest['theme_color'] ?? 'Not set') . "\n";
        echo "   Background Color: " . ($manifest['background_color'] ?? 'Not set') . "\n";
        echo "   Icons Count: " . count($manifest['icons'] ?? []) . "\n";
        
        // Check if icons are using uploaded mobile icon or default
        if (!empty($manifest['icons'])) {
            $firstIcon = $manifest['icons'][0];
            if (strpos($firstIcon['src'], 'storage/') !== false) {
                echo "   ✅ Using uploaded mobile icon\n";
            } else {
                echo "   ⚠️  Using default static icons\n";
            }
        }
        
    } else {
        echo "❌ Manifest not accessible\n";
    }
} catch (Exception $e) {
    echo "❌ Error accessing manifest: " . $e->getMessage() . "\n";
}

echo "\n2. PWA Features to verify manually:\n";
echo "   - Install app on mobile device\n";
echo "   - Check if custom icon appears\n";
echo "   - Check if theme color shows in status bar\n";
echo "   - Verify splash screen background color\n";

echo "\n3. Admin Settings to check:\n";
echo "   - Go to Admin > Settings > General tab\n";
echo "   - Upload a mobile icon (512x512px recommended)\n";
echo "   - Set theme color and background color\n";
echo "   - Save settings and test PWA again\n";

echo "\nTest completed!\n";
