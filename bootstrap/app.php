<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware
        $middleware->append([
            \App\Http\Middleware\CustomAdminUrlMiddleware::class,
            \App\Http\Middleware\SiteModeMiddleware::class,
            \App\Http\Middleware\SecurityHeaders::class,
            \App\Http\Middleware\SecurityLogger::class,
            \App\Http\Middleware\SEOMiddleware::class,
            \App\Http\Middleware\PerformanceOptimization::class,
        ]);

        // Middleware aliases
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'profile.complete' => \App\Http\Middleware\EnsureProfileComplete::class,
            'chat.access' => \App\Http\Middleware\ValidateChatAccess::class,
            'subscription.required' => \App\Http\Middleware\RequireActiveSubscription::class,
            'rate.limit' => \App\Http\Middleware\RateLimitMiddleware::class,
            'custom.admin.url' => \App\Http\Middleware\CustomAdminUrlMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Production exception handling - suppress debug output
        if (config('app.env') === 'production') {
            $exceptions->dontReport([
                \Illuminate\Http\Exceptions\ThrottleRequestsException::class,
                \Illuminate\Session\TokenMismatchException::class,
                \Symfony\Component\HttpKernel\Exception\NotFoundHttpException::class,
            ]);
        }
    })->create();
