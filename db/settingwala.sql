-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jun 20, 2025 at 03:30 PM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `settingwala`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_revenues`
--

CREATE TABLE `admin_revenues` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED DEFAULT NULL,
  `subscription_id` bigint(20) UNSIGNED DEFAULT NULL,
  `platform_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `commission_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_admin_earnings` decimal(10,2) NOT NULL DEFAULT 0.00,
  `revenue_type` varchar(255) NOT NULL DEFAULT 'booking_payment',
  `description` text DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `blocked_users`
--

CREATE TABLE `blocked_users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `blocker_id` bigint(20) UNSIGNED NOT NULL,
  `blocked_id` bigint(20) UNSIGNED NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `booking_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `chat_messages`
--

CREATE TABLE `chat_messages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED NOT NULL,
  `sender_id` bigint(20) UNSIGNED NOT NULL,
  `receiver_id` bigint(20) UNSIGNED NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contact_submissions`
--

CREATE TABLE `contact_submissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('pending','in_progress','solved','follow_up','closed') NOT NULL DEFAULT 'pending',
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `admin_notes` text DEFAULT NULL,
  `assigned_to` bigint(20) UNSIGNED DEFAULT NULL,
  `responded_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `couple_activity_requests`
--

CREATE TABLE `couple_activity_requests` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `requester_id` bigint(20) UNSIGNED NOT NULL,
  `partner_id` bigint(20) UNSIGNED NOT NULL,
  `type` enum('own_partner','couple_swap') NOT NULL DEFAULT 'own_partner',
  `status` enum('pending','approved','rejected','blocked') DEFAULT 'pending',
  `message` text DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `responded_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `event_payments`
--

CREATE TABLE `event_payments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `meeting_address_id` bigint(20) UNSIGNED NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `payment_method` varchar(255) NOT NULL DEFAULT 'razorpay',
  `razorpay_payment_id` varchar(255) DEFAULT NULL,
  `razorpay_order_id` varchar(255) DEFAULT NULL,
  `razorpay_signature` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'completed',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `features`
--

CREATE TABLE `features` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `label` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `is_hidden` tinyint(1) NOT NULL DEFAULT 0,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`options`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `features`
--

INSERT INTO `features` (`id`, `name`, `label`, `description`, `is_enabled`, `is_hidden`, `options`, `created_at`, `updated_at`) VALUES
(72, 'sugar_partner', 'Sugar Partner', 'Allow users to specify interest in sugar partner relationships', 0, 0, '{\"sugar_daddy\":\"Sugar Daddy\",\"sugar_mommy\":\"Sugar Mommy\",\"sugar_companion_female\":\"Sugerbabe\",\"sugar_companion_male\":\"Sugerboy\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(73, 'subscription_model', 'Subscription Model', 'Enable subscription-based access to Time Spending service. When enabled, users must purchase subscriptions to use Time Spending features.', 1, 0, '{\"subscription_required\":\"Subscription Required for Time Spending\",\"profile_visibility\":\"Profile Visibility Based on Subscription\",\"auto_enable_features\":\"Auto-enable Related Features\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(74, 'time_spending', 'Time Spending', 'Allow users to set hourly rates for time spending services. When enabled, users can set their hourly rate in INR and offer time spending services.', 1, 0, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(75, 'gallery', 'Gallery', 'Allow users to upload and manage multiple images in their gallery. When enabled, users can upload, reorder, and manage their profile images.', 1, 0, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(76, 'partner_swapping', 'Couple Activity', 'Allow couples to participate in couple-friendly activities and events. When enabled, couples can join special activities designed for pairs.', 0, 0, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(77, 'meeting_events', 'Meeting Events', 'Allow users to join organized meeting events and activities. When enabled, users can view and participate in meeting events.', 1, 0, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(78, 'notifications', 'Notifications', 'Enable push notifications and in-app notifications for users. When enabled, users receive notifications for various activities.', 1, 0, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(79, 'user_verification', 'User Verification', 'Enable user verification system with profile verification badges. When enabled, users can get verified profiles.', 0, 1, '{\"photo_verification\":\"Photo Verification\",\"phone_verification\":\"Phone Verification\",\"email_verification\":\"Email Verification\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(80, 'premium_membership', 'Premium Membership', 'Enable premium membership features with enhanced privileges. When enabled, users can purchase premium memberships.', 0, 1, '{\"unlimited_likes\":\"Unlimited Likes\",\"advanced_filters\":\"Advanced Filters\",\"priority_support\":\"Priority Support\",\"ad_free_experience\":\"Ad-Free Experience\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(81, 'chat_system', 'Chat System', 'Enable real-time chat functionality between users. Chat is automatically enabled when Time Spending feature is active.', 1, 0, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(82, 'location_services', 'Location Services', 'Enable location-based features including location detection and matching. Essential for time spending and meeting events.', 1, 1, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(83, 'privacy_controls', 'Privacy Controls', 'Enable advanced privacy settings for user profiles. Allows users to control visibility of their information.', 1, 1, '{\"public_profile\":\"Public Profile Visibility\",\"contact_visibility\":\"Contact Number Visibility\",\"dob_visibility\":\"Date of Birth Visibility\",\"interests_visibility\":\"Interests Visibility\",\"expectations_visibility\":\"Expectations Visibility\",\"gallery_visibility\":\"Gallery Images Visibility\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(84, 'meeting_verification', 'Meeting Verification', 'Enable photo and location verification for meetings. Users must take photos with location data at meeting start and end.', 1, 0, '{\"require_location\":\"Require Location Data\",\"camera_only\":\"Camera Only (No Gallery)\",\"auto_duration_calc\":\"Auto Duration Calculation\",\"verification_notifications\":\"Verification Notifications\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(85, 'rating_review_system', 'Rating & Review System', 'Enable users to rate and review each other after completed meetings. Works when meeting verification is disabled.', 1, 0, '{\"allow_anonymous_reviews\":\"Allow Anonymous Reviews\",\"require_text_review\":\"Require Text Review\",\"admin_approval_required\":\"Admin Approval Required\",\"auto_review_prompts\":\"Automatic Review Prompts\"}', '2025-06-16 12:44:45', '2025-06-16 12:44:45');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `meeting_addresses`
--

CREATE TABLE `meeting_addresses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` longtext NOT NULL,
  `is_couple_event` tinyint(1) NOT NULL DEFAULT 0,
  `payment_amount_couple` decimal(10,2) NOT NULL DEFAULT 0.00,
  `rules_and_regulations` longtext DEFAULT NULL,
  `payment_amount_boys` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_amount_girls` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_event_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `event_date` datetime DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `meeting_addresses`
--

INSERT INTO `meeting_addresses` (`id`, `title`, `description`, `is_couple_event`, `payment_amount_couple`, `rules_and_regulations`, `payment_amount_boys`, `payment_amount_girls`, `is_event_enabled`, `event_date`, `location`, `latitude`, `longitude`, `created_at`, `updated_at`) VALUES
(1, 'Coffee Meet & Greet', 'Join us for a casual coffee meetup where singles can connect in a relaxed atmosphere. Perfect for making new friends and potential romantic connections.', 0, 0.00, '<ul><li>Be respectful to all participants</li><li>Arrive on time</li><li>No inappropriate behavior</li><li>Exchange contacts only with mutual consent</li></ul>', 299.00, 199.00, 1, '2025-06-21 18:00:00', 'Cafe Coffee Day, Connaught Place, New Delhi', 28.63150000, 77.21670000, '2025-06-14 12:32:43', '2025-06-14 12:32:43'),
(2, 'Speed Dating Night', 'Experience the excitement of speed dating! Meet multiple potential matches in one evening through structured 5-minute conversations.', 0, 0.00, '<ul><li>Dress code: Smart casual</li><li>No phones during dating rounds</li><li>Be punctual for all rounds</li><li>Respect the time limits</li><li>Have fun and be yourself!</li></ul>', 599.00, 399.00, 1, '2025-06-28 19:30:00', 'The Imperial Hotel, Janpath, New Delhi', 28.61390000, 77.20900000, '2025-06-14 12:32:43', '2025-06-14 12:32:43'),
(3, 'Outdoor Adventure Meetup', 'Join fellow adventure enthusiasts for a day of hiking and outdoor activities. Great way to meet like-minded people who love nature and adventure.', 0, 0.00, '<ul><li>Wear comfortable hiking shoes</li><li>Bring water and snacks</li><li>Follow safety guidelines</li><li>Stay with the group</li><li>Respect the environment</li></ul>', 399.00, 299.00, 1, '2025-07-05 09:00:00', 'Lodhi Gardens, New Delhi', 28.59180000, 77.22730000, '2025-06-14 12:32:43', '2025-06-14 12:32:43'),
(4, 'Cultural Evening & Dinner', 'An elegant evening featuring cultural performances, fine dining, and opportunities to connect with educated professionals in a sophisticated setting.', 0, 0.00, '<ul><li>Formal dress code required</li><li>RSVP mandatory</li><li>No outside food or drinks</li><li>Maintain decorum throughout the event</li><li>Photography allowed in designated areas only</li></ul>', 899.00, 699.00, 1, '2025-07-12 19:00:00', 'India Habitat Centre, Lodhi Road, New Delhi', 28.57060000, 77.22940000, '2025-06-14 12:32:43', '2025-06-14 12:32:43'),
(5, 'Game Night Social', 'Fun-filled evening with board games, card games, and interactive activities. Perfect for breaking the ice and getting to know each other in a playful environment.', 0, 0.00, '<ul><li>Participate actively in games</li><li>Be a good sport - win or lose gracefully</li><li>No cheating or unfair play</li><li>Help with setup and cleanup</li><li>Respect game rules and other players</li></ul>', 249.00, 199.00, 1, '2025-06-24 18:30:00', 'Select City Walk Mall, Saket, New Delhi', 28.52450000, 77.20660000, '2025-06-14 12:32:43', '2025-06-14 12:32:43');

-- --------------------------------------------------------

--
-- Table structure for table `meeting_verifications`
--

CREATE TABLE `meeting_verifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED NOT NULL,
  `client_start_photo` varchar(255) DEFAULT NULL,
  `provider_start_photo` varchar(255) DEFAULT NULL,
  `client_end_photo` varchar(255) DEFAULT NULL,
  `provider_end_photo` varchar(255) DEFAULT NULL,
  `client_start_time` timestamp NULL DEFAULT NULL,
  `provider_start_time` timestamp NULL DEFAULT NULL,
  `client_end_time` timestamp NULL DEFAULT NULL,
  `provider_end_time` timestamp NULL DEFAULT NULL,
  `meeting_start_time` timestamp NULL DEFAULT NULL,
  `meeting_end_time` timestamp NULL DEFAULT NULL,
  `calculated_duration_minutes` int(11) DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `client_start_latitude` decimal(10,8) DEFAULT NULL,
  `client_start_longitude` decimal(11,8) DEFAULT NULL,
  `client_start_address` varchar(255) DEFAULT NULL,
  `provider_start_latitude` decimal(10,8) DEFAULT NULL,
  `provider_start_longitude` decimal(11,8) DEFAULT NULL,
  `provider_start_address` varchar(255) DEFAULT NULL,
  `client_end_latitude` decimal(10,8) DEFAULT NULL,
  `client_end_longitude` decimal(11,8) DEFAULT NULL,
  `client_end_address` varchar(255) DEFAULT NULL,
  `provider_end_latitude` decimal(10,8) DEFAULT NULL,
  `provider_end_longitude` decimal(11,8) DEFAULT NULL,
  `provider_end_address` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_01_15_000000_rename_partner_swapping_to_couple_activity', 1),
(5, '2025_01_15_120000_add_site_mode_settings', 1),
(6, '2025_01_15_120000_create_subscription_plans_table', 1),
(7, '2025_01_15_130000_create_user_subscriptions_table', 1),
(8, '2025_01_15_140000_add_subscription_fields_to_users_table', 1),
(9, '2025_01_15_200000_add_constraints_to_subscription_plans_table', 1),
(10, '2025_01_16_120000_add_discount_pricing_to_subscription_plans', 1),
(11, '2025_01_20_120000_create_time_spending_bookings_table', 1),
(12, '2025_01_21_120000_create_meeting_verifications_table', 1),
(13, '2025_01_21_130000_create_ratings_reviews_table', 1),
(14, '2025_01_22_000000_add_escrow_fields_to_time_spending_bookings', 1),
(15, '2025_01_27_120000_create_admin_revenues_table', 1),
(16, '2025_01_27_130000_fix_duration_hours_column_type', 1),
(17, '2025_05_21_045510_add_google_id_and_profile_fields_to_users_table', 1),
(18, '2025_05_21_050718_create_meeting_addresses_table', 1),
(19, '2025_05_21_110724_create_settings_table', 1),
(20, '2025_05_21_110917_add_is_admin_to_users_table', 1),
(21, '2025_05_21_111457_create_uploads_table', 1),
(22, '2025_05_21_111804_create_notifications_table', 1),
(23, '2025_05_21_111954_add_fcm_token_to_users_table', 1),
(24, '2025_05_25_094922_add_profile_picture_to_users_table', 1),
(25, '2025_05_25_105910_create_event_payments_table', 1),
(26, '2025_05_25_115755_add_date_of_birth_to_users_table', 1),
(27, '2025_05_26_103643_add_expectation_to_users_table', 1),
(28, '2025_05_26_105902_create_user_change_logs_table', 1),
(29, '2025_05_26_120447_add_is_suspended_to_users_table', 1),
(30, '2025_05_27_070705_add_role_to_users_table', 1),
(31, '2025_05_27_110918_create_features_table', 1),
(32, '2025_05_27_111010_add_sugar_partner_fields_to_users_table', 1),
(33, '2025_05_27_113805_update_sugar_partner_type_to_json', 1),
(34, '2025_05_28_100953_create_user_gallery_table', 1),
(35, '2025_05_28_120000_add_privacy_settings_to_users_table', 1),
(36, '2025_05_29_120000_add_partner_swapping_to_users_table', 1),
(37, '2025_05_29_120001_create_partner_swapping_requests_table', 1),
(38, '2025_05_29_120003_add_blocked_status_to_partner_swapping_requests', 1),
(39, '2025_05_29_120004_fix_partner_swapping_status_constraint', 1),
(40, '2025_05_29_165617_add_availability_schedule_to_users_table', 1),
(41, '2025_05_29_171644_add_missing_availability_columns_to_users_table', 1),
(42, '2025_05_29_180734_add_location_to_time_spending_bookings_table', 1),
(43, '2025_05_29_add_payment_tracking_to_time_spending_bookings', 1),
(44, '2025_05_30_125436_add_wallet_payment_columns_to_time_spending_bookings_table', 1),
(45, '2025_05_30_add_booking_status_fields', 1),
(46, '2025_05_30_create_chat_messages_table', 1),
(47, '2025_05_30_create_user_wallets_table', 1),
(48, '2025_05_30_create_wallet_transactions_table', 1),
(49, '2025_05_30_update_notifications_table', 1),
(50, '2025_05_31_124513_add_is_hidden_to_features_table', 1),
(51, '2025_05_31_152453_add_sugar_partner_settings_to_users_table', 1),
(52, '2025_05_31_171502_create_blocked_users_table', 1),
(53, '2025_06_01_001904_add_location_to_meeting_addresses_table', 1),
(54, '2025_06_01_012117_add_refund_columns_to_time_spending_bookings_table', 1),
(55, '2025_06_04_191530_add_sent_to_all_to_notifications_table', 1),
(56, '2025_06_05_120000_add_platform_fee_to_time_spending_bookings_table', 1),
(57, '2025_06_07_090646_remove_subscription_model_feature', 1),
(58, '2025_06_07_132703_add_subscription_id_to_admin_revenues_table', 1),
(59, '2025_06_07_133624_make_booking_id_nullable_in_admin_revenues_table', 1),
(60, '2025_06_08_140844_add_is_couple_event_to_meeting_addresses_table', 1),
(61, '2025_06_08_151637_add_payment_amount_couple_to_meeting_addresses_table', 1),
(62, '2025_06_08_152538_add_is_couple_event_to_meeting_addresses_table', 1),
(63, '2025_06_09_120000_add_performance_indexes', 1),
(64, '2025_01_22_120000_create_contact_submissions_table', 2),
(65, '2025_06_15_120000_add_dispute_fields_to_time_spending_bookings', 3),
(66, '2025_06_15_130000_create_user_bank_accounts_table', 4),
(67, '2025_06_15_140000_create_withdrawal_requests_table', 4),
(69, '2025_06_15_150000_add_withdrawal_settings_to_site_settings', 5),
(70, '2025_06_16_112824_add_gpay_fields_to_user_bank_accounts_table', 6),
(71, '2025_06_16_114325_make_bank_fields_nullable_in_user_bank_accounts_table', 7),
(72, '2025_06_16_115902_update_account_type_enum_in_user_bank_accounts_table', 8);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `message` text DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'general',
  `sent_to_all` tinyint(1) NOT NULL DEFAULT 0,
  `total_recipients` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `fcm_token` varchar(255) DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ratings_reviews`
--

CREATE TABLE `ratings_reviews` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED NOT NULL,
  `reviewer_id` bigint(20) UNSIGNED NOT NULL,
  `reviewee_id` bigint(20) UNSIGNED NOT NULL,
  `rating` int(10) UNSIGNED NOT NULL COMMENT 'Rating from 1 to 5 stars',
  `review_text` text DEFAULT NULL COMMENT 'Optional text review',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether review is anonymous',
  `is_approved` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Admin approval status',
  `is_flagged` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Flagged for inappropriate content',
  `admin_notes` text DEFAULT NULL COMMENT 'Admin notes for moderation',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `key` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `group` varchar(255) NOT NULL DEFAULT 'general',
  `type` varchar(255) NOT NULL DEFAULT 'text',
  `label` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `group`, `type`, `label`, `description`, `created_at`, `updated_at`) VALUES
(167, 'copyright_text', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(168, 'facebook_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(169, 'twitter_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(170, 'instagram_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(171, 'linkedin_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(172, 'youtube_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(173, 'whatsapp_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(174, 'google_client_id', '16067955045-fki5p81e8f98gfcijtifnje6cu4djc47.apps.googleusercontent.com', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(175, 'google_client_secret', '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', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(176, 'google_redirect_uri', 'http://127.0.0.1:8000/auth/callback/google', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(177, 'razorpay_key_id', 'rzp_test_Go3jN8rdNmRJ7P', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(178, 'razorpay_key_secret', 'sbD3JVTl7W7UJ18O43cRmtCE', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(179, 'platform_fee', '27', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(180, 'commission_percentage', '20', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(181, 'firebase_project_id', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(182, 'firebase_client_email', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(183, 'firebase_private_key', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(184, 'maintenance_mode_end_time', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(185, 'maintenance_mode_message', 'We are currently under maintenance. The website will be back online approximately at {end_time}.', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(186, 'coming_soon_launch_time', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(187, 'coming_soon_message', 'We\'re launching soon! Our amazing platform will be available at {launch_time}.', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(188, 'custom_admin_url', NULL, 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(189, 'header_logo', 'logos/xuj0PQ0CJp2IvQUpql1lzz4aLBwkiDnv0XgMu5N2.png', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(190, 'footer_logo', 'logos/BWTs1luW0nBvm6sZQptgNQsMRpXRr0G8TgJ919aF.png', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(191, 'mobile_icon', 'logos/fmFU7P8uWFpaHdDw7SKk7yyvjcgoCJpCdhybx9Fw.png', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(192, 'favicon', 'logos/5GDm8IgpXZhgS7WSLwE8FzXUlJRblrMpK11SCHpm.png', 'general', 'text', NULL, NULL, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(193, 'minimum_withdrawal_amount', '1', 'withdrawal', 'number', 'Minimum Withdrawal Amount', 'Minimum amount required in wallet to request withdrawal', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(194, 'maximum_withdrawal_amount', '50000', 'withdrawal', 'number', 'Maximum Withdrawal Amount', 'Maximum amount that can be withdrawn per request', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(195, 'daily_withdrawal_limit', '100000', 'withdrawal', 'number', 'Daily Withdrawal Limit', 'Maximum amount that can be withdrawn per day', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(196, 'withdrawal_processing_fee', '0', 'withdrawal', 'number', 'Withdrawal Processing Fee', 'Processing fee for withdrawal requests (in rupees)', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(197, 'withdrawal_processing_time', '1-3 business days', 'withdrawal', 'text', 'Withdrawal Processing Time', 'Expected processing time for withdrawals', '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(198, 'auto_withdrawal_enabled', 'false', 'withdrawal', 'boolean', 'Auto Withdrawal Enabled', 'Enable automatic withdrawal processing via payment gateway', '2025-06-16 12:44:45', '2025-06-16 12:44:45');

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `duration_months` int(10) UNSIGNED NOT NULL,
  `amount` decimal(10,2) UNSIGNED NOT NULL,
  `original_price` decimal(10,2) NOT NULL,
  `discount_price` decimal(10,2) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`id`, `name`, `duration_months`, `amount`, `original_price`, `discount_price`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(41, '1 Month Premium', 1, 99.00, 99.00, NULL, 'Perfect for trying out our Time Spending Service. Get full access to all features for one month.', 1, 1, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(42, '3 Month Premium', 3, 297.00, 297.00, 199.00, 'Save 16% with our 3-month subscription. Great value for regular users.', 1, 2, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(43, '6 Month Premium', 6, 594.00, 594.00, 299.00, 'Save 33% with our 6-month subscription. Perfect for serious users who want long-term visibility.', 1, 3, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(44, '1 Year Premium', 12, 499.00, 1188.00, 499.00, 'Ultimate value plan! Get a full year of premium access with maximum 58% savings. Only ₹499 for the entire year!', 0, 4, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(45, '1 Month Premium', 1, 99.00, 99.00, NULL, 'Perfect for trying out our Time Spending Service. Get full access to all features for one month.', 1, 1, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(46, '3 Month Premium', 3, 297.00, 297.00, 199.00, 'Save 16% with our 3-month subscription. Great value for regular users.', 1, 2, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(47, '6 Month Premium', 6, 594.00, 594.00, 299.00, 'Save 33% with our 6-month subscription. Perfect for serious users who want long-term visibility.', 1, 3, '2025-06-16 12:44:45', '2025-06-16 12:44:45'),
(48, '1 Year Premium', 12, 499.00, 1188.00, 499.00, 'Ultimate value plan! Get a full year of premium access with maximum 58% savings. Only ₹499 for the entire year!', 0, 4, '2025-06-16 12:44:45', '2025-06-16 12:44:45');

-- --------------------------------------------------------

--
-- Table structure for table `time_spending_bookings`
--

CREATE TABLE `time_spending_bookings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `client_id` bigint(20) UNSIGNED NOT NULL,
  `provider_id` bigint(20) UNSIGNED NOT NULL,
  `booking_date` datetime NOT NULL,
  `duration_hours` decimal(4,2) NOT NULL DEFAULT 1.00,
  `actual_duration_hours` decimal(4,2) DEFAULT 1.00,
  `hourly_rate` decimal(8,2) NOT NULL,
  `platform_fee` decimal(8,2) NOT NULL DEFAULT 0.00,
  `base_amount` decimal(8,2) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `commission_percentage` decimal(5,2) NOT NULL DEFAULT 0.00,
  `commission_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `provider_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` varchar(255) NOT NULL DEFAULT 'pending',
  `provider_status` enum('pending','accepted','rejected') NOT NULL DEFAULT 'pending',
  `rejection_reason` text DEFAULT NULL,
  `provider_responded_at` timestamp NULL DEFAULT NULL,
  `chat_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `payment_status` varchar(255) NOT NULL DEFAULT 'pending',
  `refund_status` enum('pending','processed','failed') DEFAULT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  `refunded_at` timestamp NULL DEFAULT NULL,
  `escrow_status` varchar(255) NOT NULL DEFAULT 'pending',
  `escrow_held_at` timestamp NULL DEFAULT NULL,
  `escrow_released_at` timestamp NULL DEFAULT NULL,
  `auto_release_at` timestamp NULL DEFAULT NULL,
  `dispute_reason` text DEFAULT NULL,
  `disputed_at` timestamp NULL DEFAULT NULL,
  `payment_method` varchar(255) DEFAULT NULL,
  `wallet_amount_used` decimal(10,2) NOT NULL DEFAULT 0.00,
  `razorpay_amount_paid` decimal(10,2) NOT NULL DEFAULT 0.00,
  `razorpay_payment_id` varchar(255) DEFAULT NULL,
  `razorpay_order_id` varchar(255) DEFAULT NULL,
  `razorpay_signature` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `meeting_location` varchar(255) DEFAULT NULL,
  `client_location` varchar(255) DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `cancellation_reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `disputed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `dispute_type` enum('no_show','service_issue','payment_issue','other') DEFAULT NULL,
  `dispute_status` enum('pending','investigating','resolved','rejected') DEFAULT NULL,
  `dispute_evidence` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Photos, messages, or other evidence' CHECK (json_valid(`dispute_evidence`)),
  `admin_notes` text DEFAULT NULL COMMENT 'Admin notes for dispute resolution',
  `resolved_at` timestamp NULL DEFAULT NULL,
  `resolved_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `uploads`
--

CREATE TABLE `uploads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `path` varchar(255) NOT NULL,
  `mime_type` varchar(255) NOT NULL,
  `size` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'image',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0,
  `is_suspended` tinyint(1) DEFAULT 0,
  `role` enum('user','admin','super_admin','editor','accountant') NOT NULL DEFAULT 'user',
  `google_id` varchar(255) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `contact_number` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `interests` text DEFAULT NULL,
  `expectation` text DEFAULT NULL,
  `interested_in_sugar_partner` tinyint(1) NOT NULL DEFAULT 0,
  `sugar_partner_types` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`sugar_partner_types`)),
  `sugar_partner_bio` text DEFAULT NULL,
  `sugar_partner_expectations` text DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `fcm_token` varchar(255) DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `has_active_time_spending_subscription` tinyint(1) NOT NULL DEFAULT 0,
  `time_spending_subscription_expires_at` datetime DEFAULT NULL,
  `is_public_profile` tinyint(1) NOT NULL DEFAULT 0,
  `show_contact_number` tinyint(1) NOT NULL DEFAULT 0,
  `show_date_of_birth` tinyint(1) NOT NULL DEFAULT 1,
  `hide_dob_year` tinyint(1) NOT NULL DEFAULT 0,
  `show_interests_hobbies` tinyint(1) NOT NULL DEFAULT 1,
  `show_expectations` tinyint(1) NOT NULL DEFAULT 1,
  `show_gallery_images` tinyint(1) NOT NULL DEFAULT 1,
  `hourly_rate` decimal(8,2) DEFAULT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'INR',
  `is_time_spending_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `availability_schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`availability_schedule`)),
  `holiday_dates` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`holiday_dates`)),
  `service_location` varchar(255) DEFAULT NULL,
  `is_couple_activity_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `couple_activity_status` varchar(255) NOT NULL DEFAULT 'available'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `is_admin`, `is_suspended`, `role`, `google_id`, `email_verified_at`, `password`, `contact_number`, `gender`, `date_of_birth`, `interests`, `expectation`, `interested_in_sugar_partner`, `sugar_partner_types`, `sugar_partner_bio`, `sugar_partner_expectations`, `paid_at`, `fcm_token`, `profile_picture`, `remember_token`, `created_at`, `updated_at`, `has_active_time_spending_subscription`, `time_spending_subscription_expires_at`, `is_public_profile`, `show_contact_number`, `show_date_of_birth`, `hide_dob_year`, `show_interests_hobbies`, `show_expectations`, `show_gallery_images`, `hourly_rate`, `currency`, `is_time_spending_enabled`, `availability_schedule`, `holiday_dates`, `service_location`, `is_couple_activity_enabled`, `couple_activity_status`) VALUES
(11, 'Test Admin', '<EMAIL>', 1, 0, 'admin', NULL, NULL, '$2y$12$2RRt8kuu6MJCTm1GpRAj4uEw36/5kLcvcGkdeUoXc0YKB29Mtz62e', '1234567890', 'male', '1990-01-01', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-16 12:41:31', '2025-06-16 12:41:31', 0, NULL, 0, 0, 1, 0, 1, 1, 1, NULL, 'INR', 0, NULL, NULL, NULL, 0, 'available'),
(13, 'RekTech Admin', '<EMAIL>', 1, 0, 'super_admin', NULL, NULL, '$2y$12$fRWwSdDzbT9xwps.nuJEqesYF8BYDXVJoRG/BB4F886l9IOzmjQkW', '1234567890', 'male', '1990-01-01', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-16 12:44:14', '2025-06-16 12:44:14', 0, NULL, 0, 0, 1, 0, 1, 1, 1, NULL, 'INR', 0, NULL, NULL, NULL, 0, 'available'),
(14, 'Luckyrajsinh Jadeja', '<EMAIL>', 0, 1, 'user', '108620304514590752142', NULL, '$2y$12$q9A2mFf99nnGHYip5Crfi.IqZbstUb6NPe4ASdmwxD3FiVFyBUB6S', '**********', 'female', '2025-06-10', 'as, asd, asdf', 'asdsadas', 0, NULL, NULL, NULL, NULL, NULL, 'profile-pictures/fOCouUKjKUPdcAmT9IXMUi95d5yYxGQA8bBois0M.jpg', 'M2Xwjj0Qha2qZkKnjlWN5Zmv2USQqeKvMV6xLRpaKadSIQdBd4rZRBSMLHHx', '2025-06-17 04:25:26', '2025-06-17 05:11:01', 0, NULL, 0, 0, 1, 0, 1, 1, 0, NULL, 'INR', 0, NULL, NULL, NULL, 0, 'inactive');

-- --------------------------------------------------------

--
-- Table structure for table `user_bank_accounts`
--

CREATE TABLE `user_bank_accounts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `account_holder_name` varchar(255) NOT NULL,
  `account_number` varchar(255) DEFAULT NULL,
  `ifsc_code` varchar(255) DEFAULT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `account_type` enum('savings','current','gpay') DEFAULT 'savings',
  `payment_method_type` varchar(255) NOT NULL DEFAULT 'bank',
  `gpay_number` varchar(255) DEFAULT NULL,
  `gpay_upi` varchar(255) DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `razorpay_fund_account_id` varchar(255) DEFAULT NULL,
  `verification_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`verification_data`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_bank_accounts`
--

INSERT INTO `user_bank_accounts` (`id`, `user_id`, `account_holder_name`, `account_number`, `ifsc_code`, `bank_name`, `account_type`, `payment_method_type`, `gpay_number`, `gpay_upi`, `is_verified`, `is_primary`, `razorpay_fund_account_id`, `verification_data`, `created_at`, `updated_at`) VALUES
(1, 1, 'Kevin Manvar', '***************', 'BKID0003127', 'Bank of India', 'savings', 'bank', NULL, NULL, 1, 1, NULL, NULL, '2025-06-16 05:24:50', '2025-06-16 05:24:50'),
(2, 1, 'Kevin Manvar', NULL, NULL, NULL, 'gpay', 'gpay', '**********', 'kevinmanvar27-1@okaxis', 1, 0, NULL, NULL, '2025-06-16 06:32:44', '2025-06-16 06:32:44');

-- --------------------------------------------------------

--
-- Table structure for table `user_change_logs`
--

CREATE TABLE `user_change_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `field_name` varchar(255) NOT NULL,
  `old_value` text DEFAULT NULL,
  `new_value` text DEFAULT NULL,
  `action` varchar(255) NOT NULL DEFAULT 'update',
  `ip_address` varchar(255) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_change_logs`
--

INSERT INTO `user_change_logs` (`id`, `user_id`, `field_name`, `old_value`, `new_value`, `action`, `ip_address`, `user_agent`, `created_at`, `updated_at`) VALUES
(1, 8, 'contact_number', NULL, '9876543214', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:40:54', '2025-06-16 03:40:54'),
(2, 8, 'gender', NULL, 'male', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:40:54', '2025-06-16 03:40:54'),
(3, 8, 'date_of_birth', NULL, '2025-06-02T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:40:54', '2025-06-16 03:40:54'),
(4, 8, 'date_of_birth', '2025-06-03', '2025-06-02T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:41:07', '2025-06-16 03:41:07'),
(5, 8, 'interests', NULL, 'sd', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:41:07', '2025-06-16 03:41:07'),
(6, 8, 'expectation', NULL, 'sdsd', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:41:07', '2025-06-16 03:41:07'),
(7, 8, 'date_of_birth', '2025-06-03', '2025-06-02T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:41:19', '2025-06-16 03:41:19'),
(8, 8, 'profile_picture', NULL, 'new_image_uploaded', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:41:19', '2025-06-16 03:41:19'),
(9, 7, 'contact_number', NULL, '+91**********', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:11', '2025-06-16 03:43:11'),
(10, 7, 'gender', NULL, 'female', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:11', '2025-06-16 03:43:11'),
(11, 7, 'date_of_birth', NULL, '2025-06-02T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:11', '2025-06-16 03:43:11'),
(12, 7, 'interests', NULL, '00', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:11', '2025-06-16 03:43:11'),
(13, 7, 'expectation', NULL, 'oi', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:11', '2025-06-16 03:43:11'),
(14, 7, 'profile_picture', NULL, 'new_image_uploaded', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:11', '2025-06-16 03:43:11'),
(15, 8, 'date_of_birth', '2025-06-03', '2025-06-02T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:43:55', '2025-06-16 03:43:55'),
(16, 8, 'date_of_birth', '2025-06-03', '2025-06-02T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:44:51', '2025-06-16 03:44:51'),
(17, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:45:28', '2025-06-16 03:45:28'),
(18, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 03:45:34', '2025-06-16 03:45:34'),
(19, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 11:19:57', '2025-06-16 11:19:57'),
(20, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 11:23:33', '2025-06-16 11:23:33'),
(21, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 11:31:40', '2025-06-16 11:31:40'),
(22, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 11:31:47', '2025-06-16 11:31:47'),
(23, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 11:44:27', '2025-06-16 11:44:27'),
(24, 1, 'date_of_birth', '2007-05-04', '2007-05-03T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-16 11:44:33', '2025-06-16 11:44:33'),
(25, 14, 'contact_number', NULL, '**********', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:10:51', '2025-06-17 05:10:51'),
(26, 14, 'gender', NULL, 'female', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:10:51', '2025-06-17 05:10:51'),
(27, 14, 'date_of_birth', NULL, '2025-06-09T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:10:51', '2025-06-17 05:10:51'),
(28, 14, 'interests', NULL, 'as, asd, asdf', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:10:51', '2025-06-17 05:10:51'),
(29, 14, 'expectation', NULL, 'asdsadas', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:10:51', '2025-06-17 05:10:51'),
(30, 14, 'profile_picture', NULL, 'new_image_uploaded', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:10:51', '2025-06-17 05:10:51'),
(31, 14, 'date_of_birth', '2025-06-10', '2025-06-09T18:30:00.000000Z', 'update', '127.0.0.1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-17 05:11:01', '2025-06-17 05:11:01');

-- --------------------------------------------------------

--
-- Table structure for table `user_gallery`
--

CREATE TABLE `user_gallery` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_subscriptions`
--

CREATE TABLE `user_subscriptions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `subscription_plan_id` bigint(20) UNSIGNED NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `wallet_amount_used` decimal(10,2) NOT NULL DEFAULT 0.00,
  `razorpay_amount_paid` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` varchar(255) DEFAULT NULL,
  `razorpay_payment_id` varchar(255) DEFAULT NULL,
  `razorpay_order_id` varchar(255) DEFAULT NULL,
  `razorpay_signature` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'active',
  `starts_at` datetime NOT NULL,
  `expires_at` datetime NOT NULL,
  `paid_at` datetime DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_wallets`
--

CREATE TABLE `user_wallets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `balance` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_earned` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_withdrawn` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_wallets`
--

INSERT INTO `user_wallets` (`id`, `user_id`, `balance`, `total_earned`, `total_withdrawn`, `created_at`, `updated_at`) VALUES
(5, 14, 0.00, 0.00, 0.00, '2025-06-17 04:25:26', '2025-06-17 04:25:26'),
(6, 13, 0.00, 0.00, 0.00, '2025-06-17 04:36:45', '2025-06-17 04:36:45');

-- --------------------------------------------------------

--
-- Table structure for table `wallet_transactions`
--

CREATE TABLE `wallet_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `commission_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL,
  `description` varchar(255) NOT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `wallet_transactions`
--

INSERT INTO `wallet_transactions` (`id`, `user_id`, `booking_id`, `type`, `amount`, `commission_amount`, `final_amount`, `description`, `metadata`, `created_at`, `updated_at`) VALUES
(1, 8, 2, 'credit', 50.00, 5.00, 45.00, 'Earnings from booking with Rek Tech', '{\"commission_percentage\":\"10.00\",\"gross_amount\":\"50.00\",\"commission_amount\":5,\"net_amount\":45}', '2025-06-16 04:13:50', '2025-06-16 04:13:50'),
(2, 1, 1, 'credit', 50.00, 0.00, 50.00, 'Auto-refund for booking #1 - Provider did not respond before meeting time', '{\"refund_type\":\"auto_cancellation\",\"original_booking_id\":1,\"refund_reason\":\"Provider did not respond before meeting time\",\"refund_amount\":\"50.00\",\"auto_processed\":true}', '2025-06-16 04:26:46', '2025-06-16 04:26:46'),
(9, 1, NULL, 'debit', 10.00, 0.00, 10.00, 'Withdrawal request to G-Pay - ******9565', '{\"withdrawal_request\":true}', '2025-06-16 08:31:30', '2025-06-16 08:31:30'),
(10, 1, NULL, 'debit', 10.00, 0.00, 10.00, 'Withdrawal processed - Request #2', '{\"withdrawal_processed\":true,\"transaction_reference\":\"Pass\"}', '2025-06-16 10:11:38', '2025-06-16 10:11:38'),
(11, 1, 8, 'debit', 30.00, 0.00, 0.00, 'Payment for booking #8 - Time spending with Kevin Manvar', NULL, '2025-06-16 10:58:26', '2025-06-16 10:58:26');

-- --------------------------------------------------------

--
-- Table structure for table `withdrawal_requests`
--

CREATE TABLE `withdrawal_requests` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `bank_account_id` bigint(20) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `processing_fee` decimal(8,2) NOT NULL DEFAULT 0.00,
  `net_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `razorpay_payout_id` varchar(255) DEFAULT NULL,
  `transaction_reference` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `failure_reason` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL,
  `processed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `withdrawal_requests`
--

INSERT INTO `withdrawal_requests` (`id`, `user_id`, `bank_account_id`, `amount`, `processing_fee`, `net_amount`, `status`, `razorpay_payout_id`, `transaction_reference`, `notes`, `failure_reason`, `admin_notes`, `requested_at`, `processed_at`, `processed_by`, `created_at`, `updated_at`) VALUES
(1, 1, 2, 10.00, 0.00, 10.00, 'failed', NULL, NULL, 'Its urgent', 'Reject from admin', 'note from admin', '2025-06-16 09:53:18', '2025-06-16 09:53:18', 1, '2025-06-16 08:31:30', '2025-06-16 09:53:18'),
(2, 1, 1, 10.00, 0.00, 10.00, 'completed', NULL, 'Pass', 'Urgent', NULL, 'Done', '2025-06-16 10:11:38', '2025-06-16 10:11:38', 1, '2025-06-16 09:22:00', '2025-06-16 10:11:38'),
(3, 1, 2, 5.00, 0.00, 5.00, 'failed', NULL, NULL, NULL, 'Fir kabhi le lena', 'Abhi paise nahi hai', '2025-06-16 10:06:12', '2025-06-16 10:06:12', 1, '2025-06-16 10:02:52', '2025-06-16 10:06:12');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_revenues`
--
ALTER TABLE `admin_revenues`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_revenues_booking_id_index` (`booking_id`),
  ADD KEY `admin_revenues_revenue_type_index` (`revenue_type`),
  ADD KEY `admin_revenues_created_at_index` (`created_at`),
  ADD KEY `admin_revenues_subscription_id_index` (`subscription_id`);

--
-- Indexes for table `blocked_users`
--
ALTER TABLE `blocked_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `blocked_users_blocker_id_blocked_id_unique` (`blocker_id`,`blocked_id`),
  ADD KEY `blocked_users_booking_id_foreign` (`booking_id`),
  ADD KEY `blocked_users_blocker_id_blocked_id_index` (`blocker_id`,`blocked_id`),
  ADD KEY `blocked_users_blocked_id_index` (`blocked_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `chat_messages_booking_id_created_at_index` (`booking_id`,`created_at`),
  ADD KEY `chat_messages_sender_id_receiver_id_index` (`sender_id`,`receiver_id`),
  ADD KEY `chat_sender_receiver_idx` (`sender_id`,`receiver_id`),
  ADD KEY `chat_receiver_read_idx` (`receiver_id`,`is_read`),
  ADD KEY `chat_created_at_idx` (`created_at`);

--
-- Indexes for table `contact_submissions`
--
ALTER TABLE `contact_submissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `contact_submissions_assigned_to_foreign` (`assigned_to`),
  ADD KEY `contact_submissions_status_created_at_index` (`status`,`created_at`),
  ADD KEY `contact_submissions_priority_created_at_index` (`priority`,`created_at`),
  ADD KEY `contact_submissions_email_index` (`email`);

--
-- Indexes for table `couple_activity_requests`
--
ALTER TABLE `couple_activity_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `couple_activity_requests_requester_id_partner_id_type_unique` (`requester_id`,`partner_id`,`type`),
  ADD KEY `couple_activity_requests_partner_id_foreign` (`partner_id`);

--
-- Indexes for table `event_payments`
--
ALTER TABLE `event_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `event_payments_user_id_meeting_address_id_unique` (`user_id`,`meeting_address_id`),
  ADD KEY `event_payments_user_status_idx` (`user_id`,`status`),
  ADD KEY `event_payments_meeting_status_idx` (`meeting_address_id`,`status`),
  ADD KEY `event_payments_created_at_idx` (`created_at`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `features`
--
ALTER TABLE `features`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `features_name_unique` (`name`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `meeting_addresses`
--
ALTER TABLE `meeting_addresses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `meeting_verifications`
--
ALTER TABLE `meeting_verifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `meeting_verifications_booking_id_index` (`booking_id`),
  ADD KEY `meeting_verifications_is_verified_index` (`is_verified`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_user_id_foreign` (`user_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `ratings_reviews`
--
ALTER TABLE `ratings_reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_booking_reviewer` (`booking_id`,`reviewer_id`),
  ADD KEY `ratings_reviews_reviewer_id_foreign` (`reviewer_id`),
  ADD KEY `ratings_reviews_reviewee_id_is_approved_index` (`reviewee_id`,`is_approved`),
  ADD KEY `ratings_reviews_booking_id_index` (`booking_id`),
  ADD KEY `ratings_reviews_rating_index` (`rating`),
  ADD KEY `ratings_reviews_created_at_index` (`created_at`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_key_unique` (`key`),
  ADD KEY `settings_group_idx` (`group`),
  ADD KEY `settings_type_idx` (`type`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subscription_plans_is_active_sort_order_index` (`is_active`,`sort_order`);

--
-- Indexes for table `time_spending_bookings`
--
ALTER TABLE `time_spending_bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `time_spending_bookings_provider_id_booking_date_index` (`provider_id`,`booking_date`),
  ADD KEY `time_spending_bookings_client_id_booking_date_index` (`client_id`,`booking_date`),
  ADD KEY `time_spending_bookings_booking_date_status_index` (`booking_date`,`status`),
  ADD KEY `time_spending_bookings_disputed_by_foreign` (`disputed_by`),
  ADD KEY `escrow_auto_release_idx` (`escrow_status`,`auto_release_at`),
  ADD KEY `booking_payment_status_idx` (`status`,`payment_status`,`created_at`),
  ADD KEY `bookings_client_status_idx` (`client_id`,`status`),
  ADD KEY `bookings_provider_status_idx` (`provider_id`,`status`),
  ADD KEY `bookings_date_status_idx` (`booking_date`,`status`),
  ADD KEY `bookings_payment_status_idx` (`payment_status`),
  ADD KEY `bookings_created_at_idx` (`created_at`),
  ADD KEY `time_spending_bookings_resolved_by_foreign` (`resolved_by`),
  ADD KEY `dispute_status_idx` (`dispute_status`,`disputed_at`),
  ADD KEY `dispute_type_status_idx` (`dispute_type`,`dispute_status`);

--
-- Indexes for table `uploads`
--
ALTER TABLE `uploads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uploads_user_id_foreign` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD UNIQUE KEY `users_google_id_unique` (`google_id`),
  ADD KEY `users_time_spending_public_idx` (`is_time_spending_enabled`,`is_public_profile`),
  ADD KEY `users_gender_time_spending_idx` (`gender`,`is_time_spending_enabled`),
  ADD KEY `users_created_at_idx` (`created_at`),
  ADD KEY `users_email_verified_idx` (`email_verified_at`);

--
-- Indexes for table `user_bank_accounts`
--
ALTER TABLE `user_bank_accounts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_bank_accounts_user_id_is_primary_index` (`user_id`,`is_primary`),
  ADD KEY `user_bank_accounts_is_verified_index` (`is_verified`);

--
-- Indexes for table `user_change_logs`
--
ALTER TABLE `user_change_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_change_logs_user_id_created_at_index` (`user_id`,`created_at`),
  ADD KEY `user_change_logs_field_name_index` (`field_name`);

--
-- Indexes for table `user_gallery`
--
ALTER TABLE `user_gallery`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_gallery_user_id_sort_order_index` (`user_id`,`sort_order`);

--
-- Indexes for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_subscriptions_subscription_plan_id_foreign` (`subscription_plan_id`),
  ADD KEY `user_subscriptions_user_id_status_index` (`user_id`,`status`),
  ADD KEY `user_subscriptions_user_id_expires_at_index` (`user_id`,`expires_at`),
  ADD KEY `user_subscriptions_expires_at_status_index` (`expires_at`,`status`);

--
-- Indexes for table `user_wallets`
--
ALTER TABLE `user_wallets`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_wallets_user_id_unique` (`user_id`);

--
-- Indexes for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wallet_transactions_booking_id_foreign` (`booking_id`),
  ADD KEY `wallet_transactions_user_id_type_index` (`user_id`,`type`),
  ADD KEY `wallet_transactions_user_id_created_at_index` (`user_id`,`created_at`),
  ADD KEY `wallet_user_created_idx` (`user_id`,`created_at`),
  ADD KEY `wallet_type_created_idx` (`type`,`created_at`);

--
-- Indexes for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `withdrawal_requests_bank_account_id_foreign` (`bank_account_id`),
  ADD KEY `withdrawal_requests_processed_by_foreign` (`processed_by`),
  ADD KEY `withdrawal_requests_user_id_status_index` (`user_id`,`status`),
  ADD KEY `withdrawal_requests_status_requested_at_index` (`status`,`requested_at`),
  ADD KEY `withdrawal_requests_razorpay_payout_id_index` (`razorpay_payout_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_revenues`
--
ALTER TABLE `admin_revenues`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `blocked_users`
--
ALTER TABLE `blocked_users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_messages`
--
ALTER TABLE `chat_messages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `contact_submissions`
--
ALTER TABLE `contact_submissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `couple_activity_requests`
--
ALTER TABLE `couple_activity_requests`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_payments`
--
ALTER TABLE `event_payments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `features`
--
ALTER TABLE `features`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=86;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `meeting_addresses`
--
ALTER TABLE `meeting_addresses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `meeting_verifications`
--
ALTER TABLE `meeting_verifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=73;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `ratings_reviews`
--
ALTER TABLE `ratings_reviews`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=199;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

--
-- AUTO_INCREMENT for table `time_spending_bookings`
--
ALTER TABLE `time_spending_bookings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `uploads`
--
ALTER TABLE `uploads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `user_bank_accounts`
--
ALTER TABLE `user_bank_accounts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user_change_logs`
--
ALTER TABLE `user_change_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `user_gallery`
--
ALTER TABLE `user_gallery`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user_wallets`
--
ALTER TABLE `user_wallets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_revenues`
--
ALTER TABLE `admin_revenues`
  ADD CONSTRAINT `admin_revenues_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `time_spending_bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_revenues_subscription_id_foreign` FOREIGN KEY (`subscription_id`) REFERENCES `user_subscriptions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blocked_users`
--
ALTER TABLE `blocked_users`
  ADD CONSTRAINT `blocked_users_blocked_id_foreign` FOREIGN KEY (`blocked_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `blocked_users_blocker_id_foreign` FOREIGN KEY (`blocker_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `blocked_users_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `time_spending_bookings` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD CONSTRAINT `chat_messages_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `time_spending_bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chat_messages_receiver_id_foreign` FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `chat_messages_sender_id_foreign` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `contact_submissions`
--
ALTER TABLE `contact_submissions`
  ADD CONSTRAINT `contact_submissions_assigned_to_foreign` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `couple_activity_requests`
--
ALTER TABLE `couple_activity_requests`
  ADD CONSTRAINT `couple_activity_requests_partner_id_foreign` FOREIGN KEY (`partner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `couple_activity_requests_requester_id_foreign` FOREIGN KEY (`requester_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `event_payments`
--
ALTER TABLE `event_payments`
  ADD CONSTRAINT `event_payments_meeting_address_id_foreign` FOREIGN KEY (`meeting_address_id`) REFERENCES `meeting_addresses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `event_payments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `meeting_verifications`
--
ALTER TABLE `meeting_verifications`
  ADD CONSTRAINT `meeting_verifications_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `time_spending_bookings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `ratings_reviews`
--
ALTER TABLE `ratings_reviews`
  ADD CONSTRAINT `ratings_reviews_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `time_spending_bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `ratings_reviews_reviewee_id_foreign` FOREIGN KEY (`reviewee_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `ratings_reviews_reviewer_id_foreign` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `time_spending_bookings`
--
ALTER TABLE `time_spending_bookings`
  ADD CONSTRAINT `time_spending_bookings_client_id_foreign` FOREIGN KEY (`client_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `time_spending_bookings_disputed_by_foreign` FOREIGN KEY (`disputed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `time_spending_bookings_provider_id_foreign` FOREIGN KEY (`provider_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `time_spending_bookings_resolved_by_foreign` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `uploads`
--
ALTER TABLE `uploads`
  ADD CONSTRAINT `uploads_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_bank_accounts`
--
ALTER TABLE `user_bank_accounts`
  ADD CONSTRAINT `user_bank_accounts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_change_logs`
--
ALTER TABLE `user_change_logs`
  ADD CONSTRAINT `user_change_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_gallery`
--
ALTER TABLE `user_gallery`
  ADD CONSTRAINT `user_gallery_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  ADD CONSTRAINT `user_subscriptions_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_subscriptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_wallets`
--
ALTER TABLE `user_wallets`
  ADD CONSTRAINT `user_wallets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  ADD CONSTRAINT `wallet_transactions_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `time_spending_bookings` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `wallet_transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  ADD CONSTRAINT `withdrawal_requests_bank_account_id_foreign` FOREIGN KEY (`bank_account_id`) REFERENCES `user_bank_accounts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `withdrawal_requests_processed_by_foreign` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `withdrawal_requests_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
